import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { analyticsService } from '../services/analyticsService';
import { useDecks } from '../hooks/useDecks';
import {
  BookOpen,
  Plus,
  TrendingUp,
  Clock,
  Target,
  Zap,
  Loader2
} from 'lucide-react';
import type { AnalyticsData } from '../types';

export const DashboardPage: React.FC = () => {
  const { user } = useAuth();
  const { decks } = useDecks();
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadAnalytics();
  }, []);

  const loadAnalytics = async () => {
    try {
      setIsLoading(true);
      const data = await analyticsService.getDashboardAnalytics();
      setAnalytics(data);
    } catch (error) {
      console.error('Failed to load analytics:', error);
      // Use fallback data if API fails
      setAnalytics({
        total_decks: decks.length,
        total_flashcards: decks.reduce((sum, deck) => sum + deck.flashcard_count, 0),
        cards_due_today: decks.reduce((sum, deck) => sum + deck.due_count, 0),
        study_streak: 0,
        total_study_time: 0,
        average_accuracy: 0,
        recent_sessions: [],
        performance_chart: { labels: [], datasets: [] },
        deck_progress: [],
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="max-w-7xl mx-auto">
        <div className="flex items-center justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-primary-600" />
          <span className="ml-2 text-gray-600">Loading dashboard...</span>
        </div>
      </div>
    );
  }

  const stats = {
    totalDecks: analytics?.total_decks || 0,
    totalCards: analytics?.total_flashcards || 0,
    cardsDue: analytics?.cards_due_today || 0,
    studyStreak: analytics?.study_streak || 0,
    studyTime: Math.round((analytics?.total_study_time || 0) / 60), // Convert to minutes
    accuracy: analytics?.average_accuracy || 0
  };

  // Get recent decks (first 3)
  const recentDecks = decks.slice(0, 3);

  return (
    <div className="max-w-7xl mx-auto">
      {/* Welcome Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">
          Welcome back, {user?.username}! 👋
        </h1>
        <p className="text-gray-600 mt-2">
          Ready to continue your learning journey?
        </p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        <div className="card">
          <div className="flex items-center">
            <div className="p-2 bg-primary-100 rounded-lg">
              <BookOpen className="h-6 w-6 text-primary-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Decks</p>
              <p className="text-2xl font-bold text-gray-900">{stats.totalDecks}</p>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="flex items-center">
            <div className="p-2 bg-success-100 rounded-lg">
              <Target className="h-6 w-6 text-success-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Cards</p>
              <p className="text-2xl font-bold text-gray-900">{stats.totalCards}</p>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="flex items-center">
            <div className="p-2 bg-warning-100 rounded-lg">
              <Clock className="h-6 w-6 text-warning-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Cards Due</p>
              <p className="text-2xl font-bold text-gray-900">{stats.cardsDue}</p>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="flex items-center">
            <div className="p-2 bg-error-100 rounded-lg">
              <Zap className="h-6 w-6 text-error-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Study Streak</p>
              <p className="text-2xl font-bold text-gray-900">{stats.studyStreak} days</p>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="flex items-center">
            <div className="p-2 bg-purple-100 rounded-lg">
              <Clock className="h-6 w-6 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Study Time</p>
              <p className="text-2xl font-bold text-gray-900">{stats.studyTime}m</p>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="flex items-center">
            <div className="p-2 bg-indigo-100 rounded-lg">
              <TrendingUp className="h-6 w-6 text-indigo-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Accuracy</p>
              <p className="text-2xl font-bold text-gray-900">{stats.accuracy}%</p>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        {/* Study Now */}
        <div className="card">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Study Now</h2>
          {stats.cardsDue > 0 ? (
            <div>
              <p className="text-gray-600 mb-4">
                You have {stats.cardsDue} cards ready for review. Keep up the momentum!
              </p>
              <Link
                to="/decks"
                className="btn-primary inline-flex items-center space-x-2"
              >
                <Clock className="h-4 w-4" />
                <span>Start Studying</span>
              </Link>
            </div>
          ) : (
            <div>
              <p className="text-gray-600 mb-4">
                Great job! No cards are due for review right now. Check back later or create new content.
              </p>
              <Link
                to="/decks/create"
                className="btn-secondary inline-flex items-center space-x-2"
              >
                <Plus className="h-4 w-4" />
                <span>Create New Deck</span>
              </Link>
            </div>
          )}
        </div>

        {/* Quick Create */}
        <div className="card">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Quick Create</h2>
          <p className="text-gray-600 mb-4">
            Start learning something new by creating a deck or generating flashcards from text.
          </p>
          <div className="space-y-2">
            <Link
              to="/decks/create"
              className="btn-primary w-full inline-flex items-center justify-center space-x-2"
            >
              <Plus className="h-4 w-4" />
              <span>Create New Deck</span>
            </Link>
            <button className="btn-secondary w-full inline-flex items-center justify-center space-x-2">
              <Zap className="h-4 w-4" />
              <span>Generate from Text</span>
            </button>
          </div>
        </div>
      </div>

      {/* Recent Decks */}
      <div className="card">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-gray-900">Recent Decks</h2>
          <Link
            to="/decks"
            className="text-primary-600 hover:text-primary-700 text-sm font-medium"
          >
            View all decks →
          </Link>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {recentDecks.map((deck) => (
            <div
              key={deck.id}
              className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer"
            >
              <div className="flex items-center mb-3">
                <div
                  className="w-4 h-4 rounded-full mr-3"
                  style={{ backgroundColor: deck.color }}
                ></div>
                <h3 className="font-medium text-gray-900 truncate">{deck.name}</h3>
              </div>

              <div className="flex justify-between text-sm text-gray-600 mb-3">
                <span>{deck.flashcard_count} cards</span>
                <span className="text-warning-600 font-medium">
                  {deck.due_count} due
                </span>
              </div>

              <Link
                to={`/study/${deck.id}`}
                className="btn-primary text-sm w-full"
              >
                Study Now
              </Link>
            </div>
          ))}
        </div>

        {recentDecks.length === 0 && (
          <div className="text-center py-8">
            <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600 mb-4">No decks yet. Create your first deck to get started!</p>
            <Link to="/decks/create" className="btn-primary">
              Create Your First Deck
            </Link>
          </div>
        )}
      </div>
    </div>
  );
};
