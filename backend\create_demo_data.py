#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create demo data for the flashcard application.
"""

import os
import sys
from datetime import datetime, timedelta

# Add the backend directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models.user import User
from app.models.deck import Deck
from app.models.flashcard import Flashcard
from app.models.study_session import StudySession

def create_demo_data():
    """Create demo user and sample flashcards."""
    
    app = create_app()
    
    with app.app_context():
        # Create tables
        db.create_all()
        
        # Check if demo user already exists
        demo_user = User.query.filter_by(username='demo').first()
        if demo_user:
            print("Demo user already exists. Skipping creation.")
            return
        
        # Create demo user
        demo_user = User(
            username='demo',
            email='<EMAIL>',
            password='demo123'
        )
        db.session.add(demo_user)
        db.session.commit()
        
        print(f"Created demo user: {demo_user.username}")
        
        # Create sample decks
        decks_data = [
            {
                'name': 'Spanish Vocabulary',
                'description': 'Essential Spanish words and phrases for beginners',
                'color': '#3B82F6',
                'tags': ['Spanish', 'Language', 'Beginner'],
                'flashcards': [
                    {'front': 'Hello', 'back': 'Hola'},
                    {'front': 'Goodbye', 'back': 'Adiós'},
                    {'front': 'Please', 'back': 'Por favor'},
                    {'front': 'Thank you', 'back': 'Gracias'},
                    {'front': 'Yes', 'back': 'Sí'},
                    {'front': 'No', 'back': 'No'},
                    {'front': 'Water', 'back': 'Agua'},
                    {'front': 'Food', 'back': 'Comida'},
                    {'front': 'House', 'back': 'Casa'},
                    {'front': 'Car', 'back': 'Coche'},
                    {'front': 'Book', 'back': 'Libro'},
                    {'front': 'School', 'back': 'Escuela'},
                    {'front': 'Friend', 'back': 'Amigo'},
                    {'front': 'Family', 'back': 'Familia'},
                    {'front': 'Time', 'back': 'Tiempo'},
                ]
            },
            {
                'name': 'History Facts',
                'description': 'Important historical events and dates',
                'color': '#10B981',
                'tags': ['History', 'Facts'],
                'flashcards': [
                    {'front': 'When did World War II end?', 'back': '1945'},
                    {'front': 'Who was the first President of the United States?', 'back': 'George Washington'},
                    {'front': 'In what year did the Berlin Wall fall?', 'back': '1989'},
                    {'front': 'When did the American Civil War begin?', 'back': '1861'},
                    {'front': 'Who wrote the Declaration of Independence?', 'back': 'Thomas Jefferson'},
                    {'front': 'When did the French Revolution begin?', 'back': '1789'},
                    {'front': 'What year did Columbus reach the Americas?', 'back': '1492'},
                    {'front': 'When was the Magna Carta signed?', 'back': '1215'},
                    {'front': 'Who was the first person to walk on the moon?', 'back': 'Neil Armstrong'},
                    {'front': 'When did the Roman Empire fall?', 'back': '476 AD'},
                ]
            },
            {
                'name': 'Programming Concepts',
                'description': 'Core programming principles and algorithms',
                'color': '#8B5CF6',
                'tags': ['Programming', 'Computer Science'],
                'flashcards': [
                    {'front': 'What is a variable?', 'back': 'A storage location with an associated name that contains data'},
                    {'front': 'What is a function?', 'back': 'A reusable block of code that performs a specific task'},
                    {'front': 'What is recursion?', 'back': 'A programming technique where a function calls itself'},
                    {'front': 'What is Big O notation?', 'back': 'A mathematical notation to describe algorithm complexity'},
                    {'front': 'What is a loop?', 'back': 'A control structure that repeats a block of code'},
                    {'front': 'What is an array?', 'back': 'A data structure that stores multiple elements of the same type'},
                    {'front': 'What is object-oriented programming?', 'back': 'A programming paradigm based on objects and classes'},
                    {'front': 'What is a database?', 'back': 'An organized collection of structured information'},
                    {'front': 'What is an API?', 'back': 'Application Programming Interface - a set of protocols for building software'},
                    {'front': 'What is version control?', 'back': 'A system that tracks changes to files over time'},
                ]
            }
        ]
        
        for deck_data in decks_data:
            # Create deck
            deck = Deck(
                name=deck_data['name'],
                user_id=demo_user.id,
                description=deck_data['description'],
                color=deck_data['color']
            )
            deck.set_tags(deck_data['tags'])
            db.session.add(deck)
            db.session.flush()  # Get the deck ID
            
            print(f"Created deck: {deck.name}")
            
            # Create flashcards
            for i, card_data in enumerate(deck_data['flashcards']):
                flashcard = Flashcard(
                    front=card_data['front'],
                    back=card_data['back'],
                    deck_id=deck.id,
                    difficulty='medium'
                )
                
                # Simulate some study history for variety
                if i < 5:  # First 5 cards have been studied
                    flashcard.times_reviewed = 2
                    flashcard.times_correct = 1
                    flashcard.repetitions = 1
                    flashcard.ease_factor = 2.4
                    flashcard.interval = 3
                    flashcard.next_review = datetime.utcnow() - timedelta(days=1)  # Due yesterday
                    flashcard.last_reviewed = datetime.utcnow() - timedelta(days=4)
                elif i < 8:  # Next 3 cards are new but due for first review
                    flashcard.next_review = datetime.utcnow() - timedelta(hours=1)
                
                db.session.add(flashcard)
            
            print(f"Created {len(deck_data['flashcards'])} flashcards for {deck.name}")
        
        # Create some sample study sessions
        sessions_data = [
            {
                'deck_id': 1,
                'started_at': datetime.utcnow() - timedelta(days=1),
                'ended_at': datetime.utcnow() - timedelta(days=1) + timedelta(minutes=15),
                'cards_studied': 10,
                'cards_correct': 8,
                'session_type': 'review'
            },
            {
                'deck_id': 2,
                'started_at': datetime.utcnow() - timedelta(days=2),
                'ended_at': datetime.utcnow() - timedelta(days=2) + timedelta(minutes=12),
                'cards_studied': 8,
                'cards_correct': 6,
                'session_type': 'new'
            },
            {
                'deck_id': 3,
                'started_at': datetime.utcnow() - timedelta(days=3),
                'ended_at': datetime.utcnow() - timedelta(days=3) + timedelta(minutes=20),
                'cards_studied': 12,
                'cards_correct': 10,
                'session_type': 'mixed'
            }
        ]
        
        for session_data in sessions_data:
            session = StudySession(
                user_id=demo_user.id,
                deck_id=session_data['deck_id'],
                session_type=session_data['session_type']
            )
            session.started_at = session_data['started_at']
            session.ended_at = session_data['ended_at']
            session.cards_studied = session_data['cards_studied']
            session.cards_correct = session_data['cards_correct']
            
            # Calculate duration
            duration = session_data['ended_at'] - session_data['started_at']
            session.total_time_seconds = int(duration.total_seconds())
            
            db.session.add(session)
        
        db.session.commit()
        print(f"Created {len(sessions_data)} sample study sessions")
        
        print("\nDemo data created successfully!")
        print("Demo credentials:")
        print("Username: demo")
        print("Password: demo123")

if __name__ == '__main__':
    create_demo_data()
