import React, { useState, useRef } from 'react';
import { Link } from 'react-router-dom';
import { BookOpen, Plus, Play, Edit, Trash2, Loader2, Download, Upload } from 'lucide-react';
import { useDecks } from '../hooks/useDecks';
import { importExportService } from '../services/importExportService';
import toast from 'react-hot-toast';

export const DecksPage: React.FC = () => {
  const { decks, isLoading, deleteDeck, fetchDecks } = useDecks();
  const [deletingDeckId, setDeletingDeckId] = useState<number | null>(null);
  const [isExporting, setIsExporting] = useState(false);
  const [isImporting, setIsImporting] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleDeleteDeck = async (deckId: number, deckName: string) => {
    if (!confirm(`Are you sure you want to delete "${deckName}"? This action cannot be undone.`)) {
      return;
    }

    setDeletingDeckId(deckId);
    try {
      await deleteDeck(deckId);
    } catch (error) {
      // Error is handled in the hook
    } finally {
      setDeletingDeckId(null);
    }
  };

  const handleExportJSON = async () => {
    setIsExporting(true);
    try {
      const blob = await importExportService.exportToJSON();
      importExportService.downloadBlob(blob, 'flashcards_export.json');
      toast.success('Decks exported successfully!');
    } catch (error: any) {
      const message = error.response?.data?.error || 'Export failed';
      toast.error(message);
    } finally {
      setIsExporting(false);
    }
  };

  const handleExportCSV = async () => {
    setIsExporting(true);
    try {
      const blob = await importExportService.exportToCSV();
      importExportService.downloadBlob(blob, 'flashcards_export.csv');
      toast.success('Flashcards exported to CSV successfully!');
    } catch (error: any) {
      const message = error.response?.data?.error || 'Export failed';
      toast.error(message);
    } finally {
      setIsExporting(false);
    }
  };

  const handleImportFile = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setIsImporting(true);
    try {
      if (file.name.endsWith('.json')) {
        const result = await importExportService.importFromJSON(file);
        toast.success(`Imported ${result.imported_decks} decks with ${result.imported_cards} cards!`);
      } else if (file.name.endsWith('.csv')) {
        const result = await importExportService.importFromCSV(file);
        toast.success(`Imported ${result.imported_cards} cards to "${result.deck_name}"!`);
      } else {
        toast.error('Unsupported file format. Please use JSON or CSV files.');
        return;
      }

      // Refresh decks list
      await fetchDecks();
    } catch (error: any) {
      const message = error.response?.data?.error || 'Import failed';
      toast.error(message);
    } finally {
      setIsImporting(false);
      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  if (isLoading) {
    return (
      <div className="max-w-7xl mx-auto">
        <div className="flex items-center justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-primary-600" />
          <span className="ml-2 text-gray-600">Loading decks...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">My Decks</h1>
          <p className="text-gray-600 mt-2">
            Manage your flashcard collections and track your progress
          </p>
        </div>

        <div className="flex items-center space-x-3">
          {/* Import/Export Dropdown */}
          <div className="relative">
            <button
              className="btn-secondary inline-flex items-center space-x-2"
              onClick={() => fileInputRef.current?.click()}
              disabled={isImporting}
            >
              {isImporting ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Upload className="h-4 w-4" />
              )}
              <span>Import</span>
            </button>

            <input
              ref={fileInputRef}
              type="file"
              accept=".json,.csv"
              onChange={handleImportFile}
              className="hidden"
            />
          </div>

          <div className="flex items-center space-x-1">
            <button
              onClick={handleExportJSON}
              disabled={isExporting || decks.length === 0}
              className="btn-secondary inline-flex items-center space-x-2"
            >
              {isExporting ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Download className="h-4 w-4" />
              )}
              <span>JSON</span>
            </button>

            <button
              onClick={handleExportCSV}
              disabled={isExporting || decks.length === 0}
              className="btn-secondary inline-flex items-center space-x-2"
            >
              {isExporting ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Download className="h-4 w-4" />
              )}
              <span>CSV</span>
            </button>
          </div>

          <Link
            to="/decks/create"
            className="btn-primary inline-flex items-center space-x-2"
          >
            <Plus className="h-4 w-4" />
            <span>Create New Deck</span>
          </Link>
        </div>
      </div>

      {/* Decks Grid */}
      {decks.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {decks.map((deck) => (
            <div key={deck.id} className="card hover:shadow-lg transition-shadow">
              {/* Deck Header */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center">
                  <div
                    className="w-4 h-4 rounded-full mr-3 flex-shrink-0"
                    style={{ backgroundColor: deck.color }}
                  ></div>
                  <div className="min-w-0">
                    <h3 className="font-semibold text-gray-900 truncate">
                      {deck.name}
                    </h3>
                    <p className="text-sm text-gray-600 mt-1 line-clamp-2">
                      {deck.description}
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-1 ml-2">
                  <button className="p-1 text-gray-400 hover:text-gray-600 rounded">
                    <Edit className="h-4 w-4" />
                  </button>
                  <button
                    className="p-1 text-gray-400 hover:text-red-600 rounded disabled:opacity-50"
                    onClick={() => handleDeleteDeck(deck.id, deck.name)}
                    disabled={deletingDeckId === deck.id}
                  >
                    {deletingDeckId === deck.id ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <Trash2 className="h-4 w-4" />
                    )}
                  </button>
                </div>
              </div>

              {/* Stats */}
              <div className="flex justify-between text-sm text-gray-600 mb-4">
                <span>{deck.flashcard_count} cards</span>
                <div className="flex space-x-3">
                  {deck.due_count > 0 && (
                    <span className="text-warning-600 font-medium">
                      {deck.due_count} due
                    </span>
                  )}
                  {deck.new_count > 0 && (
                    <span className="text-primary-600 font-medium">
                      {deck.new_count} new
                    </span>
                  )}
                </div>
              </div>

              {/* Tags */}
              {deck.tags && deck.tags.length > 0 && (
                <div className="flex flex-wrap gap-1 mb-4">
                  {deck.tags.map((tag, index) => (
                    <span
                      key={index}
                      className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
              )}

              {/* Actions */}
              <div className="flex space-x-2">
                <Link
                  to={`/study/${deck.id}`}
                  className="btn-primary flex-1 text-sm inline-flex items-center justify-center space-x-1"
                >
                  <Play className="h-4 w-4" />
                  <span>Study</span>
                </Link>
                <Link
                  to={`/decks/${deck.id}`}
                  className="btn-secondary flex-1 text-sm inline-flex items-center justify-center space-x-1"
                >
                  <BookOpen className="h-4 w-4" />
                  <span>View</span>
                </Link>
              </div>
            </div>
          ))}
        </div>
      ) : (
        /* Empty State */
        <div className="text-center py-12">
          <BookOpen className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-gray-900 mb-2">
            No decks yet
          </h3>
          <p className="text-gray-600 mb-6 max-w-md mx-auto">
            Create your first flashcard deck to start learning. You can add cards manually 
            or generate them from text content.
          </p>
          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            <Link
              to="/decks/create"
              className="btn-primary inline-flex items-center space-x-2"
            >
              <Plus className="h-4 w-4" />
              <span>Create Your First Deck</span>
            </Link>
            <button className="btn-secondary inline-flex items-center space-x-2">
              <BookOpen className="h-4 w-4" />
              <span>Import from File</span>
            </button>
          </div>
        </div>
      )}

      {/* Quick Stats */}
      {decks.length > 0 && (
        <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="card text-center">
            <div className="text-2xl font-bold text-primary-600">
              {decks.length}
            </div>
            <div className="text-sm text-gray-600">Total Decks</div>
          </div>
          
          <div className="card text-center">
            <div className="text-2xl font-bold text-success-600">
              {decks.reduce((sum, deck) => sum + deck.flashcard_count, 0)}
            </div>
            <div className="text-sm text-gray-600">Total Cards</div>
          </div>
          
          <div className="card text-center">
            <div className="text-2xl font-bold text-warning-600">
              {decks.reduce((sum, deck) => sum + deck.due_count, 0)}
            </div>
            <div className="text-sm text-gray-600">Cards Due</div>
          </div>
        </div>
      )}
    </div>
  );
};
