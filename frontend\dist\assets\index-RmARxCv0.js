(function(){const r=document.createElement("link").relList;if(r&&r.supports&&r.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))s(o);new MutationObserver(o=>{for(const d of o)if(d.type==="childList")for(const h of d.addedNodes)h.tagName==="LINK"&&h.rel==="modulepreload"&&s(h)}).observe(document,{childList:!0,subtree:!0});function c(o){const d={};return o.integrity&&(d.integrity=o.integrity),o.referrerPolicy&&(d.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?d.credentials="include":o.crossOrigin==="anonymous"?d.credentials="omit":d.credentials="same-origin",d}function s(o){if(o.ep)return;o.ep=!0;const d=c(o);fetch(o.href,d)}})();function Bp(n){return n&&n.__esModule&&Object.prototype.hasOwnProperty.call(n,"default")?n.default:n}var yc={exports:{}},In={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Qh;function qp(){if(Qh)return In;Qh=1;var n=Symbol.for("react.transitional.element"),r=Symbol.for("react.fragment");function c(s,o,d){var h=null;if(d!==void 0&&(h=""+d),o.key!==void 0&&(h=""+o.key),"key"in o){d={};for(var v in o)v!=="key"&&(d[v]=o[v])}else d=o;return o=d.ref,{$$typeof:n,type:s,key:h,ref:o!==void 0?o:null,props:d}}return In.Fragment=r,In.jsx=c,In.jsxs=c,In}var Vh;function Lp(){return Vh||(Vh=1,yc.exports=qp()),yc.exports}var p=Lp(),pc={exports:{}},ae={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Zh;function Yp(){if(Zh)return ae;Zh=1;var n=Symbol.for("react.transitional.element"),r=Symbol.for("react.portal"),c=Symbol.for("react.fragment"),s=Symbol.for("react.strict_mode"),o=Symbol.for("react.profiler"),d=Symbol.for("react.consumer"),h=Symbol.for("react.context"),v=Symbol.for("react.forward_ref"),g=Symbol.for("react.suspense"),y=Symbol.for("react.memo"),x=Symbol.for("react.lazy"),O=Symbol.iterator;function C(S){return S===null||typeof S!="object"?null:(S=O&&S[O]||S["@@iterator"],typeof S=="function"?S:null)}var G={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},D=Object.assign,q={};function B(S,L,V){this.props=S,this.context=L,this.refs=q,this.updater=V||G}B.prototype.isReactComponent={},B.prototype.setState=function(S,L){if(typeof S!="object"&&typeof S!="function"&&S!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,S,L,"setState")},B.prototype.forceUpdate=function(S){this.updater.enqueueForceUpdate(this,S,"forceUpdate")};function Y(){}Y.prototype=B.prototype;function $(S,L,V){this.props=S,this.context=L,this.refs=q,this.updater=V||G}var K=$.prototype=new Y;K.constructor=$,D(K,B.prototype),K.isPureReactComponent=!0;var ce=Array.isArray,k={H:null,A:null,T:null,S:null,V:null},ve=Object.prototype.hasOwnProperty;function be(S,L,V,X,F,oe){return V=oe.ref,{$$typeof:n,type:S,key:L,ref:V!==void 0?V:null,props:oe}}function je(S,L){return be(S.type,L,void 0,void 0,void 0,S.props)}function Ne(S){return typeof S=="object"&&S!==null&&S.$$typeof===n}function st(S){var L={"=":"=0",":":"=2"};return"$"+S.replace(/[=:]/g,function(V){return L[V]})}var bt=/\/+/g;function Ke(S,L){return typeof S=="object"&&S!==null&&S.key!=null?st(""+S.key):L.toString(36)}function ql(){}function Ll(S){switch(S.status){case"fulfilled":return S.value;case"rejected":throw S.reason;default:switch(typeof S.status=="string"?S.then(ql,ql):(S.status="pending",S.then(function(L){S.status==="pending"&&(S.status="fulfilled",S.value=L)},function(L){S.status==="pending"&&(S.status="rejected",S.reason=L)})),S.status){case"fulfilled":return S.value;case"rejected":throw S.reason}}throw S}function ke(S,L,V,X,F){var oe=typeof S;(oe==="undefined"||oe==="boolean")&&(S=null);var te=!1;if(S===null)te=!0;else switch(oe){case"bigint":case"string":case"number":te=!0;break;case"object":switch(S.$$typeof){case n:case r:te=!0;break;case x:return te=S._init,ke(te(S._payload),L,V,X,F)}}if(te)return F=F(S),te=X===""?"."+Ke(S,0):X,ce(F)?(V="",te!=null&&(V=te.replace(bt,"$&/")+"/"),ke(F,L,V,"",function(fl){return fl})):F!=null&&(Ne(F)&&(F=je(F,V+(F.key==null||S&&S.key===F.key?"":(""+F.key).replace(bt,"$&/")+"/")+te)),L.push(F)),1;te=0;var ct=X===""?".":X+":";if(ce(S))for(var Te=0;Te<S.length;Te++)X=S[Te],oe=ct+Ke(X,Te),te+=ke(X,L,V,oe,F);else if(Te=C(S),typeof Te=="function")for(S=Te.call(S),Te=0;!(X=S.next()).done;)X=X.value,oe=ct+Ke(X,Te++),te+=ke(X,L,V,oe,F);else if(oe==="object"){if(typeof S.then=="function")return ke(Ll(S),L,V,X,F);throw L=String(S),Error("Objects are not valid as a React child (found: "+(L==="[object Object]"?"object with keys {"+Object.keys(S).join(", ")+"}":L)+"). If you meant to render a collection of children, use an array instead.")}return te}function z(S,L,V){if(S==null)return S;var X=[],F=0;return ke(S,X,"","",function(oe){return L.call(V,oe,F++)}),X}function Q(S){if(S._status===-1){var L=S._result;L=L(),L.then(function(V){(S._status===0||S._status===-1)&&(S._status=1,S._result=V)},function(V){(S._status===0||S._status===-1)&&(S._status=2,S._result=V)}),S._status===-1&&(S._status=0,S._result=L)}if(S._status===1)return S._result.default;throw S._result}var I=typeof reportError=="function"?reportError:function(S){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var L=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof S=="object"&&S!==null&&typeof S.message=="string"?String(S.message):String(S),error:S});if(!window.dispatchEvent(L))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",S);return}console.error(S)};function xe(){}return ae.Children={map:z,forEach:function(S,L,V){z(S,function(){L.apply(this,arguments)},V)},count:function(S){var L=0;return z(S,function(){L++}),L},toArray:function(S){return z(S,function(L){return L})||[]},only:function(S){if(!Ne(S))throw Error("React.Children.only expected to receive a single React element child.");return S}},ae.Component=B,ae.Fragment=c,ae.Profiler=o,ae.PureComponent=$,ae.StrictMode=s,ae.Suspense=g,ae.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=k,ae.__COMPILER_RUNTIME={__proto__:null,c:function(S){return k.H.useMemoCache(S)}},ae.cache=function(S){return function(){return S.apply(null,arguments)}},ae.cloneElement=function(S,L,V){if(S==null)throw Error("The argument must be a React element, but you passed "+S+".");var X=D({},S.props),F=S.key,oe=void 0;if(L!=null)for(te in L.ref!==void 0&&(oe=void 0),L.key!==void 0&&(F=""+L.key),L)!ve.call(L,te)||te==="key"||te==="__self"||te==="__source"||te==="ref"&&L.ref===void 0||(X[te]=L[te]);var te=arguments.length-2;if(te===1)X.children=V;else if(1<te){for(var ct=Array(te),Te=0;Te<te;Te++)ct[Te]=arguments[Te+2];X.children=ct}return be(S.type,F,void 0,void 0,oe,X)},ae.createContext=function(S){return S={$$typeof:h,_currentValue:S,_currentValue2:S,_threadCount:0,Provider:null,Consumer:null},S.Provider=S,S.Consumer={$$typeof:d,_context:S},S},ae.createElement=function(S,L,V){var X,F={},oe=null;if(L!=null)for(X in L.key!==void 0&&(oe=""+L.key),L)ve.call(L,X)&&X!=="key"&&X!=="__self"&&X!=="__source"&&(F[X]=L[X]);var te=arguments.length-2;if(te===1)F.children=V;else if(1<te){for(var ct=Array(te),Te=0;Te<te;Te++)ct[Te]=arguments[Te+2];F.children=ct}if(S&&S.defaultProps)for(X in te=S.defaultProps,te)F[X]===void 0&&(F[X]=te[X]);return be(S,oe,void 0,void 0,null,F)},ae.createRef=function(){return{current:null}},ae.forwardRef=function(S){return{$$typeof:v,render:S}},ae.isValidElement=Ne,ae.lazy=function(S){return{$$typeof:x,_payload:{_status:-1,_result:S},_init:Q}},ae.memo=function(S,L){return{$$typeof:y,type:S,compare:L===void 0?null:L}},ae.startTransition=function(S){var L=k.T,V={};k.T=V;try{var X=S(),F=k.S;F!==null&&F(V,X),typeof X=="object"&&X!==null&&typeof X.then=="function"&&X.then(xe,I)}catch(oe){I(oe)}finally{k.T=L}},ae.unstable_useCacheRefresh=function(){return k.H.useCacheRefresh()},ae.use=function(S){return k.H.use(S)},ae.useActionState=function(S,L,V){return k.H.useActionState(S,L,V)},ae.useCallback=function(S,L){return k.H.useCallback(S,L)},ae.useContext=function(S){return k.H.useContext(S)},ae.useDebugValue=function(){},ae.useDeferredValue=function(S,L){return k.H.useDeferredValue(S,L)},ae.useEffect=function(S,L,V){var X=k.H;if(typeof V=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return X.useEffect(S,L)},ae.useId=function(){return k.H.useId()},ae.useImperativeHandle=function(S,L,V){return k.H.useImperativeHandle(S,L,V)},ae.useInsertionEffect=function(S,L){return k.H.useInsertionEffect(S,L)},ae.useLayoutEffect=function(S,L){return k.H.useLayoutEffect(S,L)},ae.useMemo=function(S,L){return k.H.useMemo(S,L)},ae.useOptimistic=function(S,L){return k.H.useOptimistic(S,L)},ae.useReducer=function(S,L,V){return k.H.useReducer(S,L,V)},ae.useRef=function(S){return k.H.useRef(S)},ae.useState=function(S){return k.H.useState(S)},ae.useSyncExternalStore=function(S,L,V){return k.H.useSyncExternalStore(S,L,V)},ae.useTransition=function(){return k.H.useTransition()},ae.version="19.1.0",ae}var Kh;function qc(){return Kh||(Kh=1,pc.exports=Yp()),pc.exports}var A=qc();const xm=Bp(A);var gc={exports:{}},eu={},vc={exports:{}},bc={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var kh;function Gp(){return kh||(kh=1,function(n){function r(z,Q){var I=z.length;z.push(Q);e:for(;0<I;){var xe=I-1>>>1,S=z[xe];if(0<o(S,Q))z[xe]=Q,z[I]=S,I=xe;else break e}}function c(z){return z.length===0?null:z[0]}function s(z){if(z.length===0)return null;var Q=z[0],I=z.pop();if(I!==Q){z[0]=I;e:for(var xe=0,S=z.length,L=S>>>1;xe<L;){var V=2*(xe+1)-1,X=z[V],F=V+1,oe=z[F];if(0>o(X,I))F<S&&0>o(oe,X)?(z[xe]=oe,z[F]=I,xe=F):(z[xe]=X,z[V]=I,xe=V);else if(F<S&&0>o(oe,I))z[xe]=oe,z[F]=I,xe=F;else break e}}return Q}function o(z,Q){var I=z.sortIndex-Q.sortIndex;return I!==0?I:z.id-Q.id}if(n.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var d=performance;n.unstable_now=function(){return d.now()}}else{var h=Date,v=h.now();n.unstable_now=function(){return h.now()-v}}var g=[],y=[],x=1,O=null,C=3,G=!1,D=!1,q=!1,B=!1,Y=typeof setTimeout=="function"?setTimeout:null,$=typeof clearTimeout=="function"?clearTimeout:null,K=typeof setImmediate<"u"?setImmediate:null;function ce(z){for(var Q=c(y);Q!==null;){if(Q.callback===null)s(y);else if(Q.startTime<=z)s(y),Q.sortIndex=Q.expirationTime,r(g,Q);else break;Q=c(y)}}function k(z){if(q=!1,ce(z),!D)if(c(g)!==null)D=!0,ve||(ve=!0,Ke());else{var Q=c(y);Q!==null&&ke(k,Q.startTime-z)}}var ve=!1,be=-1,je=5,Ne=-1;function st(){return B?!0:!(n.unstable_now()-Ne<je)}function bt(){if(B=!1,ve){var z=n.unstable_now();Ne=z;var Q=!0;try{e:{D=!1,q&&(q=!1,$(be),be=-1),G=!0;var I=C;try{t:{for(ce(z),O=c(g);O!==null&&!(O.expirationTime>z&&st());){var xe=O.callback;if(typeof xe=="function"){O.callback=null,C=O.priorityLevel;var S=xe(O.expirationTime<=z);if(z=n.unstable_now(),typeof S=="function"){O.callback=S,ce(z),Q=!0;break t}O===c(g)&&s(g),ce(z)}else s(g);O=c(g)}if(O!==null)Q=!0;else{var L=c(y);L!==null&&ke(k,L.startTime-z),Q=!1}}break e}finally{O=null,C=I,G=!1}Q=void 0}}finally{Q?Ke():ve=!1}}}var Ke;if(typeof K=="function")Ke=function(){K(bt)};else if(typeof MessageChannel<"u"){var ql=new MessageChannel,Ll=ql.port2;ql.port1.onmessage=bt,Ke=function(){Ll.postMessage(null)}}else Ke=function(){Y(bt,0)};function ke(z,Q){be=Y(function(){z(n.unstable_now())},Q)}n.unstable_IdlePriority=5,n.unstable_ImmediatePriority=1,n.unstable_LowPriority=4,n.unstable_NormalPriority=3,n.unstable_Profiling=null,n.unstable_UserBlockingPriority=2,n.unstable_cancelCallback=function(z){z.callback=null},n.unstable_forceFrameRate=function(z){0>z||125<z?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):je=0<z?Math.floor(1e3/z):5},n.unstable_getCurrentPriorityLevel=function(){return C},n.unstable_next=function(z){switch(C){case 1:case 2:case 3:var Q=3;break;default:Q=C}var I=C;C=Q;try{return z()}finally{C=I}},n.unstable_requestPaint=function(){B=!0},n.unstable_runWithPriority=function(z,Q){switch(z){case 1:case 2:case 3:case 4:case 5:break;default:z=3}var I=C;C=z;try{return Q()}finally{C=I}},n.unstable_scheduleCallback=function(z,Q,I){var xe=n.unstable_now();switch(typeof I=="object"&&I!==null?(I=I.delay,I=typeof I=="number"&&0<I?xe+I:xe):I=xe,z){case 1:var S=-1;break;case 2:S=250;break;case 5:S=1073741823;break;case 4:S=1e4;break;default:S=5e3}return S=I+S,z={id:x++,callback:Q,priorityLevel:z,startTime:I,expirationTime:S,sortIndex:-1},I>xe?(z.sortIndex=I,r(y,z),c(g)===null&&z===c(y)&&(q?($(be),be=-1):q=!0,ke(k,I-xe))):(z.sortIndex=S,r(g,z),D||G||(D=!0,ve||(ve=!0,Ke()))),z},n.unstable_shouldYield=st,n.unstable_wrapCallback=function(z){var Q=C;return function(){var I=C;C=Q;try{return z.apply(this,arguments)}finally{C=I}}}}(bc)),bc}var Jh;function Xp(){return Jh||(Jh=1,vc.exports=Gp()),vc.exports}var xc={exports:{}},Fe={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var $h;function Qp(){if($h)return Fe;$h=1;var n=qc();function r(g){var y="https://react.dev/errors/"+g;if(1<arguments.length){y+="?args[]="+encodeURIComponent(arguments[1]);for(var x=2;x<arguments.length;x++)y+="&args[]="+encodeURIComponent(arguments[x])}return"Minified React error #"+g+"; visit "+y+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function c(){}var s={d:{f:c,r:function(){throw Error(r(522))},D:c,C:c,L:c,m:c,X:c,S:c,M:c},p:0,findDOMNode:null},o=Symbol.for("react.portal");function d(g,y,x){var O=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:o,key:O==null?null:""+O,children:g,containerInfo:y,implementation:x}}var h=n.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function v(g,y){if(g==="font")return"";if(typeof y=="string")return y==="use-credentials"?y:""}return Fe.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=s,Fe.createPortal=function(g,y){var x=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!y||y.nodeType!==1&&y.nodeType!==9&&y.nodeType!==11)throw Error(r(299));return d(g,y,null,x)},Fe.flushSync=function(g){var y=h.T,x=s.p;try{if(h.T=null,s.p=2,g)return g()}finally{h.T=y,s.p=x,s.d.f()}},Fe.preconnect=function(g,y){typeof g=="string"&&(y?(y=y.crossOrigin,y=typeof y=="string"?y==="use-credentials"?y:"":void 0):y=null,s.d.C(g,y))},Fe.prefetchDNS=function(g){typeof g=="string"&&s.d.D(g)},Fe.preinit=function(g,y){if(typeof g=="string"&&y&&typeof y.as=="string"){var x=y.as,O=v(x,y.crossOrigin),C=typeof y.integrity=="string"?y.integrity:void 0,G=typeof y.fetchPriority=="string"?y.fetchPriority:void 0;x==="style"?s.d.S(g,typeof y.precedence=="string"?y.precedence:void 0,{crossOrigin:O,integrity:C,fetchPriority:G}):x==="script"&&s.d.X(g,{crossOrigin:O,integrity:C,fetchPriority:G,nonce:typeof y.nonce=="string"?y.nonce:void 0})}},Fe.preinitModule=function(g,y){if(typeof g=="string")if(typeof y=="object"&&y!==null){if(y.as==null||y.as==="script"){var x=v(y.as,y.crossOrigin);s.d.M(g,{crossOrigin:x,integrity:typeof y.integrity=="string"?y.integrity:void 0,nonce:typeof y.nonce=="string"?y.nonce:void 0})}}else y==null&&s.d.M(g)},Fe.preload=function(g,y){if(typeof g=="string"&&typeof y=="object"&&y!==null&&typeof y.as=="string"){var x=y.as,O=v(x,y.crossOrigin);s.d.L(g,x,{crossOrigin:O,integrity:typeof y.integrity=="string"?y.integrity:void 0,nonce:typeof y.nonce=="string"?y.nonce:void 0,type:typeof y.type=="string"?y.type:void 0,fetchPriority:typeof y.fetchPriority=="string"?y.fetchPriority:void 0,referrerPolicy:typeof y.referrerPolicy=="string"?y.referrerPolicy:void 0,imageSrcSet:typeof y.imageSrcSet=="string"?y.imageSrcSet:void 0,imageSizes:typeof y.imageSizes=="string"?y.imageSizes:void 0,media:typeof y.media=="string"?y.media:void 0})}},Fe.preloadModule=function(g,y){if(typeof g=="string")if(y){var x=v(y.as,y.crossOrigin);s.d.m(g,{as:typeof y.as=="string"&&y.as!=="script"?y.as:void 0,crossOrigin:x,integrity:typeof y.integrity=="string"?y.integrity:void 0})}else s.d.m(g)},Fe.requestFormReset=function(g){s.d.r(g)},Fe.unstable_batchedUpdates=function(g,y){return g(y)},Fe.useFormState=function(g,y,x){return h.H.useFormState(g,y,x)},Fe.useFormStatus=function(){return h.H.useHostTransitionStatus()},Fe.version="19.1.0",Fe}var Fh;function Vp(){if(Fh)return xc.exports;Fh=1;function n(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(n)}catch(r){console.error(r)}}return n(),xc.exports=Qp(),xc.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Wh;function Zp(){if(Wh)return eu;Wh=1;var n=Xp(),r=qc(),c=Vp();function s(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var l=2;l<arguments.length;l++)t+="&args[]="+encodeURIComponent(arguments[l])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function o(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function d(e){var t=e,l=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(l=t.return),e=t.return;while(e)}return t.tag===3?l:null}function h(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function v(e){if(d(e)!==e)throw Error(s(188))}function g(e){var t=e.alternate;if(!t){if(t=d(e),t===null)throw Error(s(188));return t!==e?null:e}for(var l=e,a=t;;){var u=l.return;if(u===null)break;var i=u.alternate;if(i===null){if(a=u.return,a!==null){l=a;continue}break}if(u.child===i.child){for(i=u.child;i;){if(i===l)return v(u),e;if(i===a)return v(u),t;i=i.sibling}throw Error(s(188))}if(l.return!==a.return)l=u,a=i;else{for(var f=!1,m=u.child;m;){if(m===l){f=!0,l=u,a=i;break}if(m===a){f=!0,a=u,l=i;break}m=m.sibling}if(!f){for(m=i.child;m;){if(m===l){f=!0,l=i,a=u;break}if(m===a){f=!0,a=i,l=u;break}m=m.sibling}if(!f)throw Error(s(189))}}if(l.alternate!==a)throw Error(s(190))}if(l.tag!==3)throw Error(s(188));return l.stateNode.current===l?e:t}function y(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e;for(e=e.child;e!==null;){if(t=y(e),t!==null)return t;e=e.sibling}return null}var x=Object.assign,O=Symbol.for("react.element"),C=Symbol.for("react.transitional.element"),G=Symbol.for("react.portal"),D=Symbol.for("react.fragment"),q=Symbol.for("react.strict_mode"),B=Symbol.for("react.profiler"),Y=Symbol.for("react.provider"),$=Symbol.for("react.consumer"),K=Symbol.for("react.context"),ce=Symbol.for("react.forward_ref"),k=Symbol.for("react.suspense"),ve=Symbol.for("react.suspense_list"),be=Symbol.for("react.memo"),je=Symbol.for("react.lazy"),Ne=Symbol.for("react.activity"),st=Symbol.for("react.memo_cache_sentinel"),bt=Symbol.iterator;function Ke(e){return e===null||typeof e!="object"?null:(e=bt&&e[bt]||e["@@iterator"],typeof e=="function"?e:null)}var ql=Symbol.for("react.client.reference");function Ll(e){if(e==null)return null;if(typeof e=="function")return e.$$typeof===ql?null:e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case D:return"Fragment";case B:return"Profiler";case q:return"StrictMode";case k:return"Suspense";case ve:return"SuspenseList";case Ne:return"Activity"}if(typeof e=="object")switch(e.$$typeof){case G:return"Portal";case K:return(e.displayName||"Context")+".Provider";case $:return(e._context.displayName||"Context")+".Consumer";case ce:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case be:return t=e.displayName||null,t!==null?t:Ll(e.type)||"Memo";case je:t=e._payload,e=e._init;try{return Ll(e(t))}catch{}}return null}var ke=Array.isArray,z=r.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,Q=c.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,I={pending:!1,data:null,method:null,action:null},xe=[],S=-1;function L(e){return{current:e}}function V(e){0>S||(e.current=xe[S],xe[S]=null,S--)}function X(e,t){S++,xe[S]=e.current,e.current=t}var F=L(null),oe=L(null),te=L(null),ct=L(null);function Te(e,t){switch(X(te,t),X(oe,e),X(F,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?gh(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)t=gh(t),e=vh(t,e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}V(F),X(F,e)}function fl(){V(F),V(oe),V(te)}function er(e){e.memoizedState!==null&&X(ct,e);var t=F.current,l=vh(t,e.type);t!==l&&(X(oe,e),X(F,l))}function hu(e){oe.current===e&&(V(F),V(oe)),ct.current===e&&(V(ct),Jn._currentValue=I)}var tr=Object.prototype.hasOwnProperty,lr=n.unstable_scheduleCallback,ar=n.unstable_cancelCallback,y0=n.unstable_shouldYield,p0=n.unstable_requestPaint,Ht=n.unstable_now,g0=n.unstable_getCurrentPriorityLevel,$c=n.unstable_ImmediatePriority,Fc=n.unstable_UserBlockingPriority,mu=n.unstable_NormalPriority,v0=n.unstable_LowPriority,Wc=n.unstable_IdlePriority,b0=n.log,x0=n.unstable_setDisableYieldValue,tn=null,ot=null;function dl(e){if(typeof b0=="function"&&x0(e),ot&&typeof ot.setStrictMode=="function")try{ot.setStrictMode(tn,e)}catch{}}var ft=Math.clz32?Math.clz32:N0,S0=Math.log,E0=Math.LN2;function N0(e){return e>>>=0,e===0?32:31-(S0(e)/E0|0)|0}var yu=256,pu=4194304;function Yl(e){var t=e&42;if(t!==0)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return e&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function gu(e,t,l){var a=e.pendingLanes;if(a===0)return 0;var u=0,i=e.suspendedLanes,f=e.pingedLanes;e=e.warmLanes;var m=a&134217727;return m!==0?(a=m&~i,a!==0?u=Yl(a):(f&=m,f!==0?u=Yl(f):l||(l=m&~e,l!==0&&(u=Yl(l))))):(m=a&~i,m!==0?u=Yl(m):f!==0?u=Yl(f):l||(l=a&~e,l!==0&&(u=Yl(l)))),u===0?0:t!==0&&t!==u&&(t&i)===0&&(i=u&-u,l=t&-t,i>=l||i===32&&(l&4194048)!==0)?t:u}function ln(e,t){return(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)===0}function T0(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Pc(){var e=yu;return yu<<=1,(yu&4194048)===0&&(yu=256),e}function Ic(){var e=pu;return pu<<=1,(pu&62914560)===0&&(pu=4194304),e}function nr(e){for(var t=[],l=0;31>l;l++)t.push(e);return t}function an(e,t){e.pendingLanes|=t,t!==268435456&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function R0(e,t,l,a,u,i){var f=e.pendingLanes;e.pendingLanes=l,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=l,e.entangledLanes&=l,e.errorRecoveryDisabledLanes&=l,e.shellSuspendCounter=0;var m=e.entanglements,b=e.expirationTimes,R=e.hiddenUpdates;for(l=f&~l;0<l;){var M=31-ft(l),H=1<<M;m[M]=0,b[M]=-1;var j=R[M];if(j!==null)for(R[M]=null,M=0;M<j.length;M++){var w=j[M];w!==null&&(w.lane&=-536870913)}l&=~H}a!==0&&eo(e,a,0),i!==0&&u===0&&e.tag!==0&&(e.suspendedLanes|=i&~(f&~t))}function eo(e,t,l){e.pendingLanes|=t,e.suspendedLanes&=~t;var a=31-ft(t);e.entangledLanes|=t,e.entanglements[a]=e.entanglements[a]|1073741824|l&4194090}function to(e,t){var l=e.entangledLanes|=t;for(e=e.entanglements;l;){var a=31-ft(l),u=1<<a;u&t|e[a]&t&&(e[a]|=t),l&=~u}}function ur(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function ir(e){return e&=-e,2<e?8<e?(e&134217727)!==0?32:268435456:8:2}function lo(){var e=Q.p;return e!==0?e:(e=window.event,e===void 0?32:Bh(e.type))}function A0(e,t){var l=Q.p;try{return Q.p=e,t()}finally{Q.p=l}}var hl=Math.random().toString(36).slice(2),Je="__reactFiber$"+hl,tt="__reactProps$"+hl,oa="__reactContainer$"+hl,rr="__reactEvents$"+hl,O0="__reactListeners$"+hl,j0="__reactHandles$"+hl,ao="__reactResources$"+hl,nn="__reactMarker$"+hl;function sr(e){delete e[Je],delete e[tt],delete e[rr],delete e[O0],delete e[j0]}function fa(e){var t=e[Je];if(t)return t;for(var l=e.parentNode;l;){if(t=l[oa]||l[Je]){if(l=t.alternate,t.child!==null||l!==null&&l.child!==null)for(e=Eh(e);e!==null;){if(l=e[Je])return l;e=Eh(e)}return t}e=l,l=e.parentNode}return null}function da(e){if(e=e[Je]||e[oa]){var t=e.tag;if(t===5||t===6||t===13||t===26||t===27||t===3)return e}return null}function un(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e.stateNode;throw Error(s(33))}function ha(e){var t=e[ao];return t||(t=e[ao]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function Le(e){e[nn]=!0}var no=new Set,uo={};function Gl(e,t){ma(e,t),ma(e+"Capture",t)}function ma(e,t){for(uo[e]=t,e=0;e<t.length;e++)no.add(t[e])}var w0=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),io={},ro={};function _0(e){return tr.call(ro,e)?!0:tr.call(io,e)?!1:w0.test(e)?ro[e]=!0:(io[e]=!0,!1)}function vu(e,t,l){if(_0(t))if(l===null)e.removeAttribute(t);else{switch(typeof l){case"undefined":case"function":case"symbol":e.removeAttribute(t);return;case"boolean":var a=t.toLowerCase().slice(0,5);if(a!=="data-"&&a!=="aria-"){e.removeAttribute(t);return}}e.setAttribute(t,""+l)}}function bu(e,t,l){if(l===null)e.removeAttribute(t);else{switch(typeof l){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(t);return}e.setAttribute(t,""+l)}}function Qt(e,t,l,a){if(a===null)e.removeAttribute(l);else{switch(typeof a){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(l);return}e.setAttributeNS(t,l,""+a)}}var cr,so;function ya(e){if(cr===void 0)try{throw Error()}catch(l){var t=l.stack.trim().match(/\n( *(at )?)/);cr=t&&t[1]||"",so=-1<l.stack.indexOf(`
    at`)?" (<anonymous>)":-1<l.stack.indexOf("@")?"@unknown:0:0":""}return`
`+cr+e+so}var or=!1;function fr(e,t){if(!e||or)return"";or=!0;var l=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var a={DetermineComponentFrameRoot:function(){try{if(t){var H=function(){throw Error()};if(Object.defineProperty(H.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(H,[])}catch(w){var j=w}Reflect.construct(e,[],H)}else{try{H.call()}catch(w){j=w}e.call(H.prototype)}}else{try{throw Error()}catch(w){j=w}(H=e())&&typeof H.catch=="function"&&H.catch(function(){})}}catch(w){if(w&&j&&typeof w.stack=="string")return[w.stack,j.stack]}return[null,null]}};a.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var u=Object.getOwnPropertyDescriptor(a.DetermineComponentFrameRoot,"name");u&&u.configurable&&Object.defineProperty(a.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var i=a.DetermineComponentFrameRoot(),f=i[0],m=i[1];if(f&&m){var b=f.split(`
`),R=m.split(`
`);for(u=a=0;a<b.length&&!b[a].includes("DetermineComponentFrameRoot");)a++;for(;u<R.length&&!R[u].includes("DetermineComponentFrameRoot");)u++;if(a===b.length||u===R.length)for(a=b.length-1,u=R.length-1;1<=a&&0<=u&&b[a]!==R[u];)u--;for(;1<=a&&0<=u;a--,u--)if(b[a]!==R[u]){if(a!==1||u!==1)do if(a--,u--,0>u||b[a]!==R[u]){var M=`
`+b[a].replace(" at new "," at ");return e.displayName&&M.includes("<anonymous>")&&(M=M.replace("<anonymous>",e.displayName)),M}while(1<=a&&0<=u);break}}}finally{or=!1,Error.prepareStackTrace=l}return(l=e?e.displayName||e.name:"")?ya(l):""}function D0(e){switch(e.tag){case 26:case 27:case 5:return ya(e.type);case 16:return ya("Lazy");case 13:return ya("Suspense");case 19:return ya("SuspenseList");case 0:case 15:return fr(e.type,!1);case 11:return fr(e.type.render,!1);case 1:return fr(e.type,!0);case 31:return ya("Activity");default:return""}}function co(e){try{var t="";do t+=D0(e),e=e.return;while(e);return t}catch(l){return`
Error generating stack: `+l.message+`
`+l.stack}}function xt(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function oo(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function C0(e){var t=oo(e)?"checked":"value",l=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),a=""+e[t];if(!e.hasOwnProperty(t)&&typeof l<"u"&&typeof l.get=="function"&&typeof l.set=="function"){var u=l.get,i=l.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return u.call(this)},set:function(f){a=""+f,i.call(this,f)}}),Object.defineProperty(e,t,{enumerable:l.enumerable}),{getValue:function(){return a},setValue:function(f){a=""+f},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function xu(e){e._valueTracker||(e._valueTracker=C0(e))}function fo(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var l=t.getValue(),a="";return e&&(a=oo(e)?e.checked?"true":"false":e.value),e=a,e!==l?(t.setValue(e),!0):!1}function Su(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}var M0=/[\n"\\]/g;function St(e){return e.replace(M0,function(t){return"\\"+t.charCodeAt(0).toString(16)+" "})}function dr(e,t,l,a,u,i,f,m){e.name="",f!=null&&typeof f!="function"&&typeof f!="symbol"&&typeof f!="boolean"?e.type=f:e.removeAttribute("type"),t!=null?f==="number"?(t===0&&e.value===""||e.value!=t)&&(e.value=""+xt(t)):e.value!==""+xt(t)&&(e.value=""+xt(t)):f!=="submit"&&f!=="reset"||e.removeAttribute("value"),t!=null?hr(e,f,xt(t)):l!=null?hr(e,f,xt(l)):a!=null&&e.removeAttribute("value"),u==null&&i!=null&&(e.defaultChecked=!!i),u!=null&&(e.checked=u&&typeof u!="function"&&typeof u!="symbol"),m!=null&&typeof m!="function"&&typeof m!="symbol"&&typeof m!="boolean"?e.name=""+xt(m):e.removeAttribute("name")}function ho(e,t,l,a,u,i,f,m){if(i!=null&&typeof i!="function"&&typeof i!="symbol"&&typeof i!="boolean"&&(e.type=i),t!=null||l!=null){if(!(i!=="submit"&&i!=="reset"||t!=null))return;l=l!=null?""+xt(l):"",t=t!=null?""+xt(t):l,m||t===e.value||(e.value=t),e.defaultValue=t}a=a??u,a=typeof a!="function"&&typeof a!="symbol"&&!!a,e.checked=m?e.checked:!!a,e.defaultChecked=!!a,f!=null&&typeof f!="function"&&typeof f!="symbol"&&typeof f!="boolean"&&(e.name=f)}function hr(e,t,l){t==="number"&&Su(e.ownerDocument)===e||e.defaultValue===""+l||(e.defaultValue=""+l)}function pa(e,t,l,a){if(e=e.options,t){t={};for(var u=0;u<l.length;u++)t["$"+l[u]]=!0;for(l=0;l<e.length;l++)u=t.hasOwnProperty("$"+e[l].value),e[l].selected!==u&&(e[l].selected=u),u&&a&&(e[l].defaultSelected=!0)}else{for(l=""+xt(l),t=null,u=0;u<e.length;u++){if(e[u].value===l){e[u].selected=!0,a&&(e[u].defaultSelected=!0);return}t!==null||e[u].disabled||(t=e[u])}t!==null&&(t.selected=!0)}}function mo(e,t,l){if(t!=null&&(t=""+xt(t),t!==e.value&&(e.value=t),l==null)){e.defaultValue!==t&&(e.defaultValue=t);return}e.defaultValue=l!=null?""+xt(l):""}function yo(e,t,l,a){if(t==null){if(a!=null){if(l!=null)throw Error(s(92));if(ke(a)){if(1<a.length)throw Error(s(93));a=a[0]}l=a}l==null&&(l=""),t=l}l=xt(t),e.defaultValue=l,a=e.textContent,a===l&&a!==""&&a!==null&&(e.value=a)}function ga(e,t){if(t){var l=e.firstChild;if(l&&l===e.lastChild&&l.nodeType===3){l.nodeValue=t;return}}e.textContent=t}var z0=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function po(e,t,l){var a=t.indexOf("--")===0;l==null||typeof l=="boolean"||l===""?a?e.setProperty(t,""):t==="float"?e.cssFloat="":e[t]="":a?e.setProperty(t,l):typeof l!="number"||l===0||z0.has(t)?t==="float"?e.cssFloat=l:e[t]=(""+l).trim():e[t]=l+"px"}function go(e,t,l){if(t!=null&&typeof t!="object")throw Error(s(62));if(e=e.style,l!=null){for(var a in l)!l.hasOwnProperty(a)||t!=null&&t.hasOwnProperty(a)||(a.indexOf("--")===0?e.setProperty(a,""):a==="float"?e.cssFloat="":e[a]="");for(var u in t)a=t[u],t.hasOwnProperty(u)&&l[u]!==a&&po(e,u,a)}else for(var i in t)t.hasOwnProperty(i)&&po(e,i,t[i])}function mr(e){if(e.indexOf("-")===-1)return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var U0=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),H0=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function Eu(e){return H0.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var yr=null;function pr(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var va=null,ba=null;function vo(e){var t=da(e);if(t&&(e=t.stateNode)){var l=e[tt]||null;e:switch(e=t.stateNode,t.type){case"input":if(dr(e,l.value,l.defaultValue,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name),t=l.name,l.type==="radio"&&t!=null){for(l=e;l.parentNode;)l=l.parentNode;for(l=l.querySelectorAll('input[name="'+St(""+t)+'"][type="radio"]'),t=0;t<l.length;t++){var a=l[t];if(a!==e&&a.form===e.form){var u=a[tt]||null;if(!u)throw Error(s(90));dr(a,u.value,u.defaultValue,u.defaultValue,u.checked,u.defaultChecked,u.type,u.name)}}for(t=0;t<l.length;t++)a=l[t],a.form===e.form&&fo(a)}break e;case"textarea":mo(e,l.value,l.defaultValue);break e;case"select":t=l.value,t!=null&&pa(e,!!l.multiple,t,!1)}}}var gr=!1;function bo(e,t,l){if(gr)return e(t,l);gr=!0;try{var a=e(t);return a}finally{if(gr=!1,(va!==null||ba!==null)&&(ri(),va&&(t=va,e=ba,ba=va=null,vo(t),e)))for(t=0;t<e.length;t++)vo(e[t])}}function rn(e,t){var l=e.stateNode;if(l===null)return null;var a=l[tt]||null;if(a===null)return null;l=a[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(a=!a.disabled)||(e=e.type,a=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!a;break e;default:e=!1}if(e)return null;if(l&&typeof l!="function")throw Error(s(231,t,typeof l));return l}var Vt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),vr=!1;if(Vt)try{var sn={};Object.defineProperty(sn,"passive",{get:function(){vr=!0}}),window.addEventListener("test",sn,sn),window.removeEventListener("test",sn,sn)}catch{vr=!1}var ml=null,br=null,Nu=null;function xo(){if(Nu)return Nu;var e,t=br,l=t.length,a,u="value"in ml?ml.value:ml.textContent,i=u.length;for(e=0;e<l&&t[e]===u[e];e++);var f=l-e;for(a=1;a<=f&&t[l-a]===u[i-a];a++);return Nu=u.slice(e,1<a?1-a:void 0)}function Tu(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Ru(){return!0}function So(){return!1}function lt(e){function t(l,a,u,i,f){this._reactName=l,this._targetInst=u,this.type=a,this.nativeEvent=i,this.target=f,this.currentTarget=null;for(var m in e)e.hasOwnProperty(m)&&(l=e[m],this[m]=l?l(i):i[m]);return this.isDefaultPrevented=(i.defaultPrevented!=null?i.defaultPrevented:i.returnValue===!1)?Ru:So,this.isPropagationStopped=So,this}return x(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var l=this.nativeEvent;l&&(l.preventDefault?l.preventDefault():typeof l.returnValue!="unknown"&&(l.returnValue=!1),this.isDefaultPrevented=Ru)},stopPropagation:function(){var l=this.nativeEvent;l&&(l.stopPropagation?l.stopPropagation():typeof l.cancelBubble!="unknown"&&(l.cancelBubble=!0),this.isPropagationStopped=Ru)},persist:function(){},isPersistent:Ru}),t}var Xl={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Au=lt(Xl),cn=x({},Xl,{view:0,detail:0}),B0=lt(cn),xr,Sr,on,Ou=x({},cn,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Nr,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==on&&(on&&e.type==="mousemove"?(xr=e.screenX-on.screenX,Sr=e.screenY-on.screenY):Sr=xr=0,on=e),xr)},movementY:function(e){return"movementY"in e?e.movementY:Sr}}),Eo=lt(Ou),q0=x({},Ou,{dataTransfer:0}),L0=lt(q0),Y0=x({},cn,{relatedTarget:0}),Er=lt(Y0),G0=x({},Xl,{animationName:0,elapsedTime:0,pseudoElement:0}),X0=lt(G0),Q0=x({},Xl,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),V0=lt(Q0),Z0=x({},Xl,{data:0}),No=lt(Z0),K0={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},k0={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},J0={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function $0(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=J0[e])?!!t[e]:!1}function Nr(){return $0}var F0=x({},cn,{key:function(e){if(e.key){var t=K0[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Tu(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?k0[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Nr,charCode:function(e){return e.type==="keypress"?Tu(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Tu(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),W0=lt(F0),P0=x({},Ou,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),To=lt(P0),I0=x({},cn,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Nr}),ey=lt(I0),ty=x({},Xl,{propertyName:0,elapsedTime:0,pseudoElement:0}),ly=lt(ty),ay=x({},Ou,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),ny=lt(ay),uy=x({},Xl,{newState:0,oldState:0}),iy=lt(uy),ry=[9,13,27,32],Tr=Vt&&"CompositionEvent"in window,fn=null;Vt&&"documentMode"in document&&(fn=document.documentMode);var sy=Vt&&"TextEvent"in window&&!fn,Ro=Vt&&(!Tr||fn&&8<fn&&11>=fn),Ao=" ",Oo=!1;function jo(e,t){switch(e){case"keyup":return ry.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function wo(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var xa=!1;function cy(e,t){switch(e){case"compositionend":return wo(t);case"keypress":return t.which!==32?null:(Oo=!0,Ao);case"textInput":return e=t.data,e===Ao&&Oo?null:e;default:return null}}function oy(e,t){if(xa)return e==="compositionend"||!Tr&&jo(e,t)?(e=xo(),Nu=br=ml=null,xa=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Ro&&t.locale!=="ko"?null:t.data;default:return null}}var fy={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function _o(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!fy[e.type]:t==="textarea"}function Do(e,t,l,a){va?ba?ba.push(a):ba=[a]:va=a,t=hi(t,"onChange"),0<t.length&&(l=new Au("onChange","change",null,l,a),e.push({event:l,listeners:t}))}var dn=null,hn=null;function dy(e){dh(e,0)}function ju(e){var t=un(e);if(fo(t))return e}function Co(e,t){if(e==="change")return t}var Mo=!1;if(Vt){var Rr;if(Vt){var Ar="oninput"in document;if(!Ar){var zo=document.createElement("div");zo.setAttribute("oninput","return;"),Ar=typeof zo.oninput=="function"}Rr=Ar}else Rr=!1;Mo=Rr&&(!document.documentMode||9<document.documentMode)}function Uo(){dn&&(dn.detachEvent("onpropertychange",Ho),hn=dn=null)}function Ho(e){if(e.propertyName==="value"&&ju(hn)){var t=[];Do(t,hn,e,pr(e)),bo(dy,t)}}function hy(e,t,l){e==="focusin"?(Uo(),dn=t,hn=l,dn.attachEvent("onpropertychange",Ho)):e==="focusout"&&Uo()}function my(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return ju(hn)}function yy(e,t){if(e==="click")return ju(t)}function py(e,t){if(e==="input"||e==="change")return ju(t)}function gy(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var dt=typeof Object.is=="function"?Object.is:gy;function mn(e,t){if(dt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var l=Object.keys(e),a=Object.keys(t);if(l.length!==a.length)return!1;for(a=0;a<l.length;a++){var u=l[a];if(!tr.call(t,u)||!dt(e[u],t[u]))return!1}return!0}function Bo(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function qo(e,t){var l=Bo(e);e=0;for(var a;l;){if(l.nodeType===3){if(a=e+l.textContent.length,e<=t&&a>=t)return{node:l,offset:t-e};e=a}e:{for(;l;){if(l.nextSibling){l=l.nextSibling;break e}l=l.parentNode}l=void 0}l=Bo(l)}}function Lo(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Lo(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Yo(e){e=e!=null&&e.ownerDocument!=null&&e.ownerDocument.defaultView!=null?e.ownerDocument.defaultView:window;for(var t=Su(e.document);t instanceof e.HTMLIFrameElement;){try{var l=typeof t.contentWindow.location.href=="string"}catch{l=!1}if(l)e=t.contentWindow;else break;t=Su(e.document)}return t}function Or(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}var vy=Vt&&"documentMode"in document&&11>=document.documentMode,Sa=null,jr=null,yn=null,wr=!1;function Go(e,t,l){var a=l.window===l?l.document:l.nodeType===9?l:l.ownerDocument;wr||Sa==null||Sa!==Su(a)||(a=Sa,"selectionStart"in a&&Or(a)?a={start:a.selectionStart,end:a.selectionEnd}:(a=(a.ownerDocument&&a.ownerDocument.defaultView||window).getSelection(),a={anchorNode:a.anchorNode,anchorOffset:a.anchorOffset,focusNode:a.focusNode,focusOffset:a.focusOffset}),yn&&mn(yn,a)||(yn=a,a=hi(jr,"onSelect"),0<a.length&&(t=new Au("onSelect","select",null,t,l),e.push({event:t,listeners:a}),t.target=Sa)))}function Ql(e,t){var l={};return l[e.toLowerCase()]=t.toLowerCase(),l["Webkit"+e]="webkit"+t,l["Moz"+e]="moz"+t,l}var Ea={animationend:Ql("Animation","AnimationEnd"),animationiteration:Ql("Animation","AnimationIteration"),animationstart:Ql("Animation","AnimationStart"),transitionrun:Ql("Transition","TransitionRun"),transitionstart:Ql("Transition","TransitionStart"),transitioncancel:Ql("Transition","TransitionCancel"),transitionend:Ql("Transition","TransitionEnd")},_r={},Xo={};Vt&&(Xo=document.createElement("div").style,"AnimationEvent"in window||(delete Ea.animationend.animation,delete Ea.animationiteration.animation,delete Ea.animationstart.animation),"TransitionEvent"in window||delete Ea.transitionend.transition);function Vl(e){if(_r[e])return _r[e];if(!Ea[e])return e;var t=Ea[e],l;for(l in t)if(t.hasOwnProperty(l)&&l in Xo)return _r[e]=t[l];return e}var Qo=Vl("animationend"),Vo=Vl("animationiteration"),Zo=Vl("animationstart"),by=Vl("transitionrun"),xy=Vl("transitionstart"),Sy=Vl("transitioncancel"),Ko=Vl("transitionend"),ko=new Map,Dr="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");Dr.push("scrollEnd");function wt(e,t){ko.set(e,t),Gl(t,[e])}var Jo=new WeakMap;function Et(e,t){if(typeof e=="object"&&e!==null){var l=Jo.get(e);return l!==void 0?l:(t={value:e,source:t,stack:co(t)},Jo.set(e,t),t)}return{value:e,source:t,stack:co(t)}}var Nt=[],Na=0,Cr=0;function wu(){for(var e=Na,t=Cr=Na=0;t<e;){var l=Nt[t];Nt[t++]=null;var a=Nt[t];Nt[t++]=null;var u=Nt[t];Nt[t++]=null;var i=Nt[t];if(Nt[t++]=null,a!==null&&u!==null){var f=a.pending;f===null?u.next=u:(u.next=f.next,f.next=u),a.pending=u}i!==0&&$o(l,u,i)}}function _u(e,t,l,a){Nt[Na++]=e,Nt[Na++]=t,Nt[Na++]=l,Nt[Na++]=a,Cr|=a,e.lanes|=a,e=e.alternate,e!==null&&(e.lanes|=a)}function Mr(e,t,l,a){return _u(e,t,l,a),Du(e)}function Ta(e,t){return _u(e,null,null,t),Du(e)}function $o(e,t,l){e.lanes|=l;var a=e.alternate;a!==null&&(a.lanes|=l);for(var u=!1,i=e.return;i!==null;)i.childLanes|=l,a=i.alternate,a!==null&&(a.childLanes|=l),i.tag===22&&(e=i.stateNode,e===null||e._visibility&1||(u=!0)),e=i,i=i.return;return e.tag===3?(i=e.stateNode,u&&t!==null&&(u=31-ft(l),e=i.hiddenUpdates,a=e[u],a===null?e[u]=[t]:a.push(t),t.lane=l|536870912),i):null}function Du(e){if(50<Yn)throw Yn=0,Ls=null,Error(s(185));for(var t=e.return;t!==null;)e=t,t=e.return;return e.tag===3?e.stateNode:null}var Ra={};function Ey(e,t,l,a){this.tag=e,this.key=l,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=a,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function ht(e,t,l,a){return new Ey(e,t,l,a)}function zr(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Zt(e,t){var l=e.alternate;return l===null?(l=ht(e.tag,t,e.key,e.mode),l.elementType=e.elementType,l.type=e.type,l.stateNode=e.stateNode,l.alternate=e,e.alternate=l):(l.pendingProps=t,l.type=e.type,l.flags=0,l.subtreeFlags=0,l.deletions=null),l.flags=e.flags&65011712,l.childLanes=e.childLanes,l.lanes=e.lanes,l.child=e.child,l.memoizedProps=e.memoizedProps,l.memoizedState=e.memoizedState,l.updateQueue=e.updateQueue,t=e.dependencies,l.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},l.sibling=e.sibling,l.index=e.index,l.ref=e.ref,l.refCleanup=e.refCleanup,l}function Fo(e,t){e.flags&=65011714;var l=e.alternate;return l===null?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=l.childLanes,e.lanes=l.lanes,e.child=l.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=l.memoizedProps,e.memoizedState=l.memoizedState,e.updateQueue=l.updateQueue,e.type=l.type,t=l.dependencies,e.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function Cu(e,t,l,a,u,i){var f=0;if(a=e,typeof e=="function")zr(e)&&(f=1);else if(typeof e=="string")f=Tp(e,l,F.current)?26:e==="html"||e==="head"||e==="body"?27:5;else e:switch(e){case Ne:return e=ht(31,l,t,u),e.elementType=Ne,e.lanes=i,e;case D:return Zl(l.children,u,i,t);case q:f=8,u|=24;break;case B:return e=ht(12,l,t,u|2),e.elementType=B,e.lanes=i,e;case k:return e=ht(13,l,t,u),e.elementType=k,e.lanes=i,e;case ve:return e=ht(19,l,t,u),e.elementType=ve,e.lanes=i,e;default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Y:case K:f=10;break e;case $:f=9;break e;case ce:f=11;break e;case be:f=14;break e;case je:f=16,a=null;break e}f=29,l=Error(s(130,e===null?"null":typeof e,"")),a=null}return t=ht(f,l,t,u),t.elementType=e,t.type=a,t.lanes=i,t}function Zl(e,t,l,a){return e=ht(7,e,a,t),e.lanes=l,e}function Ur(e,t,l){return e=ht(6,e,null,t),e.lanes=l,e}function Hr(e,t,l){return t=ht(4,e.children!==null?e.children:[],e.key,t),t.lanes=l,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var Aa=[],Oa=0,Mu=null,zu=0,Tt=[],Rt=0,Kl=null,Kt=1,kt="";function kl(e,t){Aa[Oa++]=zu,Aa[Oa++]=Mu,Mu=e,zu=t}function Wo(e,t,l){Tt[Rt++]=Kt,Tt[Rt++]=kt,Tt[Rt++]=Kl,Kl=e;var a=Kt;e=kt;var u=32-ft(a)-1;a&=~(1<<u),l+=1;var i=32-ft(t)+u;if(30<i){var f=u-u%5;i=(a&(1<<f)-1).toString(32),a>>=f,u-=f,Kt=1<<32-ft(t)+u|l<<u|a,kt=i+e}else Kt=1<<i|l<<u|a,kt=e}function Br(e){e.return!==null&&(kl(e,1),Wo(e,1,0))}function qr(e){for(;e===Mu;)Mu=Aa[--Oa],Aa[Oa]=null,zu=Aa[--Oa],Aa[Oa]=null;for(;e===Kl;)Kl=Tt[--Rt],Tt[Rt]=null,kt=Tt[--Rt],Tt[Rt]=null,Kt=Tt[--Rt],Tt[Rt]=null}var Pe=null,_e=null,de=!1,Jl=null,Bt=!1,Lr=Error(s(519));function $l(e){var t=Error(s(418,""));throw vn(Et(t,e)),Lr}function Po(e){var t=e.stateNode,l=e.type,a=e.memoizedProps;switch(t[Je]=e,t[tt]=a,l){case"dialog":re("cancel",t),re("close",t);break;case"iframe":case"object":case"embed":re("load",t);break;case"video":case"audio":for(l=0;l<Xn.length;l++)re(Xn[l],t);break;case"source":re("error",t);break;case"img":case"image":case"link":re("error",t),re("load",t);break;case"details":re("toggle",t);break;case"input":re("invalid",t),ho(t,a.value,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name,!0),xu(t);break;case"select":re("invalid",t);break;case"textarea":re("invalid",t),yo(t,a.value,a.defaultValue,a.children),xu(t)}l=a.children,typeof l!="string"&&typeof l!="number"&&typeof l!="bigint"||t.textContent===""+l||a.suppressHydrationWarning===!0||ph(t.textContent,l)?(a.popover!=null&&(re("beforetoggle",t),re("toggle",t)),a.onScroll!=null&&re("scroll",t),a.onScrollEnd!=null&&re("scrollend",t),a.onClick!=null&&(t.onclick=mi),t=!0):t=!1,t||$l(e)}function Io(e){for(Pe=e.return;Pe;)switch(Pe.tag){case 5:case 13:Bt=!1;return;case 27:case 3:Bt=!0;return;default:Pe=Pe.return}}function pn(e){if(e!==Pe)return!1;if(!de)return Io(e),de=!0,!1;var t=e.tag,l;if((l=t!==3&&t!==27)&&((l=t===5)&&(l=e.type,l=!(l!=="form"&&l!=="button")||tc(e.type,e.memoizedProps)),l=!l),l&&_e&&$l(e),Io(e),t===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(s(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8)if(l=e.data,l==="/$"){if(t===0){_e=Dt(e.nextSibling);break e}t--}else l!=="$"&&l!=="$!"&&l!=="$?"||t++;e=e.nextSibling}_e=null}}else t===27?(t=_e,_l(e.type)?(e=uc,uc=null,_e=e):_e=t):_e=Pe?Dt(e.stateNode.nextSibling):null;return!0}function gn(){_e=Pe=null,de=!1}function ef(){var e=Jl;return e!==null&&(ut===null?ut=e:ut.push.apply(ut,e),Jl=null),e}function vn(e){Jl===null?Jl=[e]:Jl.push(e)}var Yr=L(null),Fl=null,Jt=null;function yl(e,t,l){X(Yr,t._currentValue),t._currentValue=l}function $t(e){e._currentValue=Yr.current,V(Yr)}function Gr(e,t,l){for(;e!==null;){var a=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,a!==null&&(a.childLanes|=t)):a!==null&&(a.childLanes&t)!==t&&(a.childLanes|=t),e===l)break;e=e.return}}function Xr(e,t,l,a){var u=e.child;for(u!==null&&(u.return=e);u!==null;){var i=u.dependencies;if(i!==null){var f=u.child;i=i.firstContext;e:for(;i!==null;){var m=i;i=u;for(var b=0;b<t.length;b++)if(m.context===t[b]){i.lanes|=l,m=i.alternate,m!==null&&(m.lanes|=l),Gr(i.return,l,e),a||(f=null);break e}i=m.next}}else if(u.tag===18){if(f=u.return,f===null)throw Error(s(341));f.lanes|=l,i=f.alternate,i!==null&&(i.lanes|=l),Gr(f,l,e),f=null}else f=u.child;if(f!==null)f.return=u;else for(f=u;f!==null;){if(f===e){f=null;break}if(u=f.sibling,u!==null){u.return=f.return,f=u;break}f=f.return}u=f}}function bn(e,t,l,a){e=null;for(var u=t,i=!1;u!==null;){if(!i){if((u.flags&524288)!==0)i=!0;else if((u.flags&262144)!==0)break}if(u.tag===10){var f=u.alternate;if(f===null)throw Error(s(387));if(f=f.memoizedProps,f!==null){var m=u.type;dt(u.pendingProps.value,f.value)||(e!==null?e.push(m):e=[m])}}else if(u===ct.current){if(f=u.alternate,f===null)throw Error(s(387));f.memoizedState.memoizedState!==u.memoizedState.memoizedState&&(e!==null?e.push(Jn):e=[Jn])}u=u.return}e!==null&&Xr(t,e,l,a),t.flags|=262144}function Uu(e){for(e=e.firstContext;e!==null;){if(!dt(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function Wl(e){Fl=e,Jt=null,e=e.dependencies,e!==null&&(e.firstContext=null)}function $e(e){return tf(Fl,e)}function Hu(e,t){return Fl===null&&Wl(e),tf(e,t)}function tf(e,t){var l=t._currentValue;if(t={context:t,memoizedValue:l,next:null},Jt===null){if(e===null)throw Error(s(308));Jt=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else Jt=Jt.next=t;return l}var Ny=typeof AbortController<"u"?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(l,a){e.push(a)}};this.abort=function(){t.aborted=!0,e.forEach(function(l){return l()})}},Ty=n.unstable_scheduleCallback,Ry=n.unstable_NormalPriority,Be={$$typeof:K,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Qr(){return{controller:new Ny,data:new Map,refCount:0}}function xn(e){e.refCount--,e.refCount===0&&Ty(Ry,function(){e.controller.abort()})}var Sn=null,Vr=0,ja=0,wa=null;function Ay(e,t){if(Sn===null){var l=Sn=[];Vr=0,ja=Ks(),wa={status:"pending",value:void 0,then:function(a){l.push(a)}}}return Vr++,t.then(lf,lf),t}function lf(){if(--Vr===0&&Sn!==null){wa!==null&&(wa.status="fulfilled");var e=Sn;Sn=null,ja=0,wa=null;for(var t=0;t<e.length;t++)(0,e[t])()}}function Oy(e,t){var l=[],a={status:"pending",value:null,reason:null,then:function(u){l.push(u)}};return e.then(function(){a.status="fulfilled",a.value=t;for(var u=0;u<l.length;u++)(0,l[u])(t)},function(u){for(a.status="rejected",a.reason=u,u=0;u<l.length;u++)(0,l[u])(void 0)}),a}var af=z.S;z.S=function(e,t){typeof t=="object"&&t!==null&&typeof t.then=="function"&&Ay(e,t),af!==null&&af(e,t)};var Pl=L(null);function Zr(){var e=Pl.current;return e!==null?e:Ee.pooledCache}function Bu(e,t){t===null?X(Pl,Pl.current):X(Pl,t.pool)}function nf(){var e=Zr();return e===null?null:{parent:Be._currentValue,pool:e}}var En=Error(s(460)),uf=Error(s(474)),qu=Error(s(542)),Kr={then:function(){}};function rf(e){return e=e.status,e==="fulfilled"||e==="rejected"}function Lu(){}function sf(e,t,l){switch(l=e[l],l===void 0?e.push(t):l!==t&&(t.then(Lu,Lu),t=l),t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,of(e),e;default:if(typeof t.status=="string")t.then(Lu,Lu);else{if(e=Ee,e!==null&&100<e.shellSuspendCounter)throw Error(s(482));e=t,e.status="pending",e.then(function(a){if(t.status==="pending"){var u=t;u.status="fulfilled",u.value=a}},function(a){if(t.status==="pending"){var u=t;u.status="rejected",u.reason=a}})}switch(t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,of(e),e}throw Nn=t,En}}var Nn=null;function cf(){if(Nn===null)throw Error(s(459));var e=Nn;return Nn=null,e}function of(e){if(e===En||e===qu)throw Error(s(483))}var pl=!1;function kr(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function Jr(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function gl(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function vl(e,t,l){var a=e.updateQueue;if(a===null)return null;if(a=a.shared,(he&2)!==0){var u=a.pending;return u===null?t.next=t:(t.next=u.next,u.next=t),a.pending=t,t=Du(e),$o(e,null,l),t}return _u(e,a,t,l),Du(e)}function Tn(e,t,l){if(t=t.updateQueue,t!==null&&(t=t.shared,(l&4194048)!==0)){var a=t.lanes;a&=e.pendingLanes,l|=a,t.lanes=l,to(e,l)}}function $r(e,t){var l=e.updateQueue,a=e.alternate;if(a!==null&&(a=a.updateQueue,l===a)){var u=null,i=null;if(l=l.firstBaseUpdate,l!==null){do{var f={lane:l.lane,tag:l.tag,payload:l.payload,callback:null,next:null};i===null?u=i=f:i=i.next=f,l=l.next}while(l!==null);i===null?u=i=t:i=i.next=t}else u=i=t;l={baseState:a.baseState,firstBaseUpdate:u,lastBaseUpdate:i,shared:a.shared,callbacks:a.callbacks},e.updateQueue=l;return}e=l.lastBaseUpdate,e===null?l.firstBaseUpdate=t:e.next=t,l.lastBaseUpdate=t}var Fr=!1;function Rn(){if(Fr){var e=wa;if(e!==null)throw e}}function An(e,t,l,a){Fr=!1;var u=e.updateQueue;pl=!1;var i=u.firstBaseUpdate,f=u.lastBaseUpdate,m=u.shared.pending;if(m!==null){u.shared.pending=null;var b=m,R=b.next;b.next=null,f===null?i=R:f.next=R,f=b;var M=e.alternate;M!==null&&(M=M.updateQueue,m=M.lastBaseUpdate,m!==f&&(m===null?M.firstBaseUpdate=R:m.next=R,M.lastBaseUpdate=b))}if(i!==null){var H=u.baseState;f=0,M=R=b=null,m=i;do{var j=m.lane&-536870913,w=j!==m.lane;if(w?(se&j)===j:(a&j)===j){j!==0&&j===ja&&(Fr=!0),M!==null&&(M=M.next={lane:0,tag:m.tag,payload:m.payload,callback:null,next:null});e:{var ee=e,W=m;j=t;var ge=l;switch(W.tag){case 1:if(ee=W.payload,typeof ee=="function"){H=ee.call(ge,H,j);break e}H=ee;break e;case 3:ee.flags=ee.flags&-65537|128;case 0:if(ee=W.payload,j=typeof ee=="function"?ee.call(ge,H,j):ee,j==null)break e;H=x({},H,j);break e;case 2:pl=!0}}j=m.callback,j!==null&&(e.flags|=64,w&&(e.flags|=8192),w=u.callbacks,w===null?u.callbacks=[j]:w.push(j))}else w={lane:j,tag:m.tag,payload:m.payload,callback:m.callback,next:null},M===null?(R=M=w,b=H):M=M.next=w,f|=j;if(m=m.next,m===null){if(m=u.shared.pending,m===null)break;w=m,m=w.next,w.next=null,u.lastBaseUpdate=w,u.shared.pending=null}}while(!0);M===null&&(b=H),u.baseState=b,u.firstBaseUpdate=R,u.lastBaseUpdate=M,i===null&&(u.shared.lanes=0),Al|=f,e.lanes=f,e.memoizedState=H}}function ff(e,t){if(typeof e!="function")throw Error(s(191,e));e.call(t)}function df(e,t){var l=e.callbacks;if(l!==null)for(e.callbacks=null,e=0;e<l.length;e++)ff(l[e],t)}var _a=L(null),Yu=L(0);function hf(e,t){e=ll,X(Yu,e),X(_a,t),ll=e|t.baseLanes}function Wr(){X(Yu,ll),X(_a,_a.current)}function Pr(){ll=Yu.current,V(_a),V(Yu)}var bl=0,ne=null,ye=null,Ue=null,Gu=!1,Da=!1,Il=!1,Xu=0,On=0,Ca=null,jy=0;function Ce(){throw Error(s(321))}function Ir(e,t){if(t===null)return!1;for(var l=0;l<t.length&&l<e.length;l++)if(!dt(e[l],t[l]))return!1;return!0}function es(e,t,l,a,u,i){return bl=i,ne=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,z.H=e===null||e.memoizedState===null?Ff:Wf,Il=!1,i=l(a,u),Il=!1,Da&&(i=yf(t,l,a,u)),mf(e),i}function mf(e){z.H=Ju;var t=ye!==null&&ye.next!==null;if(bl=0,Ue=ye=ne=null,Gu=!1,On=0,Ca=null,t)throw Error(s(300));e===null||Ye||(e=e.dependencies,e!==null&&Uu(e)&&(Ye=!0))}function yf(e,t,l,a){ne=e;var u=0;do{if(Da&&(Ca=null),On=0,Da=!1,25<=u)throw Error(s(301));if(u+=1,Ue=ye=null,e.updateQueue!=null){var i=e.updateQueue;i.lastEffect=null,i.events=null,i.stores=null,i.memoCache!=null&&(i.memoCache.index=0)}z.H=Uy,i=t(l,a)}while(Da);return i}function wy(){var e=z.H,t=e.useState()[0];return t=typeof t.then=="function"?jn(t):t,e=e.useState()[0],(ye!==null?ye.memoizedState:null)!==e&&(ne.flags|=1024),t}function ts(){var e=Xu!==0;return Xu=0,e}function ls(e,t,l){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~l}function as(e){if(Gu){for(e=e.memoizedState;e!==null;){var t=e.queue;t!==null&&(t.pending=null),e=e.next}Gu=!1}bl=0,Ue=ye=ne=null,Da=!1,On=Xu=0,Ca=null}function at(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Ue===null?ne.memoizedState=Ue=e:Ue=Ue.next=e,Ue}function He(){if(ye===null){var e=ne.alternate;e=e!==null?e.memoizedState:null}else e=ye.next;var t=Ue===null?ne.memoizedState:Ue.next;if(t!==null)Ue=t,ye=e;else{if(e===null)throw ne.alternate===null?Error(s(467)):Error(s(310));ye=e,e={memoizedState:ye.memoizedState,baseState:ye.baseState,baseQueue:ye.baseQueue,queue:ye.queue,next:null},Ue===null?ne.memoizedState=Ue=e:Ue=Ue.next=e}return Ue}function ns(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function jn(e){var t=On;return On+=1,Ca===null&&(Ca=[]),e=sf(Ca,e,t),t=ne,(Ue===null?t.memoizedState:Ue.next)===null&&(t=t.alternate,z.H=t===null||t.memoizedState===null?Ff:Wf),e}function Qu(e){if(e!==null&&typeof e=="object"){if(typeof e.then=="function")return jn(e);if(e.$$typeof===K)return $e(e)}throw Error(s(438,String(e)))}function us(e){var t=null,l=ne.updateQueue;if(l!==null&&(t=l.memoCache),t==null){var a=ne.alternate;a!==null&&(a=a.updateQueue,a!==null&&(a=a.memoCache,a!=null&&(t={data:a.data.map(function(u){return u.slice()}),index:0})))}if(t==null&&(t={data:[],index:0}),l===null&&(l=ns(),ne.updateQueue=l),l.memoCache=t,l=t.data[t.index],l===void 0)for(l=t.data[t.index]=Array(e),a=0;a<e;a++)l[a]=st;return t.index++,l}function Ft(e,t){return typeof t=="function"?t(e):t}function Vu(e){var t=He();return is(t,ye,e)}function is(e,t,l){var a=e.queue;if(a===null)throw Error(s(311));a.lastRenderedReducer=l;var u=e.baseQueue,i=a.pending;if(i!==null){if(u!==null){var f=u.next;u.next=i.next,i.next=f}t.baseQueue=u=i,a.pending=null}if(i=e.baseState,u===null)e.memoizedState=i;else{t=u.next;var m=f=null,b=null,R=t,M=!1;do{var H=R.lane&-536870913;if(H!==R.lane?(se&H)===H:(bl&H)===H){var j=R.revertLane;if(j===0)b!==null&&(b=b.next={lane:0,revertLane:0,action:R.action,hasEagerState:R.hasEagerState,eagerState:R.eagerState,next:null}),H===ja&&(M=!0);else if((bl&j)===j){R=R.next,j===ja&&(M=!0);continue}else H={lane:0,revertLane:R.revertLane,action:R.action,hasEagerState:R.hasEagerState,eagerState:R.eagerState,next:null},b===null?(m=b=H,f=i):b=b.next=H,ne.lanes|=j,Al|=j;H=R.action,Il&&l(i,H),i=R.hasEagerState?R.eagerState:l(i,H)}else j={lane:H,revertLane:R.revertLane,action:R.action,hasEagerState:R.hasEagerState,eagerState:R.eagerState,next:null},b===null?(m=b=j,f=i):b=b.next=j,ne.lanes|=H,Al|=H;R=R.next}while(R!==null&&R!==t);if(b===null?f=i:b.next=m,!dt(i,e.memoizedState)&&(Ye=!0,M&&(l=wa,l!==null)))throw l;e.memoizedState=i,e.baseState=f,e.baseQueue=b,a.lastRenderedState=i}return u===null&&(a.lanes=0),[e.memoizedState,a.dispatch]}function rs(e){var t=He(),l=t.queue;if(l===null)throw Error(s(311));l.lastRenderedReducer=e;var a=l.dispatch,u=l.pending,i=t.memoizedState;if(u!==null){l.pending=null;var f=u=u.next;do i=e(i,f.action),f=f.next;while(f!==u);dt(i,t.memoizedState)||(Ye=!0),t.memoizedState=i,t.baseQueue===null&&(t.baseState=i),l.lastRenderedState=i}return[i,a]}function pf(e,t,l){var a=ne,u=He(),i=de;if(i){if(l===void 0)throw Error(s(407));l=l()}else l=t();var f=!dt((ye||u).memoizedState,l);f&&(u.memoizedState=l,Ye=!0),u=u.queue;var m=bf.bind(null,a,u,e);if(wn(2048,8,m,[e]),u.getSnapshot!==t||f||Ue!==null&&Ue.memoizedState.tag&1){if(a.flags|=2048,Ma(9,Zu(),vf.bind(null,a,u,l,t),null),Ee===null)throw Error(s(349));i||(bl&124)!==0||gf(a,t,l)}return l}function gf(e,t,l){e.flags|=16384,e={getSnapshot:t,value:l},t=ne.updateQueue,t===null?(t=ns(),ne.updateQueue=t,t.stores=[e]):(l=t.stores,l===null?t.stores=[e]:l.push(e))}function vf(e,t,l,a){t.value=l,t.getSnapshot=a,xf(t)&&Sf(e)}function bf(e,t,l){return l(function(){xf(t)&&Sf(e)})}function xf(e){var t=e.getSnapshot;e=e.value;try{var l=t();return!dt(e,l)}catch{return!0}}function Sf(e){var t=Ta(e,2);t!==null&&vt(t,e,2)}function ss(e){var t=at();if(typeof e=="function"){var l=e;if(e=l(),Il){dl(!0);try{l()}finally{dl(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Ft,lastRenderedState:e},t}function Ef(e,t,l,a){return e.baseState=l,is(e,ye,typeof a=="function"?a:Ft)}function _y(e,t,l,a,u){if(ku(e))throw Error(s(485));if(e=t.action,e!==null){var i={payload:u,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(f){i.listeners.push(f)}};z.T!==null?l(!0):i.isTransition=!1,a(i),l=t.pending,l===null?(i.next=t.pending=i,Nf(t,i)):(i.next=l.next,t.pending=l.next=i)}}function Nf(e,t){var l=t.action,a=t.payload,u=e.state;if(t.isTransition){var i=z.T,f={};z.T=f;try{var m=l(u,a),b=z.S;b!==null&&b(f,m),Tf(e,t,m)}catch(R){cs(e,t,R)}finally{z.T=i}}else try{i=l(u,a),Tf(e,t,i)}catch(R){cs(e,t,R)}}function Tf(e,t,l){l!==null&&typeof l=="object"&&typeof l.then=="function"?l.then(function(a){Rf(e,t,a)},function(a){return cs(e,t,a)}):Rf(e,t,l)}function Rf(e,t,l){t.status="fulfilled",t.value=l,Af(t),e.state=l,t=e.pending,t!==null&&(l=t.next,l===t?e.pending=null:(l=l.next,t.next=l,Nf(e,l)))}function cs(e,t,l){var a=e.pending;if(e.pending=null,a!==null){a=a.next;do t.status="rejected",t.reason=l,Af(t),t=t.next;while(t!==a)}e.action=null}function Af(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function Of(e,t){return t}function jf(e,t){if(de){var l=Ee.formState;if(l!==null){e:{var a=ne;if(de){if(_e){t:{for(var u=_e,i=Bt;u.nodeType!==8;){if(!i){u=null;break t}if(u=Dt(u.nextSibling),u===null){u=null;break t}}i=u.data,u=i==="F!"||i==="F"?u:null}if(u){_e=Dt(u.nextSibling),a=u.data==="F!";break e}}$l(a)}a=!1}a&&(t=l[0])}}return l=at(),l.memoizedState=l.baseState=t,a={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Of,lastRenderedState:t},l.queue=a,l=kf.bind(null,ne,a),a.dispatch=l,a=ss(!1),i=ms.bind(null,ne,!1,a.queue),a=at(),u={state:t,dispatch:null,action:e,pending:null},a.queue=u,l=_y.bind(null,ne,u,i,l),u.dispatch=l,a.memoizedState=e,[t,l,!1]}function wf(e){var t=He();return _f(t,ye,e)}function _f(e,t,l){if(t=is(e,t,Of)[0],e=Vu(Ft)[0],typeof t=="object"&&t!==null&&typeof t.then=="function")try{var a=jn(t)}catch(f){throw f===En?qu:f}else a=t;t=He();var u=t.queue,i=u.dispatch;return l!==t.memoizedState&&(ne.flags|=2048,Ma(9,Zu(),Dy.bind(null,u,l),null)),[a,i,e]}function Dy(e,t){e.action=t}function Df(e){var t=He(),l=ye;if(l!==null)return _f(t,l,e);He(),t=t.memoizedState,l=He();var a=l.queue.dispatch;return l.memoizedState=e,[t,a,!1]}function Ma(e,t,l,a){return e={tag:e,create:l,deps:a,inst:t,next:null},t=ne.updateQueue,t===null&&(t=ns(),ne.updateQueue=t),l=t.lastEffect,l===null?t.lastEffect=e.next=e:(a=l.next,l.next=e,e.next=a,t.lastEffect=e),e}function Zu(){return{destroy:void 0,resource:void 0}}function Cf(){return He().memoizedState}function Ku(e,t,l,a){var u=at();a=a===void 0?null:a,ne.flags|=e,u.memoizedState=Ma(1|t,Zu(),l,a)}function wn(e,t,l,a){var u=He();a=a===void 0?null:a;var i=u.memoizedState.inst;ye!==null&&a!==null&&Ir(a,ye.memoizedState.deps)?u.memoizedState=Ma(t,i,l,a):(ne.flags|=e,u.memoizedState=Ma(1|t,i,l,a))}function Mf(e,t){Ku(8390656,8,e,t)}function zf(e,t){wn(2048,8,e,t)}function Uf(e,t){return wn(4,2,e,t)}function Hf(e,t){return wn(4,4,e,t)}function Bf(e,t){if(typeof t=="function"){e=e();var l=t(e);return function(){typeof l=="function"?l():t(null)}}if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function qf(e,t,l){l=l!=null?l.concat([e]):null,wn(4,4,Bf.bind(null,t,e),l)}function os(){}function Lf(e,t){var l=He();t=t===void 0?null:t;var a=l.memoizedState;return t!==null&&Ir(t,a[1])?a[0]:(l.memoizedState=[e,t],e)}function Yf(e,t){var l=He();t=t===void 0?null:t;var a=l.memoizedState;if(t!==null&&Ir(t,a[1]))return a[0];if(a=e(),Il){dl(!0);try{e()}finally{dl(!1)}}return l.memoizedState=[a,t],a}function fs(e,t,l){return l===void 0||(bl&1073741824)!==0?e.memoizedState=t:(e.memoizedState=l,e=Qd(),ne.lanes|=e,Al|=e,l)}function Gf(e,t,l,a){return dt(l,t)?l:_a.current!==null?(e=fs(e,l,a),dt(e,t)||(Ye=!0),e):(bl&42)===0?(Ye=!0,e.memoizedState=l):(e=Qd(),ne.lanes|=e,Al|=e,t)}function Xf(e,t,l,a,u){var i=Q.p;Q.p=i!==0&&8>i?i:8;var f=z.T,m={};z.T=m,ms(e,!1,t,l);try{var b=u(),R=z.S;if(R!==null&&R(m,b),b!==null&&typeof b=="object"&&typeof b.then=="function"){var M=Oy(b,a);_n(e,t,M,gt(e))}else _n(e,t,a,gt(e))}catch(H){_n(e,t,{then:function(){},status:"rejected",reason:H},gt())}finally{Q.p=i,z.T=f}}function Cy(){}function ds(e,t,l,a){if(e.tag!==5)throw Error(s(476));var u=Qf(e).queue;Xf(e,u,t,I,l===null?Cy:function(){return Vf(e),l(a)})}function Qf(e){var t=e.memoizedState;if(t!==null)return t;t={memoizedState:I,baseState:I,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Ft,lastRenderedState:I},next:null};var l={};return t.next={memoizedState:l,baseState:l,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Ft,lastRenderedState:l},next:null},e.memoizedState=t,e=e.alternate,e!==null&&(e.memoizedState=t),t}function Vf(e){var t=Qf(e).next.queue;_n(e,t,{},gt())}function hs(){return $e(Jn)}function Zf(){return He().memoizedState}function Kf(){return He().memoizedState}function My(e){for(var t=e.return;t!==null;){switch(t.tag){case 24:case 3:var l=gt();e=gl(l);var a=vl(t,e,l);a!==null&&(vt(a,t,l),Tn(a,t,l)),t={cache:Qr()},e.payload=t;return}t=t.return}}function zy(e,t,l){var a=gt();l={lane:a,revertLane:0,action:l,hasEagerState:!1,eagerState:null,next:null},ku(e)?Jf(t,l):(l=Mr(e,t,l,a),l!==null&&(vt(l,e,a),$f(l,t,a)))}function kf(e,t,l){var a=gt();_n(e,t,l,a)}function _n(e,t,l,a){var u={lane:a,revertLane:0,action:l,hasEagerState:!1,eagerState:null,next:null};if(ku(e))Jf(t,u);else{var i=e.alternate;if(e.lanes===0&&(i===null||i.lanes===0)&&(i=t.lastRenderedReducer,i!==null))try{var f=t.lastRenderedState,m=i(f,l);if(u.hasEagerState=!0,u.eagerState=m,dt(m,f))return _u(e,t,u,0),Ee===null&&wu(),!1}catch{}finally{}if(l=Mr(e,t,u,a),l!==null)return vt(l,e,a),$f(l,t,a),!0}return!1}function ms(e,t,l,a){if(a={lane:2,revertLane:Ks(),action:a,hasEagerState:!1,eagerState:null,next:null},ku(e)){if(t)throw Error(s(479))}else t=Mr(e,l,a,2),t!==null&&vt(t,e,2)}function ku(e){var t=e.alternate;return e===ne||t!==null&&t===ne}function Jf(e,t){Da=Gu=!0;var l=e.pending;l===null?t.next=t:(t.next=l.next,l.next=t),e.pending=t}function $f(e,t,l){if((l&4194048)!==0){var a=t.lanes;a&=e.pendingLanes,l|=a,t.lanes=l,to(e,l)}}var Ju={readContext:$e,use:Qu,useCallback:Ce,useContext:Ce,useEffect:Ce,useImperativeHandle:Ce,useLayoutEffect:Ce,useInsertionEffect:Ce,useMemo:Ce,useReducer:Ce,useRef:Ce,useState:Ce,useDebugValue:Ce,useDeferredValue:Ce,useTransition:Ce,useSyncExternalStore:Ce,useId:Ce,useHostTransitionStatus:Ce,useFormState:Ce,useActionState:Ce,useOptimistic:Ce,useMemoCache:Ce,useCacheRefresh:Ce},Ff={readContext:$e,use:Qu,useCallback:function(e,t){return at().memoizedState=[e,t===void 0?null:t],e},useContext:$e,useEffect:Mf,useImperativeHandle:function(e,t,l){l=l!=null?l.concat([e]):null,Ku(4194308,4,Bf.bind(null,t,e),l)},useLayoutEffect:function(e,t){return Ku(4194308,4,e,t)},useInsertionEffect:function(e,t){Ku(4,2,e,t)},useMemo:function(e,t){var l=at();t=t===void 0?null:t;var a=e();if(Il){dl(!0);try{e()}finally{dl(!1)}}return l.memoizedState=[a,t],a},useReducer:function(e,t,l){var a=at();if(l!==void 0){var u=l(t);if(Il){dl(!0);try{l(t)}finally{dl(!1)}}}else u=t;return a.memoizedState=a.baseState=u,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:u},a.queue=e,e=e.dispatch=zy.bind(null,ne,e),[a.memoizedState,e]},useRef:function(e){var t=at();return e={current:e},t.memoizedState=e},useState:function(e){e=ss(e);var t=e.queue,l=kf.bind(null,ne,t);return t.dispatch=l,[e.memoizedState,l]},useDebugValue:os,useDeferredValue:function(e,t){var l=at();return fs(l,e,t)},useTransition:function(){var e=ss(!1);return e=Xf.bind(null,ne,e.queue,!0,!1),at().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,l){var a=ne,u=at();if(de){if(l===void 0)throw Error(s(407));l=l()}else{if(l=t(),Ee===null)throw Error(s(349));(se&124)!==0||gf(a,t,l)}u.memoizedState=l;var i={value:l,getSnapshot:t};return u.queue=i,Mf(bf.bind(null,a,i,e),[e]),a.flags|=2048,Ma(9,Zu(),vf.bind(null,a,i,l,t),null),l},useId:function(){var e=at(),t=Ee.identifierPrefix;if(de){var l=kt,a=Kt;l=(a&~(1<<32-ft(a)-1)).toString(32)+l,t="«"+t+"R"+l,l=Xu++,0<l&&(t+="H"+l.toString(32)),t+="»"}else l=jy++,t="«"+t+"r"+l.toString(32)+"»";return e.memoizedState=t},useHostTransitionStatus:hs,useFormState:jf,useActionState:jf,useOptimistic:function(e){var t=at();t.memoizedState=t.baseState=e;var l={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=l,t=ms.bind(null,ne,!0,l),l.dispatch=t,[e,t]},useMemoCache:us,useCacheRefresh:function(){return at().memoizedState=My.bind(null,ne)}},Wf={readContext:$e,use:Qu,useCallback:Lf,useContext:$e,useEffect:zf,useImperativeHandle:qf,useInsertionEffect:Uf,useLayoutEffect:Hf,useMemo:Yf,useReducer:Vu,useRef:Cf,useState:function(){return Vu(Ft)},useDebugValue:os,useDeferredValue:function(e,t){var l=He();return Gf(l,ye.memoizedState,e,t)},useTransition:function(){var e=Vu(Ft)[0],t=He().memoizedState;return[typeof e=="boolean"?e:jn(e),t]},useSyncExternalStore:pf,useId:Zf,useHostTransitionStatus:hs,useFormState:wf,useActionState:wf,useOptimistic:function(e,t){var l=He();return Ef(l,ye,e,t)},useMemoCache:us,useCacheRefresh:Kf},Uy={readContext:$e,use:Qu,useCallback:Lf,useContext:$e,useEffect:zf,useImperativeHandle:qf,useInsertionEffect:Uf,useLayoutEffect:Hf,useMemo:Yf,useReducer:rs,useRef:Cf,useState:function(){return rs(Ft)},useDebugValue:os,useDeferredValue:function(e,t){var l=He();return ye===null?fs(l,e,t):Gf(l,ye.memoizedState,e,t)},useTransition:function(){var e=rs(Ft)[0],t=He().memoizedState;return[typeof e=="boolean"?e:jn(e),t]},useSyncExternalStore:pf,useId:Zf,useHostTransitionStatus:hs,useFormState:Df,useActionState:Df,useOptimistic:function(e,t){var l=He();return ye!==null?Ef(l,ye,e,t):(l.baseState=e,[e,l.queue.dispatch])},useMemoCache:us,useCacheRefresh:Kf},za=null,Dn=0;function $u(e){var t=Dn;return Dn+=1,za===null&&(za=[]),sf(za,e,t)}function Cn(e,t){t=t.props.ref,e.ref=t!==void 0?t:null}function Fu(e,t){throw t.$$typeof===O?Error(s(525)):(e=Object.prototype.toString.call(t),Error(s(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e)))}function Pf(e){var t=e._init;return t(e._payload)}function If(e){function t(N,E){if(e){var T=N.deletions;T===null?(N.deletions=[E],N.flags|=16):T.push(E)}}function l(N,E){if(!e)return null;for(;E!==null;)t(N,E),E=E.sibling;return null}function a(N){for(var E=new Map;N!==null;)N.key!==null?E.set(N.key,N):E.set(N.index,N),N=N.sibling;return E}function u(N,E){return N=Zt(N,E),N.index=0,N.sibling=null,N}function i(N,E,T){return N.index=T,e?(T=N.alternate,T!==null?(T=T.index,T<E?(N.flags|=67108866,E):T):(N.flags|=67108866,E)):(N.flags|=1048576,E)}function f(N){return e&&N.alternate===null&&(N.flags|=67108866),N}function m(N,E,T,U){return E===null||E.tag!==6?(E=Ur(T,N.mode,U),E.return=N,E):(E=u(E,T),E.return=N,E)}function b(N,E,T,U){var Z=T.type;return Z===D?M(N,E,T.props.children,U,T.key):E!==null&&(E.elementType===Z||typeof Z=="object"&&Z!==null&&Z.$$typeof===je&&Pf(Z)===E.type)?(E=u(E,T.props),Cn(E,T),E.return=N,E):(E=Cu(T.type,T.key,T.props,null,N.mode,U),Cn(E,T),E.return=N,E)}function R(N,E,T,U){return E===null||E.tag!==4||E.stateNode.containerInfo!==T.containerInfo||E.stateNode.implementation!==T.implementation?(E=Hr(T,N.mode,U),E.return=N,E):(E=u(E,T.children||[]),E.return=N,E)}function M(N,E,T,U,Z){return E===null||E.tag!==7?(E=Zl(T,N.mode,U,Z),E.return=N,E):(E=u(E,T),E.return=N,E)}function H(N,E,T){if(typeof E=="string"&&E!==""||typeof E=="number"||typeof E=="bigint")return E=Ur(""+E,N.mode,T),E.return=N,E;if(typeof E=="object"&&E!==null){switch(E.$$typeof){case C:return T=Cu(E.type,E.key,E.props,null,N.mode,T),Cn(T,E),T.return=N,T;case G:return E=Hr(E,N.mode,T),E.return=N,E;case je:var U=E._init;return E=U(E._payload),H(N,E,T)}if(ke(E)||Ke(E))return E=Zl(E,N.mode,T,null),E.return=N,E;if(typeof E.then=="function")return H(N,$u(E),T);if(E.$$typeof===K)return H(N,Hu(N,E),T);Fu(N,E)}return null}function j(N,E,T,U){var Z=E!==null?E.key:null;if(typeof T=="string"&&T!==""||typeof T=="number"||typeof T=="bigint")return Z!==null?null:m(N,E,""+T,U);if(typeof T=="object"&&T!==null){switch(T.$$typeof){case C:return T.key===Z?b(N,E,T,U):null;case G:return T.key===Z?R(N,E,T,U):null;case je:return Z=T._init,T=Z(T._payload),j(N,E,T,U)}if(ke(T)||Ke(T))return Z!==null?null:M(N,E,T,U,null);if(typeof T.then=="function")return j(N,E,$u(T),U);if(T.$$typeof===K)return j(N,E,Hu(N,T),U);Fu(N,T)}return null}function w(N,E,T,U,Z){if(typeof U=="string"&&U!==""||typeof U=="number"||typeof U=="bigint")return N=N.get(T)||null,m(E,N,""+U,Z);if(typeof U=="object"&&U!==null){switch(U.$$typeof){case C:return N=N.get(U.key===null?T:U.key)||null,b(E,N,U,Z);case G:return N=N.get(U.key===null?T:U.key)||null,R(E,N,U,Z);case je:var ue=U._init;return U=ue(U._payload),w(N,E,T,U,Z)}if(ke(U)||Ke(U))return N=N.get(T)||null,M(E,N,U,Z,null);if(typeof U.then=="function")return w(N,E,T,$u(U),Z);if(U.$$typeof===K)return w(N,E,T,Hu(E,U),Z);Fu(E,U)}return null}function ee(N,E,T,U){for(var Z=null,ue=null,J=E,P=E=0,Xe=null;J!==null&&P<T.length;P++){J.index>P?(Xe=J,J=null):Xe=J.sibling;var fe=j(N,J,T[P],U);if(fe===null){J===null&&(J=Xe);break}e&&J&&fe.alternate===null&&t(N,J),E=i(fe,E,P),ue===null?Z=fe:ue.sibling=fe,ue=fe,J=Xe}if(P===T.length)return l(N,J),de&&kl(N,P),Z;if(J===null){for(;P<T.length;P++)J=H(N,T[P],U),J!==null&&(E=i(J,E,P),ue===null?Z=J:ue.sibling=J,ue=J);return de&&kl(N,P),Z}for(J=a(J);P<T.length;P++)Xe=w(J,N,P,T[P],U),Xe!==null&&(e&&Xe.alternate!==null&&J.delete(Xe.key===null?P:Xe.key),E=i(Xe,E,P),ue===null?Z=Xe:ue.sibling=Xe,ue=Xe);return e&&J.forEach(function(Ul){return t(N,Ul)}),de&&kl(N,P),Z}function W(N,E,T,U){if(T==null)throw Error(s(151));for(var Z=null,ue=null,J=E,P=E=0,Xe=null,fe=T.next();J!==null&&!fe.done;P++,fe=T.next()){J.index>P?(Xe=J,J=null):Xe=J.sibling;var Ul=j(N,J,fe.value,U);if(Ul===null){J===null&&(J=Xe);break}e&&J&&Ul.alternate===null&&t(N,J),E=i(Ul,E,P),ue===null?Z=Ul:ue.sibling=Ul,ue=Ul,J=Xe}if(fe.done)return l(N,J),de&&kl(N,P),Z;if(J===null){for(;!fe.done;P++,fe=T.next())fe=H(N,fe.value,U),fe!==null&&(E=i(fe,E,P),ue===null?Z=fe:ue.sibling=fe,ue=fe);return de&&kl(N,P),Z}for(J=a(J);!fe.done;P++,fe=T.next())fe=w(J,N,P,fe.value,U),fe!==null&&(e&&fe.alternate!==null&&J.delete(fe.key===null?P:fe.key),E=i(fe,E,P),ue===null?Z=fe:ue.sibling=fe,ue=fe);return e&&J.forEach(function(Hp){return t(N,Hp)}),de&&kl(N,P),Z}function ge(N,E,T,U){if(typeof T=="object"&&T!==null&&T.type===D&&T.key===null&&(T=T.props.children),typeof T=="object"&&T!==null){switch(T.$$typeof){case C:e:{for(var Z=T.key;E!==null;){if(E.key===Z){if(Z=T.type,Z===D){if(E.tag===7){l(N,E.sibling),U=u(E,T.props.children),U.return=N,N=U;break e}}else if(E.elementType===Z||typeof Z=="object"&&Z!==null&&Z.$$typeof===je&&Pf(Z)===E.type){l(N,E.sibling),U=u(E,T.props),Cn(U,T),U.return=N,N=U;break e}l(N,E);break}else t(N,E);E=E.sibling}T.type===D?(U=Zl(T.props.children,N.mode,U,T.key),U.return=N,N=U):(U=Cu(T.type,T.key,T.props,null,N.mode,U),Cn(U,T),U.return=N,N=U)}return f(N);case G:e:{for(Z=T.key;E!==null;){if(E.key===Z)if(E.tag===4&&E.stateNode.containerInfo===T.containerInfo&&E.stateNode.implementation===T.implementation){l(N,E.sibling),U=u(E,T.children||[]),U.return=N,N=U;break e}else{l(N,E);break}else t(N,E);E=E.sibling}U=Hr(T,N.mode,U),U.return=N,N=U}return f(N);case je:return Z=T._init,T=Z(T._payload),ge(N,E,T,U)}if(ke(T))return ee(N,E,T,U);if(Ke(T)){if(Z=Ke(T),typeof Z!="function")throw Error(s(150));return T=Z.call(T),W(N,E,T,U)}if(typeof T.then=="function")return ge(N,E,$u(T),U);if(T.$$typeof===K)return ge(N,E,Hu(N,T),U);Fu(N,T)}return typeof T=="string"&&T!==""||typeof T=="number"||typeof T=="bigint"?(T=""+T,E!==null&&E.tag===6?(l(N,E.sibling),U=u(E,T),U.return=N,N=U):(l(N,E),U=Ur(T,N.mode,U),U.return=N,N=U),f(N)):l(N,E)}return function(N,E,T,U){try{Dn=0;var Z=ge(N,E,T,U);return za=null,Z}catch(J){if(J===En||J===qu)throw J;var ue=ht(29,J,null,N.mode);return ue.lanes=U,ue.return=N,ue}finally{}}}var Ua=If(!0),ed=If(!1),At=L(null),qt=null;function xl(e){var t=e.alternate;X(qe,qe.current&1),X(At,e),qt===null&&(t===null||_a.current!==null||t.memoizedState!==null)&&(qt=e)}function td(e){if(e.tag===22){if(X(qe,qe.current),X(At,e),qt===null){var t=e.alternate;t!==null&&t.memoizedState!==null&&(qt=e)}}else Sl()}function Sl(){X(qe,qe.current),X(At,At.current)}function Wt(e){V(At),qt===e&&(qt=null),V(qe)}var qe=L(0);function Wu(e){for(var t=e;t!==null;){if(t.tag===13){var l=t.memoizedState;if(l!==null&&(l=l.dehydrated,l===null||l.data==="$?"||nc(l)))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function ys(e,t,l,a){t=e.memoizedState,l=l(a,t),l=l==null?t:x({},t,l),e.memoizedState=l,e.lanes===0&&(e.updateQueue.baseState=l)}var ps={enqueueSetState:function(e,t,l){e=e._reactInternals;var a=gt(),u=gl(a);u.payload=t,l!=null&&(u.callback=l),t=vl(e,u,a),t!==null&&(vt(t,e,a),Tn(t,e,a))},enqueueReplaceState:function(e,t,l){e=e._reactInternals;var a=gt(),u=gl(a);u.tag=1,u.payload=t,l!=null&&(u.callback=l),t=vl(e,u,a),t!==null&&(vt(t,e,a),Tn(t,e,a))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var l=gt(),a=gl(l);a.tag=2,t!=null&&(a.callback=t),t=vl(e,a,l),t!==null&&(vt(t,e,l),Tn(t,e,l))}};function ld(e,t,l,a,u,i,f){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(a,i,f):t.prototype&&t.prototype.isPureReactComponent?!mn(l,a)||!mn(u,i):!0}function ad(e,t,l,a){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(l,a),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(l,a),t.state!==e&&ps.enqueueReplaceState(t,t.state,null)}function ea(e,t){var l=t;if("ref"in t){l={};for(var a in t)a!=="ref"&&(l[a]=t[a])}if(e=e.defaultProps){l===t&&(l=x({},l));for(var u in e)l[u]===void 0&&(l[u]=e[u])}return l}var Pu=typeof reportError=="function"?reportError:function(e){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof e=="object"&&e!==null&&typeof e.message=="string"?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",e);return}console.error(e)};function nd(e){Pu(e)}function ud(e){console.error(e)}function id(e){Pu(e)}function Iu(e,t){try{var l=e.onUncaughtError;l(t.value,{componentStack:t.stack})}catch(a){setTimeout(function(){throw a})}}function rd(e,t,l){try{var a=e.onCaughtError;a(l.value,{componentStack:l.stack,errorBoundary:t.tag===1?t.stateNode:null})}catch(u){setTimeout(function(){throw u})}}function gs(e,t,l){return l=gl(l),l.tag=3,l.payload={element:null},l.callback=function(){Iu(e,t)},l}function sd(e){return e=gl(e),e.tag=3,e}function cd(e,t,l,a){var u=l.type.getDerivedStateFromError;if(typeof u=="function"){var i=a.value;e.payload=function(){return u(i)},e.callback=function(){rd(t,l,a)}}var f=l.stateNode;f!==null&&typeof f.componentDidCatch=="function"&&(e.callback=function(){rd(t,l,a),typeof u!="function"&&(Ol===null?Ol=new Set([this]):Ol.add(this));var m=a.stack;this.componentDidCatch(a.value,{componentStack:m!==null?m:""})})}function Hy(e,t,l,a,u){if(l.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){if(t=l.alternate,t!==null&&bn(t,l,u,!0),l=At.current,l!==null){switch(l.tag){case 13:return qt===null?Gs():l.alternate===null&&De===0&&(De=3),l.flags&=-257,l.flags|=65536,l.lanes=u,a===Kr?l.flags|=16384:(t=l.updateQueue,t===null?l.updateQueue=new Set([a]):t.add(a),Qs(e,a,u)),!1;case 22:return l.flags|=65536,a===Kr?l.flags|=16384:(t=l.updateQueue,t===null?(t={transitions:null,markerInstances:null,retryQueue:new Set([a])},l.updateQueue=t):(l=t.retryQueue,l===null?t.retryQueue=new Set([a]):l.add(a)),Qs(e,a,u)),!1}throw Error(s(435,l.tag))}return Qs(e,a,u),Gs(),!1}if(de)return t=At.current,t!==null?((t.flags&65536)===0&&(t.flags|=256),t.flags|=65536,t.lanes=u,a!==Lr&&(e=Error(s(422),{cause:a}),vn(Et(e,l)))):(a!==Lr&&(t=Error(s(423),{cause:a}),vn(Et(t,l))),e=e.current.alternate,e.flags|=65536,u&=-u,e.lanes|=u,a=Et(a,l),u=gs(e.stateNode,a,u),$r(e,u),De!==4&&(De=2)),!1;var i=Error(s(520),{cause:a});if(i=Et(i,l),Ln===null?Ln=[i]:Ln.push(i),De!==4&&(De=2),t===null)return!0;a=Et(a,l),l=t;do{switch(l.tag){case 3:return l.flags|=65536,e=u&-u,l.lanes|=e,e=gs(l.stateNode,a,e),$r(l,e),!1;case 1:if(t=l.type,i=l.stateNode,(l.flags&128)===0&&(typeof t.getDerivedStateFromError=="function"||i!==null&&typeof i.componentDidCatch=="function"&&(Ol===null||!Ol.has(i))))return l.flags|=65536,u&=-u,l.lanes|=u,u=sd(u),cd(u,e,l,a),$r(l,u),!1}l=l.return}while(l!==null);return!1}var od=Error(s(461)),Ye=!1;function Qe(e,t,l,a){t.child=e===null?ed(t,null,l,a):Ua(t,e.child,l,a)}function fd(e,t,l,a,u){l=l.render;var i=t.ref;if("ref"in a){var f={};for(var m in a)m!=="ref"&&(f[m]=a[m])}else f=a;return Wl(t),a=es(e,t,l,f,i,u),m=ts(),e!==null&&!Ye?(ls(e,t,u),Pt(e,t,u)):(de&&m&&Br(t),t.flags|=1,Qe(e,t,a,u),t.child)}function dd(e,t,l,a,u){if(e===null){var i=l.type;return typeof i=="function"&&!zr(i)&&i.defaultProps===void 0&&l.compare===null?(t.tag=15,t.type=i,hd(e,t,i,a,u)):(e=Cu(l.type,null,a,t,t.mode,u),e.ref=t.ref,e.return=t,t.child=e)}if(i=e.child,!Rs(e,u)){var f=i.memoizedProps;if(l=l.compare,l=l!==null?l:mn,l(f,a)&&e.ref===t.ref)return Pt(e,t,u)}return t.flags|=1,e=Zt(i,a),e.ref=t.ref,e.return=t,t.child=e}function hd(e,t,l,a,u){if(e!==null){var i=e.memoizedProps;if(mn(i,a)&&e.ref===t.ref)if(Ye=!1,t.pendingProps=a=i,Rs(e,u))(e.flags&131072)!==0&&(Ye=!0);else return t.lanes=e.lanes,Pt(e,t,u)}return vs(e,t,l,a,u)}function md(e,t,l){var a=t.pendingProps,u=a.children,i=e!==null?e.memoizedState:null;if(a.mode==="hidden"){if((t.flags&128)!==0){if(a=i!==null?i.baseLanes|l:l,e!==null){for(u=t.child=e.child,i=0;u!==null;)i=i|u.lanes|u.childLanes,u=u.sibling;t.childLanes=i&~a}else t.childLanes=0,t.child=null;return yd(e,t,a,l)}if((l&536870912)!==0)t.memoizedState={baseLanes:0,cachePool:null},e!==null&&Bu(t,i!==null?i.cachePool:null),i!==null?hf(t,i):Wr(),td(t);else return t.lanes=t.childLanes=536870912,yd(e,t,i!==null?i.baseLanes|l:l,l)}else i!==null?(Bu(t,i.cachePool),hf(t,i),Sl(),t.memoizedState=null):(e!==null&&Bu(t,null),Wr(),Sl());return Qe(e,t,u,l),t.child}function yd(e,t,l,a){var u=Zr();return u=u===null?null:{parent:Be._currentValue,pool:u},t.memoizedState={baseLanes:l,cachePool:u},e!==null&&Bu(t,null),Wr(),td(t),e!==null&&bn(e,t,a,!0),null}function ei(e,t){var l=t.ref;if(l===null)e!==null&&e.ref!==null&&(t.flags|=4194816);else{if(typeof l!="function"&&typeof l!="object")throw Error(s(284));(e===null||e.ref!==l)&&(t.flags|=4194816)}}function vs(e,t,l,a,u){return Wl(t),l=es(e,t,l,a,void 0,u),a=ts(),e!==null&&!Ye?(ls(e,t,u),Pt(e,t,u)):(de&&a&&Br(t),t.flags|=1,Qe(e,t,l,u),t.child)}function pd(e,t,l,a,u,i){return Wl(t),t.updateQueue=null,l=yf(t,a,l,u),mf(e),a=ts(),e!==null&&!Ye?(ls(e,t,i),Pt(e,t,i)):(de&&a&&Br(t),t.flags|=1,Qe(e,t,l,i),t.child)}function gd(e,t,l,a,u){if(Wl(t),t.stateNode===null){var i=Ra,f=l.contextType;typeof f=="object"&&f!==null&&(i=$e(f)),i=new l(a,i),t.memoizedState=i.state!==null&&i.state!==void 0?i.state:null,i.updater=ps,t.stateNode=i,i._reactInternals=t,i=t.stateNode,i.props=a,i.state=t.memoizedState,i.refs={},kr(t),f=l.contextType,i.context=typeof f=="object"&&f!==null?$e(f):Ra,i.state=t.memoizedState,f=l.getDerivedStateFromProps,typeof f=="function"&&(ys(t,l,f,a),i.state=t.memoizedState),typeof l.getDerivedStateFromProps=="function"||typeof i.getSnapshotBeforeUpdate=="function"||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(f=i.state,typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount(),f!==i.state&&ps.enqueueReplaceState(i,i.state,null),An(t,a,i,u),Rn(),i.state=t.memoizedState),typeof i.componentDidMount=="function"&&(t.flags|=4194308),a=!0}else if(e===null){i=t.stateNode;var m=t.memoizedProps,b=ea(l,m);i.props=b;var R=i.context,M=l.contextType;f=Ra,typeof M=="object"&&M!==null&&(f=$e(M));var H=l.getDerivedStateFromProps;M=typeof H=="function"||typeof i.getSnapshotBeforeUpdate=="function",m=t.pendingProps!==m,M||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(m||R!==f)&&ad(t,i,a,f),pl=!1;var j=t.memoizedState;i.state=j,An(t,a,i,u),Rn(),R=t.memoizedState,m||j!==R||pl?(typeof H=="function"&&(ys(t,l,H,a),R=t.memoizedState),(b=pl||ld(t,l,b,a,j,R,f))?(M||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount()),typeof i.componentDidMount=="function"&&(t.flags|=4194308)):(typeof i.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=a,t.memoizedState=R),i.props=a,i.state=R,i.context=f,a=b):(typeof i.componentDidMount=="function"&&(t.flags|=4194308),a=!1)}else{i=t.stateNode,Jr(e,t),f=t.memoizedProps,M=ea(l,f),i.props=M,H=t.pendingProps,j=i.context,R=l.contextType,b=Ra,typeof R=="object"&&R!==null&&(b=$e(R)),m=l.getDerivedStateFromProps,(R=typeof m=="function"||typeof i.getSnapshotBeforeUpdate=="function")||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(f!==H||j!==b)&&ad(t,i,a,b),pl=!1,j=t.memoizedState,i.state=j,An(t,a,i,u),Rn();var w=t.memoizedState;f!==H||j!==w||pl||e!==null&&e.dependencies!==null&&Uu(e.dependencies)?(typeof m=="function"&&(ys(t,l,m,a),w=t.memoizedState),(M=pl||ld(t,l,M,a,j,w,b)||e!==null&&e.dependencies!==null&&Uu(e.dependencies))?(R||typeof i.UNSAFE_componentWillUpdate!="function"&&typeof i.componentWillUpdate!="function"||(typeof i.componentWillUpdate=="function"&&i.componentWillUpdate(a,w,b),typeof i.UNSAFE_componentWillUpdate=="function"&&i.UNSAFE_componentWillUpdate(a,w,b)),typeof i.componentDidUpdate=="function"&&(t.flags|=4),typeof i.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof i.componentDidUpdate!="function"||f===e.memoizedProps&&j===e.memoizedState||(t.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||f===e.memoizedProps&&j===e.memoizedState||(t.flags|=1024),t.memoizedProps=a,t.memoizedState=w),i.props=a,i.state=w,i.context=b,a=M):(typeof i.componentDidUpdate!="function"||f===e.memoizedProps&&j===e.memoizedState||(t.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||f===e.memoizedProps&&j===e.memoizedState||(t.flags|=1024),a=!1)}return i=a,ei(e,t),a=(t.flags&128)!==0,i||a?(i=t.stateNode,l=a&&typeof l.getDerivedStateFromError!="function"?null:i.render(),t.flags|=1,e!==null&&a?(t.child=Ua(t,e.child,null,u),t.child=Ua(t,null,l,u)):Qe(e,t,l,u),t.memoizedState=i.state,e=t.child):e=Pt(e,t,u),e}function vd(e,t,l,a){return gn(),t.flags|=256,Qe(e,t,l,a),t.child}var bs={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function xs(e){return{baseLanes:e,cachePool:nf()}}function Ss(e,t,l){return e=e!==null?e.childLanes&~l:0,t&&(e|=Ot),e}function bd(e,t,l){var a=t.pendingProps,u=!1,i=(t.flags&128)!==0,f;if((f=i)||(f=e!==null&&e.memoizedState===null?!1:(qe.current&2)!==0),f&&(u=!0,t.flags&=-129),f=(t.flags&32)!==0,t.flags&=-33,e===null){if(de){if(u?xl(t):Sl(),de){var m=_e,b;if(b=m){e:{for(b=m,m=Bt;b.nodeType!==8;){if(!m){m=null;break e}if(b=Dt(b.nextSibling),b===null){m=null;break e}}m=b}m!==null?(t.memoizedState={dehydrated:m,treeContext:Kl!==null?{id:Kt,overflow:kt}:null,retryLane:536870912,hydrationErrors:null},b=ht(18,null,null,0),b.stateNode=m,b.return=t,t.child=b,Pe=t,_e=null,b=!0):b=!1}b||$l(t)}if(m=t.memoizedState,m!==null&&(m=m.dehydrated,m!==null))return nc(m)?t.lanes=32:t.lanes=536870912,null;Wt(t)}return m=a.children,a=a.fallback,u?(Sl(),u=t.mode,m=ti({mode:"hidden",children:m},u),a=Zl(a,u,l,null),m.return=t,a.return=t,m.sibling=a,t.child=m,u=t.child,u.memoizedState=xs(l),u.childLanes=Ss(e,f,l),t.memoizedState=bs,a):(xl(t),Es(t,m))}if(b=e.memoizedState,b!==null&&(m=b.dehydrated,m!==null)){if(i)t.flags&256?(xl(t),t.flags&=-257,t=Ns(e,t,l)):t.memoizedState!==null?(Sl(),t.child=e.child,t.flags|=128,t=null):(Sl(),u=a.fallback,m=t.mode,a=ti({mode:"visible",children:a.children},m),u=Zl(u,m,l,null),u.flags|=2,a.return=t,u.return=t,a.sibling=u,t.child=a,Ua(t,e.child,null,l),a=t.child,a.memoizedState=xs(l),a.childLanes=Ss(e,f,l),t.memoizedState=bs,t=u);else if(xl(t),nc(m)){if(f=m.nextSibling&&m.nextSibling.dataset,f)var R=f.dgst;f=R,a=Error(s(419)),a.stack="",a.digest=f,vn({value:a,source:null,stack:null}),t=Ns(e,t,l)}else if(Ye||bn(e,t,l,!1),f=(l&e.childLanes)!==0,Ye||f){if(f=Ee,f!==null&&(a=l&-l,a=(a&42)!==0?1:ur(a),a=(a&(f.suspendedLanes|l))!==0?0:a,a!==0&&a!==b.retryLane))throw b.retryLane=a,Ta(e,a),vt(f,e,a),od;m.data==="$?"||Gs(),t=Ns(e,t,l)}else m.data==="$?"?(t.flags|=192,t.child=e.child,t=null):(e=b.treeContext,_e=Dt(m.nextSibling),Pe=t,de=!0,Jl=null,Bt=!1,e!==null&&(Tt[Rt++]=Kt,Tt[Rt++]=kt,Tt[Rt++]=Kl,Kt=e.id,kt=e.overflow,Kl=t),t=Es(t,a.children),t.flags|=4096);return t}return u?(Sl(),u=a.fallback,m=t.mode,b=e.child,R=b.sibling,a=Zt(b,{mode:"hidden",children:a.children}),a.subtreeFlags=b.subtreeFlags&65011712,R!==null?u=Zt(R,u):(u=Zl(u,m,l,null),u.flags|=2),u.return=t,a.return=t,a.sibling=u,t.child=a,a=u,u=t.child,m=e.child.memoizedState,m===null?m=xs(l):(b=m.cachePool,b!==null?(R=Be._currentValue,b=b.parent!==R?{parent:R,pool:R}:b):b=nf(),m={baseLanes:m.baseLanes|l,cachePool:b}),u.memoizedState=m,u.childLanes=Ss(e,f,l),t.memoizedState=bs,a):(xl(t),l=e.child,e=l.sibling,l=Zt(l,{mode:"visible",children:a.children}),l.return=t,l.sibling=null,e!==null&&(f=t.deletions,f===null?(t.deletions=[e],t.flags|=16):f.push(e)),t.child=l,t.memoizedState=null,l)}function Es(e,t){return t=ti({mode:"visible",children:t},e.mode),t.return=e,e.child=t}function ti(e,t){return e=ht(22,e,null,t),e.lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function Ns(e,t,l){return Ua(t,e.child,null,l),e=Es(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function xd(e,t,l){e.lanes|=t;var a=e.alternate;a!==null&&(a.lanes|=t),Gr(e.return,t,l)}function Ts(e,t,l,a,u){var i=e.memoizedState;i===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:a,tail:l,tailMode:u}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=a,i.tail=l,i.tailMode=u)}function Sd(e,t,l){var a=t.pendingProps,u=a.revealOrder,i=a.tail;if(Qe(e,t,a.children,l),a=qe.current,(a&2)!==0)a=a&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&xd(e,l,t);else if(e.tag===19)xd(e,l,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}a&=1}switch(X(qe,a),u){case"forwards":for(l=t.child,u=null;l!==null;)e=l.alternate,e!==null&&Wu(e)===null&&(u=l),l=l.sibling;l=u,l===null?(u=t.child,t.child=null):(u=l.sibling,l.sibling=null),Ts(t,!1,u,l,i);break;case"backwards":for(l=null,u=t.child,t.child=null;u!==null;){if(e=u.alternate,e!==null&&Wu(e)===null){t.child=u;break}e=u.sibling,u.sibling=l,l=u,u=e}Ts(t,!0,l,null,i);break;case"together":Ts(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Pt(e,t,l){if(e!==null&&(t.dependencies=e.dependencies),Al|=t.lanes,(l&t.childLanes)===0)if(e!==null){if(bn(e,t,l,!1),(l&t.childLanes)===0)return null}else return null;if(e!==null&&t.child!==e.child)throw Error(s(153));if(t.child!==null){for(e=t.child,l=Zt(e,e.pendingProps),t.child=l,l.return=t;e.sibling!==null;)e=e.sibling,l=l.sibling=Zt(e,e.pendingProps),l.return=t;l.sibling=null}return t.child}function Rs(e,t){return(e.lanes&t)!==0?!0:(e=e.dependencies,!!(e!==null&&Uu(e)))}function By(e,t,l){switch(t.tag){case 3:Te(t,t.stateNode.containerInfo),yl(t,Be,e.memoizedState.cache),gn();break;case 27:case 5:er(t);break;case 4:Te(t,t.stateNode.containerInfo);break;case 10:yl(t,t.type,t.memoizedProps.value);break;case 13:var a=t.memoizedState;if(a!==null)return a.dehydrated!==null?(xl(t),t.flags|=128,null):(l&t.child.childLanes)!==0?bd(e,t,l):(xl(t),e=Pt(e,t,l),e!==null?e.sibling:null);xl(t);break;case 19:var u=(e.flags&128)!==0;if(a=(l&t.childLanes)!==0,a||(bn(e,t,l,!1),a=(l&t.childLanes)!==0),u){if(a)return Sd(e,t,l);t.flags|=128}if(u=t.memoizedState,u!==null&&(u.rendering=null,u.tail=null,u.lastEffect=null),X(qe,qe.current),a)break;return null;case 22:case 23:return t.lanes=0,md(e,t,l);case 24:yl(t,Be,e.memoizedState.cache)}return Pt(e,t,l)}function Ed(e,t,l){if(e!==null)if(e.memoizedProps!==t.pendingProps)Ye=!0;else{if(!Rs(e,l)&&(t.flags&128)===0)return Ye=!1,By(e,t,l);Ye=(e.flags&131072)!==0}else Ye=!1,de&&(t.flags&1048576)!==0&&Wo(t,zu,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var a=t.elementType,u=a._init;if(a=u(a._payload),t.type=a,typeof a=="function")zr(a)?(e=ea(a,e),t.tag=1,t=gd(null,t,a,e,l)):(t.tag=0,t=vs(null,t,a,e,l));else{if(a!=null){if(u=a.$$typeof,u===ce){t.tag=11,t=fd(null,t,a,e,l);break e}else if(u===be){t.tag=14,t=dd(null,t,a,e,l);break e}}throw t=Ll(a)||a,Error(s(306,t,""))}}return t;case 0:return vs(e,t,t.type,t.pendingProps,l);case 1:return a=t.type,u=ea(a,t.pendingProps),gd(e,t,a,u,l);case 3:e:{if(Te(t,t.stateNode.containerInfo),e===null)throw Error(s(387));a=t.pendingProps;var i=t.memoizedState;u=i.element,Jr(e,t),An(t,a,null,l);var f=t.memoizedState;if(a=f.cache,yl(t,Be,a),a!==i.cache&&Xr(t,[Be],l,!0),Rn(),a=f.element,i.isDehydrated)if(i={element:a,isDehydrated:!1,cache:f.cache},t.updateQueue.baseState=i,t.memoizedState=i,t.flags&256){t=vd(e,t,a,l);break e}else if(a!==u){u=Et(Error(s(424)),t),vn(u),t=vd(e,t,a,l);break e}else{switch(e=t.stateNode.containerInfo,e.nodeType){case 9:e=e.body;break;default:e=e.nodeName==="HTML"?e.ownerDocument.body:e}for(_e=Dt(e.firstChild),Pe=t,de=!0,Jl=null,Bt=!0,l=ed(t,null,a,l),t.child=l;l;)l.flags=l.flags&-3|4096,l=l.sibling}else{if(gn(),a===u){t=Pt(e,t,l);break e}Qe(e,t,a,l)}t=t.child}return t;case 26:return ei(e,t),e===null?(l=Ah(t.type,null,t.pendingProps,null))?t.memoizedState=l:de||(l=t.type,e=t.pendingProps,a=yi(te.current).createElement(l),a[Je]=t,a[tt]=e,Ze(a,l,e),Le(a),t.stateNode=a):t.memoizedState=Ah(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return er(t),e===null&&de&&(a=t.stateNode=Nh(t.type,t.pendingProps,te.current),Pe=t,Bt=!0,u=_e,_l(t.type)?(uc=u,_e=Dt(a.firstChild)):_e=u),Qe(e,t,t.pendingProps.children,l),ei(e,t),e===null&&(t.flags|=4194304),t.child;case 5:return e===null&&de&&((u=a=_e)&&(a=fp(a,t.type,t.pendingProps,Bt),a!==null?(t.stateNode=a,Pe=t,_e=Dt(a.firstChild),Bt=!1,u=!0):u=!1),u||$l(t)),er(t),u=t.type,i=t.pendingProps,f=e!==null?e.memoizedProps:null,a=i.children,tc(u,i)?a=null:f!==null&&tc(u,f)&&(t.flags|=32),t.memoizedState!==null&&(u=es(e,t,wy,null,null,l),Jn._currentValue=u),ei(e,t),Qe(e,t,a,l),t.child;case 6:return e===null&&de&&((e=l=_e)&&(l=dp(l,t.pendingProps,Bt),l!==null?(t.stateNode=l,Pe=t,_e=null,e=!0):e=!1),e||$l(t)),null;case 13:return bd(e,t,l);case 4:return Te(t,t.stateNode.containerInfo),a=t.pendingProps,e===null?t.child=Ua(t,null,a,l):Qe(e,t,a,l),t.child;case 11:return fd(e,t,t.type,t.pendingProps,l);case 7:return Qe(e,t,t.pendingProps,l),t.child;case 8:return Qe(e,t,t.pendingProps.children,l),t.child;case 12:return Qe(e,t,t.pendingProps.children,l),t.child;case 10:return a=t.pendingProps,yl(t,t.type,a.value),Qe(e,t,a.children,l),t.child;case 9:return u=t.type._context,a=t.pendingProps.children,Wl(t),u=$e(u),a=a(u),t.flags|=1,Qe(e,t,a,l),t.child;case 14:return dd(e,t,t.type,t.pendingProps,l);case 15:return hd(e,t,t.type,t.pendingProps,l);case 19:return Sd(e,t,l);case 31:return a=t.pendingProps,l=t.mode,a={mode:a.mode,children:a.children},e===null?(l=ti(a,l),l.ref=t.ref,t.child=l,l.return=t,t=l):(l=Zt(e.child,a),l.ref=t.ref,t.child=l,l.return=t,t=l),t;case 22:return md(e,t,l);case 24:return Wl(t),a=$e(Be),e===null?(u=Zr(),u===null&&(u=Ee,i=Qr(),u.pooledCache=i,i.refCount++,i!==null&&(u.pooledCacheLanes|=l),u=i),t.memoizedState={parent:a,cache:u},kr(t),yl(t,Be,u)):((e.lanes&l)!==0&&(Jr(e,t),An(t,null,null,l),Rn()),u=e.memoizedState,i=t.memoizedState,u.parent!==a?(u={parent:a,cache:a},t.memoizedState=u,t.lanes===0&&(t.memoizedState=t.updateQueue.baseState=u),yl(t,Be,a)):(a=i.cache,yl(t,Be,a),a!==u.cache&&Xr(t,[Be],l,!0))),Qe(e,t,t.pendingProps.children,l),t.child;case 29:throw t.pendingProps}throw Error(s(156,t.tag))}function It(e){e.flags|=4}function Nd(e,t){if(t.type!=="stylesheet"||(t.state.loading&4)!==0)e.flags&=-16777217;else if(e.flags|=16777216,!Dh(t)){if(t=At.current,t!==null&&((se&4194048)===se?qt!==null:(se&62914560)!==se&&(se&536870912)===0||t!==qt))throw Nn=Kr,uf;e.flags|=8192}}function li(e,t){t!==null&&(e.flags|=4),e.flags&16384&&(t=e.tag!==22?Ic():536870912,e.lanes|=t,La|=t)}function Mn(e,t){if(!de)switch(e.tailMode){case"hidden":t=e.tail;for(var l=null;t!==null;)t.alternate!==null&&(l=t),t=t.sibling;l===null?e.tail=null:l.sibling=null;break;case"collapsed":l=e.tail;for(var a=null;l!==null;)l.alternate!==null&&(a=l),l=l.sibling;a===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:a.sibling=null}}function we(e){var t=e.alternate!==null&&e.alternate.child===e.child,l=0,a=0;if(t)for(var u=e.child;u!==null;)l|=u.lanes|u.childLanes,a|=u.subtreeFlags&65011712,a|=u.flags&65011712,u.return=e,u=u.sibling;else for(u=e.child;u!==null;)l|=u.lanes|u.childLanes,a|=u.subtreeFlags,a|=u.flags,u.return=e,u=u.sibling;return e.subtreeFlags|=a,e.childLanes=l,t}function qy(e,t,l){var a=t.pendingProps;switch(qr(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return we(t),null;case 1:return we(t),null;case 3:return l=t.stateNode,a=null,e!==null&&(a=e.memoizedState.cache),t.memoizedState.cache!==a&&(t.flags|=2048),$t(Be),fl(),l.pendingContext&&(l.context=l.pendingContext,l.pendingContext=null),(e===null||e.child===null)&&(pn(t)?It(t):e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,ef())),we(t),null;case 26:return l=t.memoizedState,e===null?(It(t),l!==null?(we(t),Nd(t,l)):(we(t),t.flags&=-16777217)):l?l!==e.memoizedState?(It(t),we(t),Nd(t,l)):(we(t),t.flags&=-16777217):(e.memoizedProps!==a&&It(t),we(t),t.flags&=-16777217),null;case 27:hu(t),l=te.current;var u=t.type;if(e!==null&&t.stateNode!=null)e.memoizedProps!==a&&It(t);else{if(!a){if(t.stateNode===null)throw Error(s(166));return we(t),null}e=F.current,pn(t)?Po(t):(e=Nh(u,a,l),t.stateNode=e,It(t))}return we(t),null;case 5:if(hu(t),l=t.type,e!==null&&t.stateNode!=null)e.memoizedProps!==a&&It(t);else{if(!a){if(t.stateNode===null)throw Error(s(166));return we(t),null}if(e=F.current,pn(t))Po(t);else{switch(u=yi(te.current),e){case 1:e=u.createElementNS("http://www.w3.org/2000/svg",l);break;case 2:e=u.createElementNS("http://www.w3.org/1998/Math/MathML",l);break;default:switch(l){case"svg":e=u.createElementNS("http://www.w3.org/2000/svg",l);break;case"math":e=u.createElementNS("http://www.w3.org/1998/Math/MathML",l);break;case"script":e=u.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e=typeof a.is=="string"?u.createElement("select",{is:a.is}):u.createElement("select"),a.multiple?e.multiple=!0:a.size&&(e.size=a.size);break;default:e=typeof a.is=="string"?u.createElement(l,{is:a.is}):u.createElement(l)}}e[Je]=t,e[tt]=a;e:for(u=t.child;u!==null;){if(u.tag===5||u.tag===6)e.appendChild(u.stateNode);else if(u.tag!==4&&u.tag!==27&&u.child!==null){u.child.return=u,u=u.child;continue}if(u===t)break e;for(;u.sibling===null;){if(u.return===null||u.return===t)break e;u=u.return}u.sibling.return=u.return,u=u.sibling}t.stateNode=e;e:switch(Ze(e,l,a),l){case"button":case"input":case"select":case"textarea":e=!!a.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&It(t)}}return we(t),t.flags&=-16777217,null;case 6:if(e&&t.stateNode!=null)e.memoizedProps!==a&&It(t);else{if(typeof a!="string"&&t.stateNode===null)throw Error(s(166));if(e=te.current,pn(t)){if(e=t.stateNode,l=t.memoizedProps,a=null,u=Pe,u!==null)switch(u.tag){case 27:case 5:a=u.memoizedProps}e[Je]=t,e=!!(e.nodeValue===l||a!==null&&a.suppressHydrationWarning===!0||ph(e.nodeValue,l)),e||$l(t)}else e=yi(e).createTextNode(a),e[Je]=t,t.stateNode=e}return we(t),null;case 13:if(a=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(u=pn(t),a!==null&&a.dehydrated!==null){if(e===null){if(!u)throw Error(s(318));if(u=t.memoizedState,u=u!==null?u.dehydrated:null,!u)throw Error(s(317));u[Je]=t}else gn(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;we(t),u=!1}else u=ef(),e!==null&&e.memoizedState!==null&&(e.memoizedState.hydrationErrors=u),u=!0;if(!u)return t.flags&256?(Wt(t),t):(Wt(t),null)}if(Wt(t),(t.flags&128)!==0)return t.lanes=l,t;if(l=a!==null,e=e!==null&&e.memoizedState!==null,l){a=t.child,u=null,a.alternate!==null&&a.alternate.memoizedState!==null&&a.alternate.memoizedState.cachePool!==null&&(u=a.alternate.memoizedState.cachePool.pool);var i=null;a.memoizedState!==null&&a.memoizedState.cachePool!==null&&(i=a.memoizedState.cachePool.pool),i!==u&&(a.flags|=2048)}return l!==e&&l&&(t.child.flags|=8192),li(t,t.updateQueue),we(t),null;case 4:return fl(),e===null&&Fs(t.stateNode.containerInfo),we(t),null;case 10:return $t(t.type),we(t),null;case 19:if(V(qe),u=t.memoizedState,u===null)return we(t),null;if(a=(t.flags&128)!==0,i=u.rendering,i===null)if(a)Mn(u,!1);else{if(De!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(i=Wu(e),i!==null){for(t.flags|=128,Mn(u,!1),e=i.updateQueue,t.updateQueue=e,li(t,e),t.subtreeFlags=0,e=l,l=t.child;l!==null;)Fo(l,e),l=l.sibling;return X(qe,qe.current&1|2),t.child}e=e.sibling}u.tail!==null&&Ht()>ui&&(t.flags|=128,a=!0,Mn(u,!1),t.lanes=4194304)}else{if(!a)if(e=Wu(i),e!==null){if(t.flags|=128,a=!0,e=e.updateQueue,t.updateQueue=e,li(t,e),Mn(u,!0),u.tail===null&&u.tailMode==="hidden"&&!i.alternate&&!de)return we(t),null}else 2*Ht()-u.renderingStartTime>ui&&l!==536870912&&(t.flags|=128,a=!0,Mn(u,!1),t.lanes=4194304);u.isBackwards?(i.sibling=t.child,t.child=i):(e=u.last,e!==null?e.sibling=i:t.child=i,u.last=i)}return u.tail!==null?(t=u.tail,u.rendering=t,u.tail=t.sibling,u.renderingStartTime=Ht(),t.sibling=null,e=qe.current,X(qe,a?e&1|2:e&1),t):(we(t),null);case 22:case 23:return Wt(t),Pr(),a=t.memoizedState!==null,e!==null?e.memoizedState!==null!==a&&(t.flags|=8192):a&&(t.flags|=8192),a?(l&536870912)!==0&&(t.flags&128)===0&&(we(t),t.subtreeFlags&6&&(t.flags|=8192)):we(t),l=t.updateQueue,l!==null&&li(t,l.retryQueue),l=null,e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(l=e.memoizedState.cachePool.pool),a=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(a=t.memoizedState.cachePool.pool),a!==l&&(t.flags|=2048),e!==null&&V(Pl),null;case 24:return l=null,e!==null&&(l=e.memoizedState.cache),t.memoizedState.cache!==l&&(t.flags|=2048),$t(Be),we(t),null;case 25:return null;case 30:return null}throw Error(s(156,t.tag))}function Ly(e,t){switch(qr(t),t.tag){case 1:return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return $t(Be),fl(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 26:case 27:case 5:return hu(t),null;case 13:if(Wt(t),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(s(340));gn()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return V(qe),null;case 4:return fl(),null;case 10:return $t(t.type),null;case 22:case 23:return Wt(t),Pr(),e!==null&&V(Pl),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 24:return $t(Be),null;case 25:return null;default:return null}}function Td(e,t){switch(qr(t),t.tag){case 3:$t(Be),fl();break;case 26:case 27:case 5:hu(t);break;case 4:fl();break;case 13:Wt(t);break;case 19:V(qe);break;case 10:$t(t.type);break;case 22:case 23:Wt(t),Pr(),e!==null&&V(Pl);break;case 24:$t(Be)}}function zn(e,t){try{var l=t.updateQueue,a=l!==null?l.lastEffect:null;if(a!==null){var u=a.next;l=u;do{if((l.tag&e)===e){a=void 0;var i=l.create,f=l.inst;a=i(),f.destroy=a}l=l.next}while(l!==u)}}catch(m){Se(t,t.return,m)}}function El(e,t,l){try{var a=t.updateQueue,u=a!==null?a.lastEffect:null;if(u!==null){var i=u.next;a=i;do{if((a.tag&e)===e){var f=a.inst,m=f.destroy;if(m!==void 0){f.destroy=void 0,u=t;var b=l,R=m;try{R()}catch(M){Se(u,b,M)}}}a=a.next}while(a!==i)}}catch(M){Se(t,t.return,M)}}function Rd(e){var t=e.updateQueue;if(t!==null){var l=e.stateNode;try{df(t,l)}catch(a){Se(e,e.return,a)}}}function Ad(e,t,l){l.props=ea(e.type,e.memoizedProps),l.state=e.memoizedState;try{l.componentWillUnmount()}catch(a){Se(e,t,a)}}function Un(e,t){try{var l=e.ref;if(l!==null){switch(e.tag){case 26:case 27:case 5:var a=e.stateNode;break;case 30:a=e.stateNode;break;default:a=e.stateNode}typeof l=="function"?e.refCleanup=l(a):l.current=a}}catch(u){Se(e,t,u)}}function Lt(e,t){var l=e.ref,a=e.refCleanup;if(l!==null)if(typeof a=="function")try{a()}catch(u){Se(e,t,u)}finally{e.refCleanup=null,e=e.alternate,e!=null&&(e.refCleanup=null)}else if(typeof l=="function")try{l(null)}catch(u){Se(e,t,u)}else l.current=null}function Od(e){var t=e.type,l=e.memoizedProps,a=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":l.autoFocus&&a.focus();break e;case"img":l.src?a.src=l.src:l.srcSet&&(a.srcset=l.srcSet)}}catch(u){Se(e,e.return,u)}}function As(e,t,l){try{var a=e.stateNode;ip(a,e.type,l,t),a[tt]=t}catch(u){Se(e,e.return,u)}}function jd(e){return e.tag===5||e.tag===3||e.tag===26||e.tag===27&&_l(e.type)||e.tag===4}function Os(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||jd(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.tag===27&&_l(e.type)||e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function js(e,t,l){var a=e.tag;if(a===5||a===6)e=e.stateNode,t?(l.nodeType===9?l.body:l.nodeName==="HTML"?l.ownerDocument.body:l).insertBefore(e,t):(t=l.nodeType===9?l.body:l.nodeName==="HTML"?l.ownerDocument.body:l,t.appendChild(e),l=l._reactRootContainer,l!=null||t.onclick!==null||(t.onclick=mi));else if(a!==4&&(a===27&&_l(e.type)&&(l=e.stateNode,t=null),e=e.child,e!==null))for(js(e,t,l),e=e.sibling;e!==null;)js(e,t,l),e=e.sibling}function ai(e,t,l){var a=e.tag;if(a===5||a===6)e=e.stateNode,t?l.insertBefore(e,t):l.appendChild(e);else if(a!==4&&(a===27&&_l(e.type)&&(l=e.stateNode),e=e.child,e!==null))for(ai(e,t,l),e=e.sibling;e!==null;)ai(e,t,l),e=e.sibling}function wd(e){var t=e.stateNode,l=e.memoizedProps;try{for(var a=e.type,u=t.attributes;u.length;)t.removeAttributeNode(u[0]);Ze(t,a,l),t[Je]=e,t[tt]=l}catch(i){Se(e,e.return,i)}}var el=!1,Me=!1,ws=!1,_d=typeof WeakSet=="function"?WeakSet:Set,Ge=null;function Yy(e,t){if(e=e.containerInfo,Is=Si,e=Yo(e),Or(e)){if("selectionStart"in e)var l={start:e.selectionStart,end:e.selectionEnd};else e:{l=(l=e.ownerDocument)&&l.defaultView||window;var a=l.getSelection&&l.getSelection();if(a&&a.rangeCount!==0){l=a.anchorNode;var u=a.anchorOffset,i=a.focusNode;a=a.focusOffset;try{l.nodeType,i.nodeType}catch{l=null;break e}var f=0,m=-1,b=-1,R=0,M=0,H=e,j=null;t:for(;;){for(var w;H!==l||u!==0&&H.nodeType!==3||(m=f+u),H!==i||a!==0&&H.nodeType!==3||(b=f+a),H.nodeType===3&&(f+=H.nodeValue.length),(w=H.firstChild)!==null;)j=H,H=w;for(;;){if(H===e)break t;if(j===l&&++R===u&&(m=f),j===i&&++M===a&&(b=f),(w=H.nextSibling)!==null)break;H=j,j=H.parentNode}H=w}l=m===-1||b===-1?null:{start:m,end:b}}else l=null}l=l||{start:0,end:0}}else l=null;for(ec={focusedElem:e,selectionRange:l},Si=!1,Ge=t;Ge!==null;)if(t=Ge,e=t.child,(t.subtreeFlags&1024)!==0&&e!==null)e.return=t,Ge=e;else for(;Ge!==null;){switch(t=Ge,i=t.alternate,e=t.flags,t.tag){case 0:break;case 11:case 15:break;case 1:if((e&1024)!==0&&i!==null){e=void 0,l=t,u=i.memoizedProps,i=i.memoizedState,a=l.stateNode;try{var ee=ea(l.type,u,l.elementType===l.type);e=a.getSnapshotBeforeUpdate(ee,i),a.__reactInternalSnapshotBeforeUpdate=e}catch(W){Se(l,l.return,W)}}break;case 3:if((e&1024)!==0){if(e=t.stateNode.containerInfo,l=e.nodeType,l===9)ac(e);else if(l===1)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":ac(e);break;default:e.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((e&1024)!==0)throw Error(s(163))}if(e=t.sibling,e!==null){e.return=t.return,Ge=e;break}Ge=t.return}}function Dd(e,t,l){var a=l.flags;switch(l.tag){case 0:case 11:case 15:Nl(e,l),a&4&&zn(5,l);break;case 1:if(Nl(e,l),a&4)if(e=l.stateNode,t===null)try{e.componentDidMount()}catch(f){Se(l,l.return,f)}else{var u=ea(l.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(u,t,e.__reactInternalSnapshotBeforeUpdate)}catch(f){Se(l,l.return,f)}}a&64&&Rd(l),a&512&&Un(l,l.return);break;case 3:if(Nl(e,l),a&64&&(e=l.updateQueue,e!==null)){if(t=null,l.child!==null)switch(l.child.tag){case 27:case 5:t=l.child.stateNode;break;case 1:t=l.child.stateNode}try{df(e,t)}catch(f){Se(l,l.return,f)}}break;case 27:t===null&&a&4&&wd(l);case 26:case 5:Nl(e,l),t===null&&a&4&&Od(l),a&512&&Un(l,l.return);break;case 12:Nl(e,l);break;case 13:Nl(e,l),a&4&&zd(e,l),a&64&&(e=l.memoizedState,e!==null&&(e=e.dehydrated,e!==null&&(l=$y.bind(null,l),hp(e,l))));break;case 22:if(a=l.memoizedState!==null||el,!a){t=t!==null&&t.memoizedState!==null||Me,u=el;var i=Me;el=a,(Me=t)&&!i?Tl(e,l,(l.subtreeFlags&8772)!==0):Nl(e,l),el=u,Me=i}break;case 30:break;default:Nl(e,l)}}function Cd(e){var t=e.alternate;t!==null&&(e.alternate=null,Cd(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&sr(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var Re=null,nt=!1;function tl(e,t,l){for(l=l.child;l!==null;)Md(e,t,l),l=l.sibling}function Md(e,t,l){if(ot&&typeof ot.onCommitFiberUnmount=="function")try{ot.onCommitFiberUnmount(tn,l)}catch{}switch(l.tag){case 26:Me||Lt(l,t),tl(e,t,l),l.memoizedState?l.memoizedState.count--:l.stateNode&&(l=l.stateNode,l.parentNode.removeChild(l));break;case 27:Me||Lt(l,t);var a=Re,u=nt;_l(l.type)&&(Re=l.stateNode,nt=!1),tl(e,t,l),Vn(l.stateNode),Re=a,nt=u;break;case 5:Me||Lt(l,t);case 6:if(a=Re,u=nt,Re=null,tl(e,t,l),Re=a,nt=u,Re!==null)if(nt)try{(Re.nodeType===9?Re.body:Re.nodeName==="HTML"?Re.ownerDocument.body:Re).removeChild(l.stateNode)}catch(i){Se(l,t,i)}else try{Re.removeChild(l.stateNode)}catch(i){Se(l,t,i)}break;case 18:Re!==null&&(nt?(e=Re,Sh(e.nodeType===9?e.body:e.nodeName==="HTML"?e.ownerDocument.body:e,l.stateNode),Pn(e)):Sh(Re,l.stateNode));break;case 4:a=Re,u=nt,Re=l.stateNode.containerInfo,nt=!0,tl(e,t,l),Re=a,nt=u;break;case 0:case 11:case 14:case 15:Me||El(2,l,t),Me||El(4,l,t),tl(e,t,l);break;case 1:Me||(Lt(l,t),a=l.stateNode,typeof a.componentWillUnmount=="function"&&Ad(l,t,a)),tl(e,t,l);break;case 21:tl(e,t,l);break;case 22:Me=(a=Me)||l.memoizedState!==null,tl(e,t,l),Me=a;break;default:tl(e,t,l)}}function zd(e,t){if(t.memoizedState===null&&(e=t.alternate,e!==null&&(e=e.memoizedState,e!==null&&(e=e.dehydrated,e!==null))))try{Pn(e)}catch(l){Se(t,t.return,l)}}function Gy(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return t===null&&(t=e.stateNode=new _d),t;case 22:return e=e.stateNode,t=e._retryCache,t===null&&(t=e._retryCache=new _d),t;default:throw Error(s(435,e.tag))}}function _s(e,t){var l=Gy(e);t.forEach(function(a){var u=Fy.bind(null,e,a);l.has(a)||(l.add(a),a.then(u,u))})}function mt(e,t){var l=t.deletions;if(l!==null)for(var a=0;a<l.length;a++){var u=l[a],i=e,f=t,m=f;e:for(;m!==null;){switch(m.tag){case 27:if(_l(m.type)){Re=m.stateNode,nt=!1;break e}break;case 5:Re=m.stateNode,nt=!1;break e;case 3:case 4:Re=m.stateNode.containerInfo,nt=!0;break e}m=m.return}if(Re===null)throw Error(s(160));Md(i,f,u),Re=null,nt=!1,i=u.alternate,i!==null&&(i.return=null),u.return=null}if(t.subtreeFlags&13878)for(t=t.child;t!==null;)Ud(t,e),t=t.sibling}var _t=null;function Ud(e,t){var l=e.alternate,a=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:mt(t,e),yt(e),a&4&&(El(3,e,e.return),zn(3,e),El(5,e,e.return));break;case 1:mt(t,e),yt(e),a&512&&(Me||l===null||Lt(l,l.return)),a&64&&el&&(e=e.updateQueue,e!==null&&(a=e.callbacks,a!==null&&(l=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=l===null?a:l.concat(a))));break;case 26:var u=_t;if(mt(t,e),yt(e),a&512&&(Me||l===null||Lt(l,l.return)),a&4){var i=l!==null?l.memoizedState:null;if(a=e.memoizedState,l===null)if(a===null)if(e.stateNode===null){e:{a=e.type,l=e.memoizedProps,u=u.ownerDocument||u;t:switch(a){case"title":i=u.getElementsByTagName("title")[0],(!i||i[nn]||i[Je]||i.namespaceURI==="http://www.w3.org/2000/svg"||i.hasAttribute("itemprop"))&&(i=u.createElement(a),u.head.insertBefore(i,u.querySelector("head > title"))),Ze(i,a,l),i[Je]=e,Le(i),a=i;break e;case"link":var f=wh("link","href",u).get(a+(l.href||""));if(f){for(var m=0;m<f.length;m++)if(i=f[m],i.getAttribute("href")===(l.href==null||l.href===""?null:l.href)&&i.getAttribute("rel")===(l.rel==null?null:l.rel)&&i.getAttribute("title")===(l.title==null?null:l.title)&&i.getAttribute("crossorigin")===(l.crossOrigin==null?null:l.crossOrigin)){f.splice(m,1);break t}}i=u.createElement(a),Ze(i,a,l),u.head.appendChild(i);break;case"meta":if(f=wh("meta","content",u).get(a+(l.content||""))){for(m=0;m<f.length;m++)if(i=f[m],i.getAttribute("content")===(l.content==null?null:""+l.content)&&i.getAttribute("name")===(l.name==null?null:l.name)&&i.getAttribute("property")===(l.property==null?null:l.property)&&i.getAttribute("http-equiv")===(l.httpEquiv==null?null:l.httpEquiv)&&i.getAttribute("charset")===(l.charSet==null?null:l.charSet)){f.splice(m,1);break t}}i=u.createElement(a),Ze(i,a,l),u.head.appendChild(i);break;default:throw Error(s(468,a))}i[Je]=e,Le(i),a=i}e.stateNode=a}else _h(u,e.type,e.stateNode);else e.stateNode=jh(u,a,e.memoizedProps);else i!==a?(i===null?l.stateNode!==null&&(l=l.stateNode,l.parentNode.removeChild(l)):i.count--,a===null?_h(u,e.type,e.stateNode):jh(u,a,e.memoizedProps)):a===null&&e.stateNode!==null&&As(e,e.memoizedProps,l.memoizedProps)}break;case 27:mt(t,e),yt(e),a&512&&(Me||l===null||Lt(l,l.return)),l!==null&&a&4&&As(e,e.memoizedProps,l.memoizedProps);break;case 5:if(mt(t,e),yt(e),a&512&&(Me||l===null||Lt(l,l.return)),e.flags&32){u=e.stateNode;try{ga(u,"")}catch(w){Se(e,e.return,w)}}a&4&&e.stateNode!=null&&(u=e.memoizedProps,As(e,u,l!==null?l.memoizedProps:u)),a&1024&&(ws=!0);break;case 6:if(mt(t,e),yt(e),a&4){if(e.stateNode===null)throw Error(s(162));a=e.memoizedProps,l=e.stateNode;try{l.nodeValue=a}catch(w){Se(e,e.return,w)}}break;case 3:if(vi=null,u=_t,_t=pi(t.containerInfo),mt(t,e),_t=u,yt(e),a&4&&l!==null&&l.memoizedState.isDehydrated)try{Pn(t.containerInfo)}catch(w){Se(e,e.return,w)}ws&&(ws=!1,Hd(e));break;case 4:a=_t,_t=pi(e.stateNode.containerInfo),mt(t,e),yt(e),_t=a;break;case 12:mt(t,e),yt(e);break;case 13:mt(t,e),yt(e),e.child.flags&8192&&e.memoizedState!==null!=(l!==null&&l.memoizedState!==null)&&(Hs=Ht()),a&4&&(a=e.updateQueue,a!==null&&(e.updateQueue=null,_s(e,a)));break;case 22:u=e.memoizedState!==null;var b=l!==null&&l.memoizedState!==null,R=el,M=Me;if(el=R||u,Me=M||b,mt(t,e),Me=M,el=R,yt(e),a&8192)e:for(t=e.stateNode,t._visibility=u?t._visibility&-2:t._visibility|1,u&&(l===null||b||el||Me||ta(e)),l=null,t=e;;){if(t.tag===5||t.tag===26){if(l===null){b=l=t;try{if(i=b.stateNode,u)f=i.style,typeof f.setProperty=="function"?f.setProperty("display","none","important"):f.display="none";else{m=b.stateNode;var H=b.memoizedProps.style,j=H!=null&&H.hasOwnProperty("display")?H.display:null;m.style.display=j==null||typeof j=="boolean"?"":(""+j).trim()}}catch(w){Se(b,b.return,w)}}}else if(t.tag===6){if(l===null){b=t;try{b.stateNode.nodeValue=u?"":b.memoizedProps}catch(w){Se(b,b.return,w)}}}else if((t.tag!==22&&t.tag!==23||t.memoizedState===null||t===e)&&t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;t.sibling===null;){if(t.return===null||t.return===e)break e;l===t&&(l=null),t=t.return}l===t&&(l=null),t.sibling.return=t.return,t=t.sibling}a&4&&(a=e.updateQueue,a!==null&&(l=a.retryQueue,l!==null&&(a.retryQueue=null,_s(e,l))));break;case 19:mt(t,e),yt(e),a&4&&(a=e.updateQueue,a!==null&&(e.updateQueue=null,_s(e,a)));break;case 30:break;case 21:break;default:mt(t,e),yt(e)}}function yt(e){var t=e.flags;if(t&2){try{for(var l,a=e.return;a!==null;){if(jd(a)){l=a;break}a=a.return}if(l==null)throw Error(s(160));switch(l.tag){case 27:var u=l.stateNode,i=Os(e);ai(e,i,u);break;case 5:var f=l.stateNode;l.flags&32&&(ga(f,""),l.flags&=-33);var m=Os(e);ai(e,m,f);break;case 3:case 4:var b=l.stateNode.containerInfo,R=Os(e);js(e,R,b);break;default:throw Error(s(161))}}catch(M){Se(e,e.return,M)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Hd(e){if(e.subtreeFlags&1024)for(e=e.child;e!==null;){var t=e;Hd(t),t.tag===5&&t.flags&1024&&t.stateNode.reset(),e=e.sibling}}function Nl(e,t){if(t.subtreeFlags&8772)for(t=t.child;t!==null;)Dd(e,t.alternate,t),t=t.sibling}function ta(e){for(e=e.child;e!==null;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:El(4,t,t.return),ta(t);break;case 1:Lt(t,t.return);var l=t.stateNode;typeof l.componentWillUnmount=="function"&&Ad(t,t.return,l),ta(t);break;case 27:Vn(t.stateNode);case 26:case 5:Lt(t,t.return),ta(t);break;case 22:t.memoizedState===null&&ta(t);break;case 30:ta(t);break;default:ta(t)}e=e.sibling}}function Tl(e,t,l){for(l=l&&(t.subtreeFlags&8772)!==0,t=t.child;t!==null;){var a=t.alternate,u=e,i=t,f=i.flags;switch(i.tag){case 0:case 11:case 15:Tl(u,i,l),zn(4,i);break;case 1:if(Tl(u,i,l),a=i,u=a.stateNode,typeof u.componentDidMount=="function")try{u.componentDidMount()}catch(R){Se(a,a.return,R)}if(a=i,u=a.updateQueue,u!==null){var m=a.stateNode;try{var b=u.shared.hiddenCallbacks;if(b!==null)for(u.shared.hiddenCallbacks=null,u=0;u<b.length;u++)ff(b[u],m)}catch(R){Se(a,a.return,R)}}l&&f&64&&Rd(i),Un(i,i.return);break;case 27:wd(i);case 26:case 5:Tl(u,i,l),l&&a===null&&f&4&&Od(i),Un(i,i.return);break;case 12:Tl(u,i,l);break;case 13:Tl(u,i,l),l&&f&4&&zd(u,i);break;case 22:i.memoizedState===null&&Tl(u,i,l),Un(i,i.return);break;case 30:break;default:Tl(u,i,l)}t=t.sibling}}function Ds(e,t){var l=null;e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(l=e.memoizedState.cachePool.pool),e=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(e=t.memoizedState.cachePool.pool),e!==l&&(e!=null&&e.refCount++,l!=null&&xn(l))}function Cs(e,t){e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&xn(e))}function Yt(e,t,l,a){if(t.subtreeFlags&10256)for(t=t.child;t!==null;)Bd(e,t,l,a),t=t.sibling}function Bd(e,t,l,a){var u=t.flags;switch(t.tag){case 0:case 11:case 15:Yt(e,t,l,a),u&2048&&zn(9,t);break;case 1:Yt(e,t,l,a);break;case 3:Yt(e,t,l,a),u&2048&&(e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&xn(e)));break;case 12:if(u&2048){Yt(e,t,l,a),e=t.stateNode;try{var i=t.memoizedProps,f=i.id,m=i.onPostCommit;typeof m=="function"&&m(f,t.alternate===null?"mount":"update",e.passiveEffectDuration,-0)}catch(b){Se(t,t.return,b)}}else Yt(e,t,l,a);break;case 13:Yt(e,t,l,a);break;case 23:break;case 22:i=t.stateNode,f=t.alternate,t.memoizedState!==null?i._visibility&2?Yt(e,t,l,a):Hn(e,t):i._visibility&2?Yt(e,t,l,a):(i._visibility|=2,Ha(e,t,l,a,(t.subtreeFlags&10256)!==0)),u&2048&&Ds(f,t);break;case 24:Yt(e,t,l,a),u&2048&&Cs(t.alternate,t);break;default:Yt(e,t,l,a)}}function Ha(e,t,l,a,u){for(u=u&&(t.subtreeFlags&10256)!==0,t=t.child;t!==null;){var i=e,f=t,m=l,b=a,R=f.flags;switch(f.tag){case 0:case 11:case 15:Ha(i,f,m,b,u),zn(8,f);break;case 23:break;case 22:var M=f.stateNode;f.memoizedState!==null?M._visibility&2?Ha(i,f,m,b,u):Hn(i,f):(M._visibility|=2,Ha(i,f,m,b,u)),u&&R&2048&&Ds(f.alternate,f);break;case 24:Ha(i,f,m,b,u),u&&R&2048&&Cs(f.alternate,f);break;default:Ha(i,f,m,b,u)}t=t.sibling}}function Hn(e,t){if(t.subtreeFlags&10256)for(t=t.child;t!==null;){var l=e,a=t,u=a.flags;switch(a.tag){case 22:Hn(l,a),u&2048&&Ds(a.alternate,a);break;case 24:Hn(l,a),u&2048&&Cs(a.alternate,a);break;default:Hn(l,a)}t=t.sibling}}var Bn=8192;function Ba(e){if(e.subtreeFlags&Bn)for(e=e.child;e!==null;)qd(e),e=e.sibling}function qd(e){switch(e.tag){case 26:Ba(e),e.flags&Bn&&e.memoizedState!==null&&Ap(_t,e.memoizedState,e.memoizedProps);break;case 5:Ba(e);break;case 3:case 4:var t=_t;_t=pi(e.stateNode.containerInfo),Ba(e),_t=t;break;case 22:e.memoizedState===null&&(t=e.alternate,t!==null&&t.memoizedState!==null?(t=Bn,Bn=16777216,Ba(e),Bn=t):Ba(e));break;default:Ba(e)}}function Ld(e){var t=e.alternate;if(t!==null&&(e=t.child,e!==null)){t.child=null;do t=e.sibling,e.sibling=null,e=t;while(e!==null)}}function qn(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var l=0;l<t.length;l++){var a=t[l];Ge=a,Gd(a,e)}Ld(e)}if(e.subtreeFlags&10256)for(e=e.child;e!==null;)Yd(e),e=e.sibling}function Yd(e){switch(e.tag){case 0:case 11:case 15:qn(e),e.flags&2048&&El(9,e,e.return);break;case 3:qn(e);break;case 12:qn(e);break;case 22:var t=e.stateNode;e.memoizedState!==null&&t._visibility&2&&(e.return===null||e.return.tag!==13)?(t._visibility&=-3,ni(e)):qn(e);break;default:qn(e)}}function ni(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var l=0;l<t.length;l++){var a=t[l];Ge=a,Gd(a,e)}Ld(e)}for(e=e.child;e!==null;){switch(t=e,t.tag){case 0:case 11:case 15:El(8,t,t.return),ni(t);break;case 22:l=t.stateNode,l._visibility&2&&(l._visibility&=-3,ni(t));break;default:ni(t)}e=e.sibling}}function Gd(e,t){for(;Ge!==null;){var l=Ge;switch(l.tag){case 0:case 11:case 15:El(8,l,t);break;case 23:case 22:if(l.memoizedState!==null&&l.memoizedState.cachePool!==null){var a=l.memoizedState.cachePool.pool;a!=null&&a.refCount++}break;case 24:xn(l.memoizedState.cache)}if(a=l.child,a!==null)a.return=l,Ge=a;else e:for(l=e;Ge!==null;){a=Ge;var u=a.sibling,i=a.return;if(Cd(a),a===l){Ge=null;break e}if(u!==null){u.return=i,Ge=u;break e}Ge=i}}}var Xy={getCacheForType:function(e){var t=$e(Be),l=t.data.get(e);return l===void 0&&(l=e(),t.data.set(e,l)),l}},Qy=typeof WeakMap=="function"?WeakMap:Map,he=0,Ee=null,ie=null,se=0,me=0,pt=null,Rl=!1,qa=!1,Ms=!1,ll=0,De=0,Al=0,la=0,zs=0,Ot=0,La=0,Ln=null,ut=null,Us=!1,Hs=0,ui=1/0,ii=null,Ol=null,Ve=0,jl=null,Ya=null,Ga=0,Bs=0,qs=null,Xd=null,Yn=0,Ls=null;function gt(){if((he&2)!==0&&se!==0)return se&-se;if(z.T!==null){var e=ja;return e!==0?e:Ks()}return lo()}function Qd(){Ot===0&&(Ot=(se&536870912)===0||de?Pc():536870912);var e=At.current;return e!==null&&(e.flags|=32),Ot}function vt(e,t,l){(e===Ee&&(me===2||me===9)||e.cancelPendingCommit!==null)&&(Xa(e,0),wl(e,se,Ot,!1)),an(e,l),((he&2)===0||e!==Ee)&&(e===Ee&&((he&2)===0&&(la|=l),De===4&&wl(e,se,Ot,!1)),Gt(e))}function Vd(e,t,l){if((he&6)!==0)throw Error(s(327));var a=!l&&(t&124)===0&&(t&e.expiredLanes)===0||ln(e,t),u=a?Ky(e,t):Xs(e,t,!0),i=a;do{if(u===0){qa&&!a&&wl(e,t,0,!1);break}else{if(l=e.current.alternate,i&&!Vy(l)){u=Xs(e,t,!1),i=!1;continue}if(u===2){if(i=t,e.errorRecoveryDisabledLanes&i)var f=0;else f=e.pendingLanes&-536870913,f=f!==0?f:f&536870912?536870912:0;if(f!==0){t=f;e:{var m=e;u=Ln;var b=m.current.memoizedState.isDehydrated;if(b&&(Xa(m,f).flags|=256),f=Xs(m,f,!1),f!==2){if(Ms&&!b){m.errorRecoveryDisabledLanes|=i,la|=i,u=4;break e}i=ut,ut=u,i!==null&&(ut===null?ut=i:ut.push.apply(ut,i))}u=f}if(i=!1,u!==2)continue}}if(u===1){Xa(e,0),wl(e,t,0,!0);break}e:{switch(a=e,i=u,i){case 0:case 1:throw Error(s(345));case 4:if((t&4194048)!==t)break;case 6:wl(a,t,Ot,!Rl);break e;case 2:ut=null;break;case 3:case 5:break;default:throw Error(s(329))}if((t&62914560)===t&&(u=Hs+300-Ht(),10<u)){if(wl(a,t,Ot,!Rl),gu(a,0,!0)!==0)break e;a.timeoutHandle=bh(Zd.bind(null,a,l,ut,ii,Us,t,Ot,la,La,Rl,i,2,-0,0),u);break e}Zd(a,l,ut,ii,Us,t,Ot,la,La,Rl,i,0,-0,0)}}break}while(!0);Gt(e)}function Zd(e,t,l,a,u,i,f,m,b,R,M,H,j,w){if(e.timeoutHandle=-1,H=t.subtreeFlags,(H&8192||(H&16785408)===16785408)&&(kn={stylesheets:null,count:0,unsuspend:Rp},qd(t),H=Op(),H!==null)){e.cancelPendingCommit=H(Pd.bind(null,e,t,i,l,a,u,f,m,b,M,1,j,w)),wl(e,i,f,!R);return}Pd(e,t,i,l,a,u,f,m,b)}function Vy(e){for(var t=e;;){var l=t.tag;if((l===0||l===11||l===15)&&t.flags&16384&&(l=t.updateQueue,l!==null&&(l=l.stores,l!==null)))for(var a=0;a<l.length;a++){var u=l[a],i=u.getSnapshot;u=u.value;try{if(!dt(i(),u))return!1}catch{return!1}}if(l=t.child,t.subtreeFlags&16384&&l!==null)l.return=t,t=l;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function wl(e,t,l,a){t&=~zs,t&=~la,e.suspendedLanes|=t,e.pingedLanes&=~t,a&&(e.warmLanes|=t),a=e.expirationTimes;for(var u=t;0<u;){var i=31-ft(u),f=1<<i;a[i]=-1,u&=~f}l!==0&&eo(e,l,t)}function ri(){return(he&6)===0?(Gn(0),!1):!0}function Ys(){if(ie!==null){if(me===0)var e=ie.return;else e=ie,Jt=Fl=null,as(e),za=null,Dn=0,e=ie;for(;e!==null;)Td(e.alternate,e),e=e.return;ie=null}}function Xa(e,t){var l=e.timeoutHandle;l!==-1&&(e.timeoutHandle=-1,sp(l)),l=e.cancelPendingCommit,l!==null&&(e.cancelPendingCommit=null,l()),Ys(),Ee=e,ie=l=Zt(e.current,null),se=t,me=0,pt=null,Rl=!1,qa=ln(e,t),Ms=!1,La=Ot=zs=la=Al=De=0,ut=Ln=null,Us=!1,(t&8)!==0&&(t|=t&32);var a=e.entangledLanes;if(a!==0)for(e=e.entanglements,a&=t;0<a;){var u=31-ft(a),i=1<<u;t|=e[u],a&=~i}return ll=t,wu(),l}function Kd(e,t){ne=null,z.H=Ju,t===En||t===qu?(t=cf(),me=3):t===uf?(t=cf(),me=4):me=t===od?8:t!==null&&typeof t=="object"&&typeof t.then=="function"?6:1,pt=t,ie===null&&(De=1,Iu(e,Et(t,e.current)))}function kd(){var e=z.H;return z.H=Ju,e===null?Ju:e}function Jd(){var e=z.A;return z.A=Xy,e}function Gs(){De=4,Rl||(se&4194048)!==se&&At.current!==null||(qa=!0),(Al&134217727)===0&&(la&134217727)===0||Ee===null||wl(Ee,se,Ot,!1)}function Xs(e,t,l){var a=he;he|=2;var u=kd(),i=Jd();(Ee!==e||se!==t)&&(ii=null,Xa(e,t)),t=!1;var f=De;e:do try{if(me!==0&&ie!==null){var m=ie,b=pt;switch(me){case 8:Ys(),f=6;break e;case 3:case 2:case 9:case 6:At.current===null&&(t=!0);var R=me;if(me=0,pt=null,Qa(e,m,b,R),l&&qa){f=0;break e}break;default:R=me,me=0,pt=null,Qa(e,m,b,R)}}Zy(),f=De;break}catch(M){Kd(e,M)}while(!0);return t&&e.shellSuspendCounter++,Jt=Fl=null,he=a,z.H=u,z.A=i,ie===null&&(Ee=null,se=0,wu()),f}function Zy(){for(;ie!==null;)$d(ie)}function Ky(e,t){var l=he;he|=2;var a=kd(),u=Jd();Ee!==e||se!==t?(ii=null,ui=Ht()+500,Xa(e,t)):qa=ln(e,t);e:do try{if(me!==0&&ie!==null){t=ie;var i=pt;t:switch(me){case 1:me=0,pt=null,Qa(e,t,i,1);break;case 2:case 9:if(rf(i)){me=0,pt=null,Fd(t);break}t=function(){me!==2&&me!==9||Ee!==e||(me=7),Gt(e)},i.then(t,t);break e;case 3:me=7;break e;case 4:me=5;break e;case 7:rf(i)?(me=0,pt=null,Fd(t)):(me=0,pt=null,Qa(e,t,i,7));break;case 5:var f=null;switch(ie.tag){case 26:f=ie.memoizedState;case 5:case 27:var m=ie;if(!f||Dh(f)){me=0,pt=null;var b=m.sibling;if(b!==null)ie=b;else{var R=m.return;R!==null?(ie=R,si(R)):ie=null}break t}}me=0,pt=null,Qa(e,t,i,5);break;case 6:me=0,pt=null,Qa(e,t,i,6);break;case 8:Ys(),De=6;break e;default:throw Error(s(462))}}ky();break}catch(M){Kd(e,M)}while(!0);return Jt=Fl=null,z.H=a,z.A=u,he=l,ie!==null?0:(Ee=null,se=0,wu(),De)}function ky(){for(;ie!==null&&!y0();)$d(ie)}function $d(e){var t=Ed(e.alternate,e,ll);e.memoizedProps=e.pendingProps,t===null?si(e):ie=t}function Fd(e){var t=e,l=t.alternate;switch(t.tag){case 15:case 0:t=pd(l,t,t.pendingProps,t.type,void 0,se);break;case 11:t=pd(l,t,t.pendingProps,t.type.render,t.ref,se);break;case 5:as(t);default:Td(l,t),t=ie=Fo(t,ll),t=Ed(l,t,ll)}e.memoizedProps=e.pendingProps,t===null?si(e):ie=t}function Qa(e,t,l,a){Jt=Fl=null,as(t),za=null,Dn=0;var u=t.return;try{if(Hy(e,u,t,l,se)){De=1,Iu(e,Et(l,e.current)),ie=null;return}}catch(i){if(u!==null)throw ie=u,i;De=1,Iu(e,Et(l,e.current)),ie=null;return}t.flags&32768?(de||a===1?e=!0:qa||(se&536870912)!==0?e=!1:(Rl=e=!0,(a===2||a===9||a===3||a===6)&&(a=At.current,a!==null&&a.tag===13&&(a.flags|=16384))),Wd(t,e)):si(t)}function si(e){var t=e;do{if((t.flags&32768)!==0){Wd(t,Rl);return}e=t.return;var l=qy(t.alternate,t,ll);if(l!==null){ie=l;return}if(t=t.sibling,t!==null){ie=t;return}ie=t=e}while(t!==null);De===0&&(De=5)}function Wd(e,t){do{var l=Ly(e.alternate,e);if(l!==null){l.flags&=32767,ie=l;return}if(l=e.return,l!==null&&(l.flags|=32768,l.subtreeFlags=0,l.deletions=null),!t&&(e=e.sibling,e!==null)){ie=e;return}ie=e=l}while(e!==null);De=6,ie=null}function Pd(e,t,l,a,u,i,f,m,b){e.cancelPendingCommit=null;do ci();while(Ve!==0);if((he&6)!==0)throw Error(s(327));if(t!==null){if(t===e.current)throw Error(s(177));if(i=t.lanes|t.childLanes,i|=Cr,R0(e,l,i,f,m,b),e===Ee&&(ie=Ee=null,se=0),Ya=t,jl=e,Ga=l,Bs=i,qs=u,Xd=a,(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?(e.callbackNode=null,e.callbackPriority=0,Wy(mu,function(){return ah(),null})):(e.callbackNode=null,e.callbackPriority=0),a=(t.flags&13878)!==0,(t.subtreeFlags&13878)!==0||a){a=z.T,z.T=null,u=Q.p,Q.p=2,f=he,he|=4;try{Yy(e,t,l)}finally{he=f,Q.p=u,z.T=a}}Ve=1,Id(),eh(),th()}}function Id(){if(Ve===1){Ve=0;var e=jl,t=Ya,l=(t.flags&13878)!==0;if((t.subtreeFlags&13878)!==0||l){l=z.T,z.T=null;var a=Q.p;Q.p=2;var u=he;he|=4;try{Ud(t,e);var i=ec,f=Yo(e.containerInfo),m=i.focusedElem,b=i.selectionRange;if(f!==m&&m&&m.ownerDocument&&Lo(m.ownerDocument.documentElement,m)){if(b!==null&&Or(m)){var R=b.start,M=b.end;if(M===void 0&&(M=R),"selectionStart"in m)m.selectionStart=R,m.selectionEnd=Math.min(M,m.value.length);else{var H=m.ownerDocument||document,j=H&&H.defaultView||window;if(j.getSelection){var w=j.getSelection(),ee=m.textContent.length,W=Math.min(b.start,ee),ge=b.end===void 0?W:Math.min(b.end,ee);!w.extend&&W>ge&&(f=ge,ge=W,W=f);var N=qo(m,W),E=qo(m,ge);if(N&&E&&(w.rangeCount!==1||w.anchorNode!==N.node||w.anchorOffset!==N.offset||w.focusNode!==E.node||w.focusOffset!==E.offset)){var T=H.createRange();T.setStart(N.node,N.offset),w.removeAllRanges(),W>ge?(w.addRange(T),w.extend(E.node,E.offset)):(T.setEnd(E.node,E.offset),w.addRange(T))}}}}for(H=[],w=m;w=w.parentNode;)w.nodeType===1&&H.push({element:w,left:w.scrollLeft,top:w.scrollTop});for(typeof m.focus=="function"&&m.focus(),m=0;m<H.length;m++){var U=H[m];U.element.scrollLeft=U.left,U.element.scrollTop=U.top}}Si=!!Is,ec=Is=null}finally{he=u,Q.p=a,z.T=l}}e.current=t,Ve=2}}function eh(){if(Ve===2){Ve=0;var e=jl,t=Ya,l=(t.flags&8772)!==0;if((t.subtreeFlags&8772)!==0||l){l=z.T,z.T=null;var a=Q.p;Q.p=2;var u=he;he|=4;try{Dd(e,t.alternate,t)}finally{he=u,Q.p=a,z.T=l}}Ve=3}}function th(){if(Ve===4||Ve===3){Ve=0,p0();var e=jl,t=Ya,l=Ga,a=Xd;(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?Ve=5:(Ve=0,Ya=jl=null,lh(e,e.pendingLanes));var u=e.pendingLanes;if(u===0&&(Ol=null),ir(l),t=t.stateNode,ot&&typeof ot.onCommitFiberRoot=="function")try{ot.onCommitFiberRoot(tn,t,void 0,(t.current.flags&128)===128)}catch{}if(a!==null){t=z.T,u=Q.p,Q.p=2,z.T=null;try{for(var i=e.onRecoverableError,f=0;f<a.length;f++){var m=a[f];i(m.value,{componentStack:m.stack})}}finally{z.T=t,Q.p=u}}(Ga&3)!==0&&ci(),Gt(e),u=e.pendingLanes,(l&4194090)!==0&&(u&42)!==0?e===Ls?Yn++:(Yn=0,Ls=e):Yn=0,Gn(0)}}function lh(e,t){(e.pooledCacheLanes&=t)===0&&(t=e.pooledCache,t!=null&&(e.pooledCache=null,xn(t)))}function ci(e){return Id(),eh(),th(),ah()}function ah(){if(Ve!==5)return!1;var e=jl,t=Bs;Bs=0;var l=ir(Ga),a=z.T,u=Q.p;try{Q.p=32>l?32:l,z.T=null,l=qs,qs=null;var i=jl,f=Ga;if(Ve=0,Ya=jl=null,Ga=0,(he&6)!==0)throw Error(s(331));var m=he;if(he|=4,Yd(i.current),Bd(i,i.current,f,l),he=m,Gn(0,!1),ot&&typeof ot.onPostCommitFiberRoot=="function")try{ot.onPostCommitFiberRoot(tn,i)}catch{}return!0}finally{Q.p=u,z.T=a,lh(e,t)}}function nh(e,t,l){t=Et(l,t),t=gs(e.stateNode,t,2),e=vl(e,t,2),e!==null&&(an(e,2),Gt(e))}function Se(e,t,l){if(e.tag===3)nh(e,e,l);else for(;t!==null;){if(t.tag===3){nh(t,e,l);break}else if(t.tag===1){var a=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof a.componentDidCatch=="function"&&(Ol===null||!Ol.has(a))){e=Et(l,e),l=sd(2),a=vl(t,l,2),a!==null&&(cd(l,a,t,e),an(a,2),Gt(a));break}}t=t.return}}function Qs(e,t,l){var a=e.pingCache;if(a===null){a=e.pingCache=new Qy;var u=new Set;a.set(t,u)}else u=a.get(t),u===void 0&&(u=new Set,a.set(t,u));u.has(l)||(Ms=!0,u.add(l),e=Jy.bind(null,e,t,l),t.then(e,e))}function Jy(e,t,l){var a=e.pingCache;a!==null&&a.delete(t),e.pingedLanes|=e.suspendedLanes&l,e.warmLanes&=~l,Ee===e&&(se&l)===l&&(De===4||De===3&&(se&62914560)===se&&300>Ht()-Hs?(he&2)===0&&Xa(e,0):zs|=l,La===se&&(La=0)),Gt(e)}function uh(e,t){t===0&&(t=Ic()),e=Ta(e,t),e!==null&&(an(e,t),Gt(e))}function $y(e){var t=e.memoizedState,l=0;t!==null&&(l=t.retryLane),uh(e,l)}function Fy(e,t){var l=0;switch(e.tag){case 13:var a=e.stateNode,u=e.memoizedState;u!==null&&(l=u.retryLane);break;case 19:a=e.stateNode;break;case 22:a=e.stateNode._retryCache;break;default:throw Error(s(314))}a!==null&&a.delete(t),uh(e,l)}function Wy(e,t){return lr(e,t)}var oi=null,Va=null,Vs=!1,fi=!1,Zs=!1,aa=0;function Gt(e){e!==Va&&e.next===null&&(Va===null?oi=Va=e:Va=Va.next=e),fi=!0,Vs||(Vs=!0,Iy())}function Gn(e,t){if(!Zs&&fi){Zs=!0;do for(var l=!1,a=oi;a!==null;){if(e!==0){var u=a.pendingLanes;if(u===0)var i=0;else{var f=a.suspendedLanes,m=a.pingedLanes;i=(1<<31-ft(42|e)+1)-1,i&=u&~(f&~m),i=i&201326741?i&201326741|1:i?i|2:0}i!==0&&(l=!0,ch(a,i))}else i=se,i=gu(a,a===Ee?i:0,a.cancelPendingCommit!==null||a.timeoutHandle!==-1),(i&3)===0||ln(a,i)||(l=!0,ch(a,i));a=a.next}while(l);Zs=!1}}function Py(){ih()}function ih(){fi=Vs=!1;var e=0;aa!==0&&(rp()&&(e=aa),aa=0);for(var t=Ht(),l=null,a=oi;a!==null;){var u=a.next,i=rh(a,t);i===0?(a.next=null,l===null?oi=u:l.next=u,u===null&&(Va=l)):(l=a,(e!==0||(i&3)!==0)&&(fi=!0)),a=u}Gn(e)}function rh(e,t){for(var l=e.suspendedLanes,a=e.pingedLanes,u=e.expirationTimes,i=e.pendingLanes&-62914561;0<i;){var f=31-ft(i),m=1<<f,b=u[f];b===-1?((m&l)===0||(m&a)!==0)&&(u[f]=T0(m,t)):b<=t&&(e.expiredLanes|=m),i&=~m}if(t=Ee,l=se,l=gu(e,e===t?l:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),a=e.callbackNode,l===0||e===t&&(me===2||me===9)||e.cancelPendingCommit!==null)return a!==null&&a!==null&&ar(a),e.callbackNode=null,e.callbackPriority=0;if((l&3)===0||ln(e,l)){if(t=l&-l,t===e.callbackPriority)return t;switch(a!==null&&ar(a),ir(l)){case 2:case 8:l=Fc;break;case 32:l=mu;break;case 268435456:l=Wc;break;default:l=mu}return a=sh.bind(null,e),l=lr(l,a),e.callbackPriority=t,e.callbackNode=l,t}return a!==null&&a!==null&&ar(a),e.callbackPriority=2,e.callbackNode=null,2}function sh(e,t){if(Ve!==0&&Ve!==5)return e.callbackNode=null,e.callbackPriority=0,null;var l=e.callbackNode;if(ci()&&e.callbackNode!==l)return null;var a=se;return a=gu(e,e===Ee?a:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),a===0?null:(Vd(e,a,t),rh(e,Ht()),e.callbackNode!=null&&e.callbackNode===l?sh.bind(null,e):null)}function ch(e,t){if(ci())return null;Vd(e,t,!0)}function Iy(){cp(function(){(he&6)!==0?lr($c,Py):ih()})}function Ks(){return aa===0&&(aa=Pc()),aa}function oh(e){return e==null||typeof e=="symbol"||typeof e=="boolean"?null:typeof e=="function"?e:Eu(""+e)}function fh(e,t){var l=t.ownerDocument.createElement("input");return l.name=t.name,l.value=t.value,e.id&&l.setAttribute("form",e.id),t.parentNode.insertBefore(l,t),e=new FormData(e),l.parentNode.removeChild(l),e}function ep(e,t,l,a,u){if(t==="submit"&&l&&l.stateNode===u){var i=oh((u[tt]||null).action),f=a.submitter;f&&(t=(t=f[tt]||null)?oh(t.formAction):f.getAttribute("formAction"),t!==null&&(i=t,f=null));var m=new Au("action","action",null,a,u);e.push({event:m,listeners:[{instance:null,listener:function(){if(a.defaultPrevented){if(aa!==0){var b=f?fh(u,f):new FormData(u);ds(l,{pending:!0,data:b,method:u.method,action:i},null,b)}}else typeof i=="function"&&(m.preventDefault(),b=f?fh(u,f):new FormData(u),ds(l,{pending:!0,data:b,method:u.method,action:i},i,b))},currentTarget:u}]})}}for(var ks=0;ks<Dr.length;ks++){var Js=Dr[ks],tp=Js.toLowerCase(),lp=Js[0].toUpperCase()+Js.slice(1);wt(tp,"on"+lp)}wt(Qo,"onAnimationEnd"),wt(Vo,"onAnimationIteration"),wt(Zo,"onAnimationStart"),wt("dblclick","onDoubleClick"),wt("focusin","onFocus"),wt("focusout","onBlur"),wt(by,"onTransitionRun"),wt(xy,"onTransitionStart"),wt(Sy,"onTransitionCancel"),wt(Ko,"onTransitionEnd"),ma("onMouseEnter",["mouseout","mouseover"]),ma("onMouseLeave",["mouseout","mouseover"]),ma("onPointerEnter",["pointerout","pointerover"]),ma("onPointerLeave",["pointerout","pointerover"]),Gl("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Gl("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Gl("onBeforeInput",["compositionend","keypress","textInput","paste"]),Gl("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Gl("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Gl("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Xn="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),ap=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Xn));function dh(e,t){t=(t&4)!==0;for(var l=0;l<e.length;l++){var a=e[l],u=a.event;a=a.listeners;e:{var i=void 0;if(t)for(var f=a.length-1;0<=f;f--){var m=a[f],b=m.instance,R=m.currentTarget;if(m=m.listener,b!==i&&u.isPropagationStopped())break e;i=m,u.currentTarget=R;try{i(u)}catch(M){Pu(M)}u.currentTarget=null,i=b}else for(f=0;f<a.length;f++){if(m=a[f],b=m.instance,R=m.currentTarget,m=m.listener,b!==i&&u.isPropagationStopped())break e;i=m,u.currentTarget=R;try{i(u)}catch(M){Pu(M)}u.currentTarget=null,i=b}}}}function re(e,t){var l=t[rr];l===void 0&&(l=t[rr]=new Set);var a=e+"__bubble";l.has(a)||(hh(t,e,2,!1),l.add(a))}function $s(e,t,l){var a=0;t&&(a|=4),hh(l,e,a,t)}var di="_reactListening"+Math.random().toString(36).slice(2);function Fs(e){if(!e[di]){e[di]=!0,no.forEach(function(l){l!=="selectionchange"&&(ap.has(l)||$s(l,!1,e),$s(l,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[di]||(t[di]=!0,$s("selectionchange",!1,t))}}function hh(e,t,l,a){switch(Bh(t)){case 2:var u=_p;break;case 8:u=Dp;break;default:u=oc}l=u.bind(null,t,l,e),u=void 0,!vr||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(u=!0),a?u!==void 0?e.addEventListener(t,l,{capture:!0,passive:u}):e.addEventListener(t,l,!0):u!==void 0?e.addEventListener(t,l,{passive:u}):e.addEventListener(t,l,!1)}function Ws(e,t,l,a,u){var i=a;if((t&1)===0&&(t&2)===0&&a!==null)e:for(;;){if(a===null)return;var f=a.tag;if(f===3||f===4){var m=a.stateNode.containerInfo;if(m===u)break;if(f===4)for(f=a.return;f!==null;){var b=f.tag;if((b===3||b===4)&&f.stateNode.containerInfo===u)return;f=f.return}for(;m!==null;){if(f=fa(m),f===null)return;if(b=f.tag,b===5||b===6||b===26||b===27){a=i=f;continue e}m=m.parentNode}}a=a.return}bo(function(){var R=i,M=pr(l),H=[];e:{var j=ko.get(e);if(j!==void 0){var w=Au,ee=e;switch(e){case"keypress":if(Tu(l)===0)break e;case"keydown":case"keyup":w=W0;break;case"focusin":ee="focus",w=Er;break;case"focusout":ee="blur",w=Er;break;case"beforeblur":case"afterblur":w=Er;break;case"click":if(l.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":w=Eo;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":w=L0;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":w=ey;break;case Qo:case Vo:case Zo:w=X0;break;case Ko:w=ly;break;case"scroll":case"scrollend":w=B0;break;case"wheel":w=ny;break;case"copy":case"cut":case"paste":w=V0;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":w=To;break;case"toggle":case"beforetoggle":w=iy}var W=(t&4)!==0,ge=!W&&(e==="scroll"||e==="scrollend"),N=W?j!==null?j+"Capture":null:j;W=[];for(var E=R,T;E!==null;){var U=E;if(T=U.stateNode,U=U.tag,U!==5&&U!==26&&U!==27||T===null||N===null||(U=rn(E,N),U!=null&&W.push(Qn(E,U,T))),ge)break;E=E.return}0<W.length&&(j=new w(j,ee,null,l,M),H.push({event:j,listeners:W}))}}if((t&7)===0){e:{if(j=e==="mouseover"||e==="pointerover",w=e==="mouseout"||e==="pointerout",j&&l!==yr&&(ee=l.relatedTarget||l.fromElement)&&(fa(ee)||ee[oa]))break e;if((w||j)&&(j=M.window===M?M:(j=M.ownerDocument)?j.defaultView||j.parentWindow:window,w?(ee=l.relatedTarget||l.toElement,w=R,ee=ee?fa(ee):null,ee!==null&&(ge=d(ee),W=ee.tag,ee!==ge||W!==5&&W!==27&&W!==6)&&(ee=null)):(w=null,ee=R),w!==ee)){if(W=Eo,U="onMouseLeave",N="onMouseEnter",E="mouse",(e==="pointerout"||e==="pointerover")&&(W=To,U="onPointerLeave",N="onPointerEnter",E="pointer"),ge=w==null?j:un(w),T=ee==null?j:un(ee),j=new W(U,E+"leave",w,l,M),j.target=ge,j.relatedTarget=T,U=null,fa(M)===R&&(W=new W(N,E+"enter",ee,l,M),W.target=T,W.relatedTarget=ge,U=W),ge=U,w&&ee)t:{for(W=w,N=ee,E=0,T=W;T;T=Za(T))E++;for(T=0,U=N;U;U=Za(U))T++;for(;0<E-T;)W=Za(W),E--;for(;0<T-E;)N=Za(N),T--;for(;E--;){if(W===N||N!==null&&W===N.alternate)break t;W=Za(W),N=Za(N)}W=null}else W=null;w!==null&&mh(H,j,w,W,!1),ee!==null&&ge!==null&&mh(H,ge,ee,W,!0)}}e:{if(j=R?un(R):window,w=j.nodeName&&j.nodeName.toLowerCase(),w==="select"||w==="input"&&j.type==="file")var Z=Co;else if(_o(j))if(Mo)Z=py;else{Z=my;var ue=hy}else w=j.nodeName,!w||w.toLowerCase()!=="input"||j.type!=="checkbox"&&j.type!=="radio"?R&&mr(R.elementType)&&(Z=Co):Z=yy;if(Z&&(Z=Z(e,R))){Do(H,Z,l,M);break e}ue&&ue(e,j,R),e==="focusout"&&R&&j.type==="number"&&R.memoizedProps.value!=null&&hr(j,"number",j.value)}switch(ue=R?un(R):window,e){case"focusin":(_o(ue)||ue.contentEditable==="true")&&(Sa=ue,jr=R,yn=null);break;case"focusout":yn=jr=Sa=null;break;case"mousedown":wr=!0;break;case"contextmenu":case"mouseup":case"dragend":wr=!1,Go(H,l,M);break;case"selectionchange":if(vy)break;case"keydown":case"keyup":Go(H,l,M)}var J;if(Tr)e:{switch(e){case"compositionstart":var P="onCompositionStart";break e;case"compositionend":P="onCompositionEnd";break e;case"compositionupdate":P="onCompositionUpdate";break e}P=void 0}else xa?jo(e,l)&&(P="onCompositionEnd"):e==="keydown"&&l.keyCode===229&&(P="onCompositionStart");P&&(Ro&&l.locale!=="ko"&&(xa||P!=="onCompositionStart"?P==="onCompositionEnd"&&xa&&(J=xo()):(ml=M,br="value"in ml?ml.value:ml.textContent,xa=!0)),ue=hi(R,P),0<ue.length&&(P=new No(P,e,null,l,M),H.push({event:P,listeners:ue}),J?P.data=J:(J=wo(l),J!==null&&(P.data=J)))),(J=sy?cy(e,l):oy(e,l))&&(P=hi(R,"onBeforeInput"),0<P.length&&(ue=new No("onBeforeInput","beforeinput",null,l,M),H.push({event:ue,listeners:P}),ue.data=J)),ep(H,e,R,l,M)}dh(H,t)})}function Qn(e,t,l){return{instance:e,listener:t,currentTarget:l}}function hi(e,t){for(var l=t+"Capture",a=[];e!==null;){var u=e,i=u.stateNode;if(u=u.tag,u!==5&&u!==26&&u!==27||i===null||(u=rn(e,l),u!=null&&a.unshift(Qn(e,u,i)),u=rn(e,t),u!=null&&a.push(Qn(e,u,i))),e.tag===3)return a;e=e.return}return[]}function Za(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5&&e.tag!==27);return e||null}function mh(e,t,l,a,u){for(var i=t._reactName,f=[];l!==null&&l!==a;){var m=l,b=m.alternate,R=m.stateNode;if(m=m.tag,b!==null&&b===a)break;m!==5&&m!==26&&m!==27||R===null||(b=R,u?(R=rn(l,i),R!=null&&f.unshift(Qn(l,R,b))):u||(R=rn(l,i),R!=null&&f.push(Qn(l,R,b)))),l=l.return}f.length!==0&&e.push({event:t,listeners:f})}var np=/\r\n?/g,up=/\u0000|\uFFFD/g;function yh(e){return(typeof e=="string"?e:""+e).replace(np,`
`).replace(up,"")}function ph(e,t){return t=yh(t),yh(e)===t}function mi(){}function pe(e,t,l,a,u,i){switch(l){case"children":typeof a=="string"?t==="body"||t==="textarea"&&a===""||ga(e,a):(typeof a=="number"||typeof a=="bigint")&&t!=="body"&&ga(e,""+a);break;case"className":bu(e,"class",a);break;case"tabIndex":bu(e,"tabindex",a);break;case"dir":case"role":case"viewBox":case"width":case"height":bu(e,l,a);break;case"style":go(e,a,i);break;case"data":if(t!=="object"){bu(e,"data",a);break}case"src":case"href":if(a===""&&(t!=="a"||l!=="href")){e.removeAttribute(l);break}if(a==null||typeof a=="function"||typeof a=="symbol"||typeof a=="boolean"){e.removeAttribute(l);break}a=Eu(""+a),e.setAttribute(l,a);break;case"action":case"formAction":if(typeof a=="function"){e.setAttribute(l,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof i=="function"&&(l==="formAction"?(t!=="input"&&pe(e,t,"name",u.name,u,null),pe(e,t,"formEncType",u.formEncType,u,null),pe(e,t,"formMethod",u.formMethod,u,null),pe(e,t,"formTarget",u.formTarget,u,null)):(pe(e,t,"encType",u.encType,u,null),pe(e,t,"method",u.method,u,null),pe(e,t,"target",u.target,u,null)));if(a==null||typeof a=="symbol"||typeof a=="boolean"){e.removeAttribute(l);break}a=Eu(""+a),e.setAttribute(l,a);break;case"onClick":a!=null&&(e.onclick=mi);break;case"onScroll":a!=null&&re("scroll",e);break;case"onScrollEnd":a!=null&&re("scrollend",e);break;case"dangerouslySetInnerHTML":if(a!=null){if(typeof a!="object"||!("__html"in a))throw Error(s(61));if(l=a.__html,l!=null){if(u.children!=null)throw Error(s(60));e.innerHTML=l}}break;case"multiple":e.multiple=a&&typeof a!="function"&&typeof a!="symbol";break;case"muted":e.muted=a&&typeof a!="function"&&typeof a!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(a==null||typeof a=="function"||typeof a=="boolean"||typeof a=="symbol"){e.removeAttribute("xlink:href");break}l=Eu(""+a),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",l);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":a!=null&&typeof a!="function"&&typeof a!="symbol"?e.setAttribute(l,""+a):e.removeAttribute(l);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":a&&typeof a!="function"&&typeof a!="symbol"?e.setAttribute(l,""):e.removeAttribute(l);break;case"capture":case"download":a===!0?e.setAttribute(l,""):a!==!1&&a!=null&&typeof a!="function"&&typeof a!="symbol"?e.setAttribute(l,a):e.removeAttribute(l);break;case"cols":case"rows":case"size":case"span":a!=null&&typeof a!="function"&&typeof a!="symbol"&&!isNaN(a)&&1<=a?e.setAttribute(l,a):e.removeAttribute(l);break;case"rowSpan":case"start":a==null||typeof a=="function"||typeof a=="symbol"||isNaN(a)?e.removeAttribute(l):e.setAttribute(l,a);break;case"popover":re("beforetoggle",e),re("toggle",e),vu(e,"popover",a);break;case"xlinkActuate":Qt(e,"http://www.w3.org/1999/xlink","xlink:actuate",a);break;case"xlinkArcrole":Qt(e,"http://www.w3.org/1999/xlink","xlink:arcrole",a);break;case"xlinkRole":Qt(e,"http://www.w3.org/1999/xlink","xlink:role",a);break;case"xlinkShow":Qt(e,"http://www.w3.org/1999/xlink","xlink:show",a);break;case"xlinkTitle":Qt(e,"http://www.w3.org/1999/xlink","xlink:title",a);break;case"xlinkType":Qt(e,"http://www.w3.org/1999/xlink","xlink:type",a);break;case"xmlBase":Qt(e,"http://www.w3.org/XML/1998/namespace","xml:base",a);break;case"xmlLang":Qt(e,"http://www.w3.org/XML/1998/namespace","xml:lang",a);break;case"xmlSpace":Qt(e,"http://www.w3.org/XML/1998/namespace","xml:space",a);break;case"is":vu(e,"is",a);break;case"innerText":case"textContent":break;default:(!(2<l.length)||l[0]!=="o"&&l[0]!=="O"||l[1]!=="n"&&l[1]!=="N")&&(l=U0.get(l)||l,vu(e,l,a))}}function Ps(e,t,l,a,u,i){switch(l){case"style":go(e,a,i);break;case"dangerouslySetInnerHTML":if(a!=null){if(typeof a!="object"||!("__html"in a))throw Error(s(61));if(l=a.__html,l!=null){if(u.children!=null)throw Error(s(60));e.innerHTML=l}}break;case"children":typeof a=="string"?ga(e,a):(typeof a=="number"||typeof a=="bigint")&&ga(e,""+a);break;case"onScroll":a!=null&&re("scroll",e);break;case"onScrollEnd":a!=null&&re("scrollend",e);break;case"onClick":a!=null&&(e.onclick=mi);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!uo.hasOwnProperty(l))e:{if(l[0]==="o"&&l[1]==="n"&&(u=l.endsWith("Capture"),t=l.slice(2,u?l.length-7:void 0),i=e[tt]||null,i=i!=null?i[l]:null,typeof i=="function"&&e.removeEventListener(t,i,u),typeof a=="function")){typeof i!="function"&&i!==null&&(l in e?e[l]=null:e.hasAttribute(l)&&e.removeAttribute(l)),e.addEventListener(t,a,u);break e}l in e?e[l]=a:a===!0?e.setAttribute(l,""):vu(e,l,a)}}}function Ze(e,t,l){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":re("error",e),re("load",e);var a=!1,u=!1,i;for(i in l)if(l.hasOwnProperty(i)){var f=l[i];if(f!=null)switch(i){case"src":a=!0;break;case"srcSet":u=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(s(137,t));default:pe(e,t,i,f,l,null)}}u&&pe(e,t,"srcSet",l.srcSet,l,null),a&&pe(e,t,"src",l.src,l,null);return;case"input":re("invalid",e);var m=i=f=u=null,b=null,R=null;for(a in l)if(l.hasOwnProperty(a)){var M=l[a];if(M!=null)switch(a){case"name":u=M;break;case"type":f=M;break;case"checked":b=M;break;case"defaultChecked":R=M;break;case"value":i=M;break;case"defaultValue":m=M;break;case"children":case"dangerouslySetInnerHTML":if(M!=null)throw Error(s(137,t));break;default:pe(e,t,a,M,l,null)}}ho(e,i,m,b,R,f,u,!1),xu(e);return;case"select":re("invalid",e),a=f=i=null;for(u in l)if(l.hasOwnProperty(u)&&(m=l[u],m!=null))switch(u){case"value":i=m;break;case"defaultValue":f=m;break;case"multiple":a=m;default:pe(e,t,u,m,l,null)}t=i,l=f,e.multiple=!!a,t!=null?pa(e,!!a,t,!1):l!=null&&pa(e,!!a,l,!0);return;case"textarea":re("invalid",e),i=u=a=null;for(f in l)if(l.hasOwnProperty(f)&&(m=l[f],m!=null))switch(f){case"value":a=m;break;case"defaultValue":u=m;break;case"children":i=m;break;case"dangerouslySetInnerHTML":if(m!=null)throw Error(s(91));break;default:pe(e,t,f,m,l,null)}yo(e,a,u,i),xu(e);return;case"option":for(b in l)if(l.hasOwnProperty(b)&&(a=l[b],a!=null))switch(b){case"selected":e.selected=a&&typeof a!="function"&&typeof a!="symbol";break;default:pe(e,t,b,a,l,null)}return;case"dialog":re("beforetoggle",e),re("toggle",e),re("cancel",e),re("close",e);break;case"iframe":case"object":re("load",e);break;case"video":case"audio":for(a=0;a<Xn.length;a++)re(Xn[a],e);break;case"image":re("error",e),re("load",e);break;case"details":re("toggle",e);break;case"embed":case"source":case"link":re("error",e),re("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(R in l)if(l.hasOwnProperty(R)&&(a=l[R],a!=null))switch(R){case"children":case"dangerouslySetInnerHTML":throw Error(s(137,t));default:pe(e,t,R,a,l,null)}return;default:if(mr(t)){for(M in l)l.hasOwnProperty(M)&&(a=l[M],a!==void 0&&Ps(e,t,M,a,l,void 0));return}}for(m in l)l.hasOwnProperty(m)&&(a=l[m],a!=null&&pe(e,t,m,a,l,null))}function ip(e,t,l,a){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var u=null,i=null,f=null,m=null,b=null,R=null,M=null;for(w in l){var H=l[w];if(l.hasOwnProperty(w)&&H!=null)switch(w){case"checked":break;case"value":break;case"defaultValue":b=H;default:a.hasOwnProperty(w)||pe(e,t,w,null,a,H)}}for(var j in a){var w=a[j];if(H=l[j],a.hasOwnProperty(j)&&(w!=null||H!=null))switch(j){case"type":i=w;break;case"name":u=w;break;case"checked":R=w;break;case"defaultChecked":M=w;break;case"value":f=w;break;case"defaultValue":m=w;break;case"children":case"dangerouslySetInnerHTML":if(w!=null)throw Error(s(137,t));break;default:w!==H&&pe(e,t,j,w,a,H)}}dr(e,f,m,b,R,M,i,u);return;case"select":w=f=m=j=null;for(i in l)if(b=l[i],l.hasOwnProperty(i)&&b!=null)switch(i){case"value":break;case"multiple":w=b;default:a.hasOwnProperty(i)||pe(e,t,i,null,a,b)}for(u in a)if(i=a[u],b=l[u],a.hasOwnProperty(u)&&(i!=null||b!=null))switch(u){case"value":j=i;break;case"defaultValue":m=i;break;case"multiple":f=i;default:i!==b&&pe(e,t,u,i,a,b)}t=m,l=f,a=w,j!=null?pa(e,!!l,j,!1):!!a!=!!l&&(t!=null?pa(e,!!l,t,!0):pa(e,!!l,l?[]:"",!1));return;case"textarea":w=j=null;for(m in l)if(u=l[m],l.hasOwnProperty(m)&&u!=null&&!a.hasOwnProperty(m))switch(m){case"value":break;case"children":break;default:pe(e,t,m,null,a,u)}for(f in a)if(u=a[f],i=l[f],a.hasOwnProperty(f)&&(u!=null||i!=null))switch(f){case"value":j=u;break;case"defaultValue":w=u;break;case"children":break;case"dangerouslySetInnerHTML":if(u!=null)throw Error(s(91));break;default:u!==i&&pe(e,t,f,u,a,i)}mo(e,j,w);return;case"option":for(var ee in l)if(j=l[ee],l.hasOwnProperty(ee)&&j!=null&&!a.hasOwnProperty(ee))switch(ee){case"selected":e.selected=!1;break;default:pe(e,t,ee,null,a,j)}for(b in a)if(j=a[b],w=l[b],a.hasOwnProperty(b)&&j!==w&&(j!=null||w!=null))switch(b){case"selected":e.selected=j&&typeof j!="function"&&typeof j!="symbol";break;default:pe(e,t,b,j,a,w)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var W in l)j=l[W],l.hasOwnProperty(W)&&j!=null&&!a.hasOwnProperty(W)&&pe(e,t,W,null,a,j);for(R in a)if(j=a[R],w=l[R],a.hasOwnProperty(R)&&j!==w&&(j!=null||w!=null))switch(R){case"children":case"dangerouslySetInnerHTML":if(j!=null)throw Error(s(137,t));break;default:pe(e,t,R,j,a,w)}return;default:if(mr(t)){for(var ge in l)j=l[ge],l.hasOwnProperty(ge)&&j!==void 0&&!a.hasOwnProperty(ge)&&Ps(e,t,ge,void 0,a,j);for(M in a)j=a[M],w=l[M],!a.hasOwnProperty(M)||j===w||j===void 0&&w===void 0||Ps(e,t,M,j,a,w);return}}for(var N in l)j=l[N],l.hasOwnProperty(N)&&j!=null&&!a.hasOwnProperty(N)&&pe(e,t,N,null,a,j);for(H in a)j=a[H],w=l[H],!a.hasOwnProperty(H)||j===w||j==null&&w==null||pe(e,t,H,j,a,w)}var Is=null,ec=null;function yi(e){return e.nodeType===9?e:e.ownerDocument}function gh(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function vh(e,t){if(e===0)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return e===1&&t==="foreignObject"?0:e}function tc(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.children=="bigint"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var lc=null;function rp(){var e=window.event;return e&&e.type==="popstate"?e===lc?!1:(lc=e,!0):(lc=null,!1)}var bh=typeof setTimeout=="function"?setTimeout:void 0,sp=typeof clearTimeout=="function"?clearTimeout:void 0,xh=typeof Promise=="function"?Promise:void 0,cp=typeof queueMicrotask=="function"?queueMicrotask:typeof xh<"u"?function(e){return xh.resolve(null).then(e).catch(op)}:bh;function op(e){setTimeout(function(){throw e})}function _l(e){return e==="head"}function Sh(e,t){var l=t,a=0,u=0;do{var i=l.nextSibling;if(e.removeChild(l),i&&i.nodeType===8)if(l=i.data,l==="/$"){if(0<a&&8>a){l=a;var f=e.ownerDocument;if(l&1&&Vn(f.documentElement),l&2&&Vn(f.body),l&4)for(l=f.head,Vn(l),f=l.firstChild;f;){var m=f.nextSibling,b=f.nodeName;f[nn]||b==="SCRIPT"||b==="STYLE"||b==="LINK"&&f.rel.toLowerCase()==="stylesheet"||l.removeChild(f),f=m}}if(u===0){e.removeChild(i),Pn(t);return}u--}else l==="$"||l==="$?"||l==="$!"?u++:a=l.charCodeAt(0)-48;else a=0;l=i}while(l);Pn(t)}function ac(e){var t=e.firstChild;for(t&&t.nodeType===10&&(t=t.nextSibling);t;){var l=t;switch(t=t.nextSibling,l.nodeName){case"HTML":case"HEAD":case"BODY":ac(l),sr(l);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(l.rel.toLowerCase()==="stylesheet")continue}e.removeChild(l)}}function fp(e,t,l,a){for(;e.nodeType===1;){var u=l;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!a&&(e.nodeName!=="INPUT"||e.type!=="hidden"))break}else if(a){if(!e[nn])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if(i=e.getAttribute("rel"),i==="stylesheet"&&e.hasAttribute("data-precedence"))break;if(i!==u.rel||e.getAttribute("href")!==(u.href==null||u.href===""?null:u.href)||e.getAttribute("crossorigin")!==(u.crossOrigin==null?null:u.crossOrigin)||e.getAttribute("title")!==(u.title==null?null:u.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(i=e.getAttribute("src"),(i!==(u.src==null?null:u.src)||e.getAttribute("type")!==(u.type==null?null:u.type)||e.getAttribute("crossorigin")!==(u.crossOrigin==null?null:u.crossOrigin))&&i&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else if(t==="input"&&e.type==="hidden"){var i=u.name==null?null:""+u.name;if(u.type==="hidden"&&e.getAttribute("name")===i)return e}else return e;if(e=Dt(e.nextSibling),e===null)break}return null}function dp(e,t,l){if(t==="")return null;for(;e.nodeType!==3;)if((e.nodeType!==1||e.nodeName!=="INPUT"||e.type!=="hidden")&&!l||(e=Dt(e.nextSibling),e===null))return null;return e}function nc(e){return e.data==="$!"||e.data==="$?"&&e.ownerDocument.readyState==="complete"}function hp(e,t){var l=e.ownerDocument;if(e.data!=="$?"||l.readyState==="complete")t();else{var a=function(){t(),l.removeEventListener("DOMContentLoaded",a)};l.addEventListener("DOMContentLoaded",a),e._reactRetry=a}}function Dt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?"||t==="F!"||t==="F")break;if(t==="/$")return null}}return e}var uc=null;function Eh(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var l=e.data;if(l==="$"||l==="$!"||l==="$?"){if(t===0)return e;t--}else l==="/$"&&t++}e=e.previousSibling}return null}function Nh(e,t,l){switch(t=yi(l),e){case"html":if(e=t.documentElement,!e)throw Error(s(452));return e;case"head":if(e=t.head,!e)throw Error(s(453));return e;case"body":if(e=t.body,!e)throw Error(s(454));return e;default:throw Error(s(451))}}function Vn(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);sr(e)}var jt=new Map,Th=new Set;function pi(e){return typeof e.getRootNode=="function"?e.getRootNode():e.nodeType===9?e:e.ownerDocument}var al=Q.d;Q.d={f:mp,r:yp,D:pp,C:gp,L:vp,m:bp,X:Sp,S:xp,M:Ep};function mp(){var e=al.f(),t=ri();return e||t}function yp(e){var t=da(e);t!==null&&t.tag===5&&t.type==="form"?Vf(t):al.r(e)}var Ka=typeof document>"u"?null:document;function Rh(e,t,l){var a=Ka;if(a&&typeof t=="string"&&t){var u=St(t);u='link[rel="'+e+'"][href="'+u+'"]',typeof l=="string"&&(u+='[crossorigin="'+l+'"]'),Th.has(u)||(Th.add(u),e={rel:e,crossOrigin:l,href:t},a.querySelector(u)===null&&(t=a.createElement("link"),Ze(t,"link",e),Le(t),a.head.appendChild(t)))}}function pp(e){al.D(e),Rh("dns-prefetch",e,null)}function gp(e,t){al.C(e,t),Rh("preconnect",e,t)}function vp(e,t,l){al.L(e,t,l);var a=Ka;if(a&&e&&t){var u='link[rel="preload"][as="'+St(t)+'"]';t==="image"&&l&&l.imageSrcSet?(u+='[imagesrcset="'+St(l.imageSrcSet)+'"]',typeof l.imageSizes=="string"&&(u+='[imagesizes="'+St(l.imageSizes)+'"]')):u+='[href="'+St(e)+'"]';var i=u;switch(t){case"style":i=ka(e);break;case"script":i=Ja(e)}jt.has(i)||(e=x({rel:"preload",href:t==="image"&&l&&l.imageSrcSet?void 0:e,as:t},l),jt.set(i,e),a.querySelector(u)!==null||t==="style"&&a.querySelector(Zn(i))||t==="script"&&a.querySelector(Kn(i))||(t=a.createElement("link"),Ze(t,"link",e),Le(t),a.head.appendChild(t)))}}function bp(e,t){al.m(e,t);var l=Ka;if(l&&e){var a=t&&typeof t.as=="string"?t.as:"script",u='link[rel="modulepreload"][as="'+St(a)+'"][href="'+St(e)+'"]',i=u;switch(a){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":i=Ja(e)}if(!jt.has(i)&&(e=x({rel:"modulepreload",href:e},t),jt.set(i,e),l.querySelector(u)===null)){switch(a){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(l.querySelector(Kn(i)))return}a=l.createElement("link"),Ze(a,"link",e),Le(a),l.head.appendChild(a)}}}function xp(e,t,l){al.S(e,t,l);var a=Ka;if(a&&e){var u=ha(a).hoistableStyles,i=ka(e);t=t||"default";var f=u.get(i);if(!f){var m={loading:0,preload:null};if(f=a.querySelector(Zn(i)))m.loading=5;else{e=x({rel:"stylesheet",href:e,"data-precedence":t},l),(l=jt.get(i))&&ic(e,l);var b=f=a.createElement("link");Le(b),Ze(b,"link",e),b._p=new Promise(function(R,M){b.onload=R,b.onerror=M}),b.addEventListener("load",function(){m.loading|=1}),b.addEventListener("error",function(){m.loading|=2}),m.loading|=4,gi(f,t,a)}f={type:"stylesheet",instance:f,count:1,state:m},u.set(i,f)}}}function Sp(e,t){al.X(e,t);var l=Ka;if(l&&e){var a=ha(l).hoistableScripts,u=Ja(e),i=a.get(u);i||(i=l.querySelector(Kn(u)),i||(e=x({src:e,async:!0},t),(t=jt.get(u))&&rc(e,t),i=l.createElement("script"),Le(i),Ze(i,"link",e),l.head.appendChild(i)),i={type:"script",instance:i,count:1,state:null},a.set(u,i))}}function Ep(e,t){al.M(e,t);var l=Ka;if(l&&e){var a=ha(l).hoistableScripts,u=Ja(e),i=a.get(u);i||(i=l.querySelector(Kn(u)),i||(e=x({src:e,async:!0,type:"module"},t),(t=jt.get(u))&&rc(e,t),i=l.createElement("script"),Le(i),Ze(i,"link",e),l.head.appendChild(i)),i={type:"script",instance:i,count:1,state:null},a.set(u,i))}}function Ah(e,t,l,a){var u=(u=te.current)?pi(u):null;if(!u)throw Error(s(446));switch(e){case"meta":case"title":return null;case"style":return typeof l.precedence=="string"&&typeof l.href=="string"?(t=ka(l.href),l=ha(u).hoistableStyles,a=l.get(t),a||(a={type:"style",instance:null,count:0,state:null},l.set(t,a)),a):{type:"void",instance:null,count:0,state:null};case"link":if(l.rel==="stylesheet"&&typeof l.href=="string"&&typeof l.precedence=="string"){e=ka(l.href);var i=ha(u).hoistableStyles,f=i.get(e);if(f||(u=u.ownerDocument||u,f={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},i.set(e,f),(i=u.querySelector(Zn(e)))&&!i._p&&(f.instance=i,f.state.loading=5),jt.has(e)||(l={rel:"preload",as:"style",href:l.href,crossOrigin:l.crossOrigin,integrity:l.integrity,media:l.media,hrefLang:l.hrefLang,referrerPolicy:l.referrerPolicy},jt.set(e,l),i||Np(u,e,l,f.state))),t&&a===null)throw Error(s(528,""));return f}if(t&&a!==null)throw Error(s(529,""));return null;case"script":return t=l.async,l=l.src,typeof l=="string"&&t&&typeof t!="function"&&typeof t!="symbol"?(t=Ja(l),l=ha(u).hoistableScripts,a=l.get(t),a||(a={type:"script",instance:null,count:0,state:null},l.set(t,a)),a):{type:"void",instance:null,count:0,state:null};default:throw Error(s(444,e))}}function ka(e){return'href="'+St(e)+'"'}function Zn(e){return'link[rel="stylesheet"]['+e+"]"}function Oh(e){return x({},e,{"data-precedence":e.precedence,precedence:null})}function Np(e,t,l,a){e.querySelector('link[rel="preload"][as="style"]['+t+"]")?a.loading=1:(t=e.createElement("link"),a.preload=t,t.addEventListener("load",function(){return a.loading|=1}),t.addEventListener("error",function(){return a.loading|=2}),Ze(t,"link",l),Le(t),e.head.appendChild(t))}function Ja(e){return'[src="'+St(e)+'"]'}function Kn(e){return"script[async]"+e}function jh(e,t,l){if(t.count++,t.instance===null)switch(t.type){case"style":var a=e.querySelector('style[data-href~="'+St(l.href)+'"]');if(a)return t.instance=a,Le(a),a;var u=x({},l,{"data-href":l.href,"data-precedence":l.precedence,href:null,precedence:null});return a=(e.ownerDocument||e).createElement("style"),Le(a),Ze(a,"style",u),gi(a,l.precedence,e),t.instance=a;case"stylesheet":u=ka(l.href);var i=e.querySelector(Zn(u));if(i)return t.state.loading|=4,t.instance=i,Le(i),i;a=Oh(l),(u=jt.get(u))&&ic(a,u),i=(e.ownerDocument||e).createElement("link"),Le(i);var f=i;return f._p=new Promise(function(m,b){f.onload=m,f.onerror=b}),Ze(i,"link",a),t.state.loading|=4,gi(i,l.precedence,e),t.instance=i;case"script":return i=Ja(l.src),(u=e.querySelector(Kn(i)))?(t.instance=u,Le(u),u):(a=l,(u=jt.get(i))&&(a=x({},l),rc(a,u)),e=e.ownerDocument||e,u=e.createElement("script"),Le(u),Ze(u,"link",a),e.head.appendChild(u),t.instance=u);case"void":return null;default:throw Error(s(443,t.type))}else t.type==="stylesheet"&&(t.state.loading&4)===0&&(a=t.instance,t.state.loading|=4,gi(a,l.precedence,e));return t.instance}function gi(e,t,l){for(var a=l.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),u=a.length?a[a.length-1]:null,i=u,f=0;f<a.length;f++){var m=a[f];if(m.dataset.precedence===t)i=m;else if(i!==u)break}i?i.parentNode.insertBefore(e,i.nextSibling):(t=l.nodeType===9?l.head:l,t.insertBefore(e,t.firstChild))}function ic(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.title==null&&(e.title=t.title)}function rc(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.integrity==null&&(e.integrity=t.integrity)}var vi=null;function wh(e,t,l){if(vi===null){var a=new Map,u=vi=new Map;u.set(l,a)}else u=vi,a=u.get(l),a||(a=new Map,u.set(l,a));if(a.has(e))return a;for(a.set(e,null),l=l.getElementsByTagName(e),u=0;u<l.length;u++){var i=l[u];if(!(i[nn]||i[Je]||e==="link"&&i.getAttribute("rel")==="stylesheet")&&i.namespaceURI!=="http://www.w3.org/2000/svg"){var f=i.getAttribute(t)||"";f=e+f;var m=a.get(f);m?m.push(i):a.set(f,[i])}}return a}function _h(e,t,l){e=e.ownerDocument||e,e.head.insertBefore(l,t==="title"?e.querySelector("head > title"):null)}function Tp(e,t,l){if(l===1||t.itemProp!=null)return!1;switch(e){case"meta":case"title":return!0;case"style":if(typeof t.precedence!="string"||typeof t.href!="string"||t.href==="")break;return!0;case"link":if(typeof t.rel!="string"||typeof t.href!="string"||t.href===""||t.onLoad||t.onError)break;switch(t.rel){case"stylesheet":return e=t.disabled,typeof t.precedence=="string"&&e==null;default:return!0}case"script":if(t.async&&typeof t.async!="function"&&typeof t.async!="symbol"&&!t.onLoad&&!t.onError&&t.src&&typeof t.src=="string")return!0}return!1}function Dh(e){return!(e.type==="stylesheet"&&(e.state.loading&3)===0)}var kn=null;function Rp(){}function Ap(e,t,l){if(kn===null)throw Error(s(475));var a=kn;if(t.type==="stylesheet"&&(typeof l.media!="string"||matchMedia(l.media).matches!==!1)&&(t.state.loading&4)===0){if(t.instance===null){var u=ka(l.href),i=e.querySelector(Zn(u));if(i){e=i._p,e!==null&&typeof e=="object"&&typeof e.then=="function"&&(a.count++,a=bi.bind(a),e.then(a,a)),t.state.loading|=4,t.instance=i,Le(i);return}i=e.ownerDocument||e,l=Oh(l),(u=jt.get(u))&&ic(l,u),i=i.createElement("link"),Le(i);var f=i;f._p=new Promise(function(m,b){f.onload=m,f.onerror=b}),Ze(i,"link",l),t.instance=i}a.stylesheets===null&&(a.stylesheets=new Map),a.stylesheets.set(t,e),(e=t.state.preload)&&(t.state.loading&3)===0&&(a.count++,t=bi.bind(a),e.addEventListener("load",t),e.addEventListener("error",t))}}function Op(){if(kn===null)throw Error(s(475));var e=kn;return e.stylesheets&&e.count===0&&sc(e,e.stylesheets),0<e.count?function(t){var l=setTimeout(function(){if(e.stylesheets&&sc(e,e.stylesheets),e.unsuspend){var a=e.unsuspend;e.unsuspend=null,a()}},6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(l)}}:null}function bi(){if(this.count--,this.count===0){if(this.stylesheets)sc(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}}var xi=null;function sc(e,t){e.stylesheets=null,e.unsuspend!==null&&(e.count++,xi=new Map,t.forEach(jp,e),xi=null,bi.call(e))}function jp(e,t){if(!(t.state.loading&4)){var l=xi.get(e);if(l)var a=l.get(null);else{l=new Map,xi.set(e,l);for(var u=e.querySelectorAll("link[data-precedence],style[data-precedence]"),i=0;i<u.length;i++){var f=u[i];(f.nodeName==="LINK"||f.getAttribute("media")!=="not all")&&(l.set(f.dataset.precedence,f),a=f)}a&&l.set(null,a)}u=t.instance,f=u.getAttribute("data-precedence"),i=l.get(f)||a,i===a&&l.set(null,u),l.set(f,u),this.count++,a=bi.bind(this),u.addEventListener("load",a),u.addEventListener("error",a),i?i.parentNode.insertBefore(u,i.nextSibling):(e=e.nodeType===9?e.head:e,e.insertBefore(u,e.firstChild)),t.state.loading|=4}}var Jn={$$typeof:K,Provider:null,Consumer:null,_currentValue:I,_currentValue2:I,_threadCount:0};function wp(e,t,l,a,u,i,f,m){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=nr(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=nr(0),this.hiddenUpdates=nr(null),this.identifierPrefix=a,this.onUncaughtError=u,this.onCaughtError=i,this.onRecoverableError=f,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=m,this.incompleteTransitions=new Map}function Ch(e,t,l,a,u,i,f,m,b,R,M,H){return e=new wp(e,t,l,f,m,b,R,H),t=1,i===!0&&(t|=24),i=ht(3,null,null,t),e.current=i,i.stateNode=e,t=Qr(),t.refCount++,e.pooledCache=t,t.refCount++,i.memoizedState={element:a,isDehydrated:l,cache:t},kr(i),e}function Mh(e){return e?(e=Ra,e):Ra}function zh(e,t,l,a,u,i){u=Mh(u),a.context===null?a.context=u:a.pendingContext=u,a=gl(t),a.payload={element:l},i=i===void 0?null:i,i!==null&&(a.callback=i),l=vl(e,a,t),l!==null&&(vt(l,e,t),Tn(l,e,t))}function Uh(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var l=e.retryLane;e.retryLane=l!==0&&l<t?l:t}}function cc(e,t){Uh(e,t),(e=e.alternate)&&Uh(e,t)}function Hh(e){if(e.tag===13){var t=Ta(e,67108864);t!==null&&vt(t,e,67108864),cc(e,67108864)}}var Si=!0;function _p(e,t,l,a){var u=z.T;z.T=null;var i=Q.p;try{Q.p=2,oc(e,t,l,a)}finally{Q.p=i,z.T=u}}function Dp(e,t,l,a){var u=z.T;z.T=null;var i=Q.p;try{Q.p=8,oc(e,t,l,a)}finally{Q.p=i,z.T=u}}function oc(e,t,l,a){if(Si){var u=fc(a);if(u===null)Ws(e,t,a,Ei,l),qh(e,a);else if(Mp(u,e,t,l,a))a.stopPropagation();else if(qh(e,a),t&4&&-1<Cp.indexOf(e)){for(;u!==null;){var i=da(u);if(i!==null)switch(i.tag){case 3:if(i=i.stateNode,i.current.memoizedState.isDehydrated){var f=Yl(i.pendingLanes);if(f!==0){var m=i;for(m.pendingLanes|=2,m.entangledLanes|=2;f;){var b=1<<31-ft(f);m.entanglements[1]|=b,f&=~b}Gt(i),(he&6)===0&&(ui=Ht()+500,Gn(0))}}break;case 13:m=Ta(i,2),m!==null&&vt(m,i,2),ri(),cc(i,2)}if(i=fc(a),i===null&&Ws(e,t,a,Ei,l),i===u)break;u=i}u!==null&&a.stopPropagation()}else Ws(e,t,a,null,l)}}function fc(e){return e=pr(e),dc(e)}var Ei=null;function dc(e){if(Ei=null,e=fa(e),e!==null){var t=d(e);if(t===null)e=null;else{var l=t.tag;if(l===13){if(e=h(t),e!==null)return e;e=null}else if(l===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return Ei=e,null}function Bh(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(g0()){case $c:return 2;case Fc:return 8;case mu:case v0:return 32;case Wc:return 268435456;default:return 32}default:return 32}}var hc=!1,Dl=null,Cl=null,Ml=null,$n=new Map,Fn=new Map,zl=[],Cp="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function qh(e,t){switch(e){case"focusin":case"focusout":Dl=null;break;case"dragenter":case"dragleave":Cl=null;break;case"mouseover":case"mouseout":Ml=null;break;case"pointerover":case"pointerout":$n.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Fn.delete(t.pointerId)}}function Wn(e,t,l,a,u,i){return e===null||e.nativeEvent!==i?(e={blockedOn:t,domEventName:l,eventSystemFlags:a,nativeEvent:i,targetContainers:[u]},t!==null&&(t=da(t),t!==null&&Hh(t)),e):(e.eventSystemFlags|=a,t=e.targetContainers,u!==null&&t.indexOf(u)===-1&&t.push(u),e)}function Mp(e,t,l,a,u){switch(t){case"focusin":return Dl=Wn(Dl,e,t,l,a,u),!0;case"dragenter":return Cl=Wn(Cl,e,t,l,a,u),!0;case"mouseover":return Ml=Wn(Ml,e,t,l,a,u),!0;case"pointerover":var i=u.pointerId;return $n.set(i,Wn($n.get(i)||null,e,t,l,a,u)),!0;case"gotpointercapture":return i=u.pointerId,Fn.set(i,Wn(Fn.get(i)||null,e,t,l,a,u)),!0}return!1}function Lh(e){var t=fa(e.target);if(t!==null){var l=d(t);if(l!==null){if(t=l.tag,t===13){if(t=h(l),t!==null){e.blockedOn=t,A0(e.priority,function(){if(l.tag===13){var a=gt();a=ur(a);var u=Ta(l,a);u!==null&&vt(u,l,a),cc(l,a)}});return}}else if(t===3&&l.stateNode.current.memoizedState.isDehydrated){e.blockedOn=l.tag===3?l.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Ni(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var l=fc(e.nativeEvent);if(l===null){l=e.nativeEvent;var a=new l.constructor(l.type,l);yr=a,l.target.dispatchEvent(a),yr=null}else return t=da(l),t!==null&&Hh(t),e.blockedOn=l,!1;t.shift()}return!0}function Yh(e,t,l){Ni(e)&&l.delete(t)}function zp(){hc=!1,Dl!==null&&Ni(Dl)&&(Dl=null),Cl!==null&&Ni(Cl)&&(Cl=null),Ml!==null&&Ni(Ml)&&(Ml=null),$n.forEach(Yh),Fn.forEach(Yh)}function Ti(e,t){e.blockedOn===t&&(e.blockedOn=null,hc||(hc=!0,n.unstable_scheduleCallback(n.unstable_NormalPriority,zp)))}var Ri=null;function Gh(e){Ri!==e&&(Ri=e,n.unstable_scheduleCallback(n.unstable_NormalPriority,function(){Ri===e&&(Ri=null);for(var t=0;t<e.length;t+=3){var l=e[t],a=e[t+1],u=e[t+2];if(typeof a!="function"){if(dc(a||l)===null)continue;break}var i=da(l);i!==null&&(e.splice(t,3),t-=3,ds(i,{pending:!0,data:u,method:l.method,action:a},a,u))}}))}function Pn(e){function t(b){return Ti(b,e)}Dl!==null&&Ti(Dl,e),Cl!==null&&Ti(Cl,e),Ml!==null&&Ti(Ml,e),$n.forEach(t),Fn.forEach(t);for(var l=0;l<zl.length;l++){var a=zl[l];a.blockedOn===e&&(a.blockedOn=null)}for(;0<zl.length&&(l=zl[0],l.blockedOn===null);)Lh(l),l.blockedOn===null&&zl.shift();if(l=(e.ownerDocument||e).$$reactFormReplay,l!=null)for(a=0;a<l.length;a+=3){var u=l[a],i=l[a+1],f=u[tt]||null;if(typeof i=="function")f||Gh(l);else if(f){var m=null;if(i&&i.hasAttribute("formAction")){if(u=i,f=i[tt]||null)m=f.formAction;else if(dc(u)!==null)continue}else m=f.action;typeof m=="function"?l[a+1]=m:(l.splice(a,3),a-=3),Gh(l)}}}function mc(e){this._internalRoot=e}Ai.prototype.render=mc.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(s(409));var l=t.current,a=gt();zh(l,a,e,t,null,null)},Ai.prototype.unmount=mc.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;zh(e.current,2,null,e,null,null),ri(),t[oa]=null}};function Ai(e){this._internalRoot=e}Ai.prototype.unstable_scheduleHydration=function(e){if(e){var t=lo();e={blockedOn:null,target:e,priority:t};for(var l=0;l<zl.length&&t!==0&&t<zl[l].priority;l++);zl.splice(l,0,e),l===0&&Lh(e)}};var Xh=r.version;if(Xh!=="19.1.0")throw Error(s(527,Xh,"19.1.0"));Q.findDOMNode=function(e){var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(s(188)):(e=Object.keys(e).join(","),Error(s(268,e)));return e=g(t),e=e!==null?y(e):null,e=e===null?null:e.stateNode,e};var Up={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:z,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Oi=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Oi.isDisabled&&Oi.supportsFiber)try{tn=Oi.inject(Up),ot=Oi}catch{}}return eu.createRoot=function(e,t){if(!o(e))throw Error(s(299));var l=!1,a="",u=nd,i=ud,f=id,m=null;return t!=null&&(t.unstable_strictMode===!0&&(l=!0),t.identifierPrefix!==void 0&&(a=t.identifierPrefix),t.onUncaughtError!==void 0&&(u=t.onUncaughtError),t.onCaughtError!==void 0&&(i=t.onCaughtError),t.onRecoverableError!==void 0&&(f=t.onRecoverableError),t.unstable_transitionCallbacks!==void 0&&(m=t.unstable_transitionCallbacks)),t=Ch(e,1,!1,null,null,l,a,u,i,f,m,null),e[oa]=t.current,Fs(e),new mc(t)},eu.hydrateRoot=function(e,t,l){if(!o(e))throw Error(s(299));var a=!1,u="",i=nd,f=ud,m=id,b=null,R=null;return l!=null&&(l.unstable_strictMode===!0&&(a=!0),l.identifierPrefix!==void 0&&(u=l.identifierPrefix),l.onUncaughtError!==void 0&&(i=l.onUncaughtError),l.onCaughtError!==void 0&&(f=l.onCaughtError),l.onRecoverableError!==void 0&&(m=l.onRecoverableError),l.unstable_transitionCallbacks!==void 0&&(b=l.unstable_transitionCallbacks),l.formState!==void 0&&(R=l.formState)),t=Ch(e,1,!0,t,l??null,a,u,i,f,m,b,R),t.context=Mh(null),l=t.current,a=gt(),a=ur(a),u=gl(a),u.callback=null,vl(l,u,a),l=a,t.current.lanes=l,an(t,l),Gt(t),e[oa]=t.current,Fs(e),new Ai(t)},eu.version="19.1.0",eu}var Ph;function Kp(){if(Ph)return gc.exports;Ph=1;function n(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(n)}catch(r){console.error(r)}}return n(),gc.exports=Zp(),gc.exports}var kp=Kp();/**
 * react-router v7.7.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */var Ih="popstate";function Jp(n={}){function r(s,o){let{pathname:d,search:h,hash:v}=s.location;return jc("",{pathname:d,search:h,hash:v},o.state&&o.state.usr||null,o.state&&o.state.key||"default")}function c(s,o){return typeof o=="string"?o:uu(o)}return Fp(r,c,null,n)}function Oe(n,r){if(n===!1||n===null||typeof n>"u")throw new Error(r)}function Ct(n,r){if(!n){typeof console<"u"&&console.warn(r);try{throw new Error(r)}catch{}}}function $p(){return Math.random().toString(36).substring(2,10)}function em(n,r){return{usr:n.state,key:n.key,idx:r}}function jc(n,r,c=null,s){return{pathname:typeof n=="string"?n:n.pathname,search:"",hash:"",...typeof r=="string"?Fa(r):r,state:c,key:r&&r.key||s||$p()}}function uu({pathname:n="/",search:r="",hash:c=""}){return r&&r!=="?"&&(n+=r.charAt(0)==="?"?r:"?"+r),c&&c!=="#"&&(n+=c.charAt(0)==="#"?c:"#"+c),n}function Fa(n){let r={};if(n){let c=n.indexOf("#");c>=0&&(r.hash=n.substring(c),n=n.substring(0,c));let s=n.indexOf("?");s>=0&&(r.search=n.substring(s),n=n.substring(0,s)),n&&(r.pathname=n)}return r}function Fp(n,r,c,s={}){let{window:o=document.defaultView,v5Compat:d=!1}=s,h=o.history,v="POP",g=null,y=x();y==null&&(y=0,h.replaceState({...h.state,idx:y},""));function x(){return(h.state||{idx:null}).idx}function O(){v="POP";let B=x(),Y=B==null?null:B-y;y=B,g&&g({action:v,location:q.location,delta:Y})}function C(B,Y){v="PUSH";let $=jc(q.location,B,Y);y=x()+1;let K=em($,y),ce=q.createHref($);try{h.pushState(K,"",ce)}catch(k){if(k instanceof DOMException&&k.name==="DataCloneError")throw k;o.location.assign(ce)}d&&g&&g({action:v,location:q.location,delta:1})}function G(B,Y){v="REPLACE";let $=jc(q.location,B,Y);y=x();let K=em($,y),ce=q.createHref($);h.replaceState(K,"",ce),d&&g&&g({action:v,location:q.location,delta:0})}function D(B){return Wp(B)}let q={get action(){return v},get location(){return n(o,h)},listen(B){if(g)throw new Error("A history only accepts one active listener");return o.addEventListener(Ih,O),g=B,()=>{o.removeEventListener(Ih,O),g=null}},createHref(B){return r(o,B)},createURL:D,encodeLocation(B){let Y=D(B);return{pathname:Y.pathname,search:Y.search,hash:Y.hash}},push:C,replace:G,go(B){return h.go(B)}};return q}function Wp(n,r=!1){let c="http://localhost";typeof window<"u"&&(c=window.location.origin!=="null"?window.location.origin:window.location.href),Oe(c,"No window.location.(origin|href) available to create URL");let s=typeof n=="string"?n:uu(n);return s=s.replace(/ $/,"%20"),!r&&s.startsWith("//")&&(s=c+s),new URL(s,c)}function Sm(n,r,c="/"){return Pp(n,r,c,!1)}function Pp(n,r,c,s){let o=typeof r=="string"?Fa(r):r,d=sl(o.pathname||"/",c);if(d==null)return null;let h=Em(n);Ip(h);let v=null;for(let g=0;v==null&&g<h.length;++g){let y=og(d);v=sg(h[g],y,s)}return v}function Em(n,r=[],c=[],s=""){let o=(d,h,v)=>{let g={relativePath:v===void 0?d.path||"":v,caseSensitive:d.caseSensitive===!0,childrenIndex:h,route:d};g.relativePath.startsWith("/")&&(Oe(g.relativePath.startsWith(s),`Absolute route path "${g.relativePath}" nested under path "${s}" is not valid. An absolute child route path must start with the combined path of all its parent routes.`),g.relativePath=g.relativePath.slice(s.length));let y=rl([s,g.relativePath]),x=c.concat(g);d.children&&d.children.length>0&&(Oe(d.index!==!0,`Index routes must not have child routes. Please remove all child routes from route path "${y}".`),Em(d.children,r,x,y)),!(d.path==null&&!d.index)&&r.push({path:y,score:ig(y,d.index),routesMeta:x})};return n.forEach((d,h)=>{if(d.path===""||!d.path?.includes("?"))o(d,h);else for(let v of Nm(d.path))o(d,h,v)}),r}function Nm(n){let r=n.split("/");if(r.length===0)return[];let[c,...s]=r,o=c.endsWith("?"),d=c.replace(/\?$/,"");if(s.length===0)return o?[d,""]:[d];let h=Nm(s.join("/")),v=[];return v.push(...h.map(g=>g===""?d:[d,g].join("/"))),o&&v.push(...h),v.map(g=>n.startsWith("/")&&g===""?"/":g)}function Ip(n){n.sort((r,c)=>r.score!==c.score?c.score-r.score:rg(r.routesMeta.map(s=>s.childrenIndex),c.routesMeta.map(s=>s.childrenIndex)))}var eg=/^:[\w-]+$/,tg=3,lg=2,ag=1,ng=10,ug=-2,tm=n=>n==="*";function ig(n,r){let c=n.split("/"),s=c.length;return c.some(tm)&&(s+=ug),r&&(s+=lg),c.filter(o=>!tm(o)).reduce((o,d)=>o+(eg.test(d)?tg:d===""?ag:ng),s)}function rg(n,r){return n.length===r.length&&n.slice(0,-1).every((s,o)=>s===r[o])?n[n.length-1]-r[r.length-1]:0}function sg(n,r,c=!1){let{routesMeta:s}=n,o={},d="/",h=[];for(let v=0;v<s.length;++v){let g=s[v],y=v===s.length-1,x=d==="/"?r:r.slice(d.length)||"/",O=Bi({path:g.relativePath,caseSensitive:g.caseSensitive,end:y},x),C=g.route;if(!O&&y&&c&&!s[s.length-1].route.index&&(O=Bi({path:g.relativePath,caseSensitive:g.caseSensitive,end:!1},x)),!O)return null;Object.assign(o,O.params),h.push({params:o,pathname:rl([d,O.pathname]),pathnameBase:mg(rl([d,O.pathnameBase])),route:C}),O.pathnameBase!=="/"&&(d=rl([d,O.pathnameBase]))}return h}function Bi(n,r){typeof n=="string"&&(n={path:n,caseSensitive:!1,end:!0});let[c,s]=cg(n.path,n.caseSensitive,n.end),o=r.match(c);if(!o)return null;let d=o[0],h=d.replace(/(.)\/+$/,"$1"),v=o.slice(1);return{params:s.reduce((y,{paramName:x,isOptional:O},C)=>{if(x==="*"){let D=v[C]||"";h=d.slice(0,d.length-D.length).replace(/(.)\/+$/,"$1")}const G=v[C];return O&&!G?y[x]=void 0:y[x]=(G||"").replace(/%2F/g,"/"),y},{}),pathname:d,pathnameBase:h,pattern:n}}function cg(n,r=!1,c=!0){Ct(n==="*"||!n.endsWith("*")||n.endsWith("/*"),`Route path "${n}" will be treated as if it were "${n.replace(/\*$/,"/*")}" because the \`*\` character must always follow a \`/\` in the pattern. To get rid of this warning, please change the route path to "${n.replace(/\*$/,"/*")}".`);let s=[],o="^"+n.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(h,v,g)=>(s.push({paramName:v,isOptional:g!=null}),g?"/?([^\\/]+)?":"/([^\\/]+)"));return n.endsWith("*")?(s.push({paramName:"*"}),o+=n==="*"||n==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):c?o+="\\/*$":n!==""&&n!=="/"&&(o+="(?:(?=\\/|$))"),[new RegExp(o,r?void 0:"i"),s]}function og(n){try{return n.split("/").map(r=>decodeURIComponent(r).replace(/\//g,"%2F")).join("/")}catch(r){return Ct(!1,`The URL path "${n}" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (${r}).`),n}}function sl(n,r){if(r==="/")return n;if(!n.toLowerCase().startsWith(r.toLowerCase()))return null;let c=r.endsWith("/")?r.length-1:r.length,s=n.charAt(c);return s&&s!=="/"?null:n.slice(c)||"/"}function fg(n,r="/"){let{pathname:c,search:s="",hash:o=""}=typeof n=="string"?Fa(n):n;return{pathname:c?c.startsWith("/")?c:dg(c,r):r,search:yg(s),hash:pg(o)}}function dg(n,r){let c=r.replace(/\/+$/,"").split("/");return n.split("/").forEach(o=>{o===".."?c.length>1&&c.pop():o!=="."&&c.push(o)}),c.length>1?c.join("/"):"/"}function Sc(n,r,c,s){return`Cannot include a '${n}' character in a manually specified \`to.${r}\` field [${JSON.stringify(s)}].  Please separate it out to the \`to.${c}\` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.`}function hg(n){return n.filter((r,c)=>c===0||r.route.path&&r.route.path.length>0)}function Lc(n){let r=hg(n);return r.map((c,s)=>s===r.length-1?c.pathname:c.pathnameBase)}function Yc(n,r,c,s=!1){let o;typeof n=="string"?o=Fa(n):(o={...n},Oe(!o.pathname||!o.pathname.includes("?"),Sc("?","pathname","search",o)),Oe(!o.pathname||!o.pathname.includes("#"),Sc("#","pathname","hash",o)),Oe(!o.search||!o.search.includes("#"),Sc("#","search","hash",o)));let d=n===""||o.pathname==="",h=d?"/":o.pathname,v;if(h==null)v=c;else{let O=r.length-1;if(!s&&h.startsWith("..")){let C=h.split("/");for(;C[0]==="..";)C.shift(),O-=1;o.pathname=C.join("/")}v=O>=0?r[O]:"/"}let g=fg(o,v),y=h&&h!=="/"&&h.endsWith("/"),x=(d||h===".")&&c.endsWith("/");return!g.pathname.endsWith("/")&&(y||x)&&(g.pathname+="/"),g}var rl=n=>n.join("/").replace(/\/\/+/g,"/"),mg=n=>n.replace(/\/+$/,"").replace(/^\/*/,"/"),yg=n=>!n||n==="?"?"":n.startsWith("?")?n:"?"+n,pg=n=>!n||n==="#"?"":n.startsWith("#")?n:"#"+n;function gg(n){return n!=null&&typeof n.status=="number"&&typeof n.statusText=="string"&&typeof n.internal=="boolean"&&"data"in n}var Tm=["POST","PUT","PATCH","DELETE"];new Set(Tm);var vg=["GET",...Tm];new Set(vg);var Wa=A.createContext(null);Wa.displayName="DataRouter";var Qi=A.createContext(null);Qi.displayName="DataRouterState";A.createContext(!1);var Rm=A.createContext({isTransitioning:!1});Rm.displayName="ViewTransition";var bg=A.createContext(new Map);bg.displayName="Fetchers";var xg=A.createContext(null);xg.displayName="Await";var Mt=A.createContext(null);Mt.displayName="Navigation";var ru=A.createContext(null);ru.displayName="Location";var zt=A.createContext({outlet:null,matches:[],isDataRoute:!1});zt.displayName="Route";var Gc=A.createContext(null);Gc.displayName="RouteError";function Sg(n,{relative:r}={}){Oe(Pa(),"useHref() may be used only in the context of a <Router> component.");let{basename:c,navigator:s}=A.useContext(Mt),{hash:o,pathname:d,search:h}=su(n,{relative:r}),v=d;return c!=="/"&&(v=d==="/"?c:rl([c,d])),s.createHref({pathname:v,search:h,hash:o})}function Pa(){return A.useContext(ru)!=null}function ol(){return Oe(Pa(),"useLocation() may be used only in the context of a <Router> component."),A.useContext(ru).location}var Am="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function Om(n){A.useContext(Mt).static||A.useLayoutEffect(n)}function Vi(){let{isDataRoute:n}=A.useContext(zt);return n?Ug():Eg()}function Eg(){Oe(Pa(),"useNavigate() may be used only in the context of a <Router> component.");let n=A.useContext(Wa),{basename:r,navigator:c}=A.useContext(Mt),{matches:s}=A.useContext(zt),{pathname:o}=ol(),d=JSON.stringify(Lc(s)),h=A.useRef(!1);return Om(()=>{h.current=!0}),A.useCallback((g,y={})=>{if(Ct(h.current,Am),!h.current)return;if(typeof g=="number"){c.go(g);return}let x=Yc(g,JSON.parse(d),o,y.relative==="path");n==null&&r!=="/"&&(x.pathname=x.pathname==="/"?r:rl([r,x.pathname])),(y.replace?c.replace:c.push)(x,y.state,y)},[r,c,d,o,n])}A.createContext(null);function Ng(){let{matches:n}=A.useContext(zt),r=n[n.length-1];return r?r.params:{}}function su(n,{relative:r}={}){let{matches:c}=A.useContext(zt),{pathname:s}=ol(),o=JSON.stringify(Lc(c));return A.useMemo(()=>Yc(n,JSON.parse(o),s,r==="path"),[n,o,s,r])}function Tg(n,r){return jm(n,r)}function jm(n,r,c,s){Oe(Pa(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:o}=A.useContext(Mt),{matches:d}=A.useContext(zt),h=d[d.length-1],v=h?h.params:{},g=h?h.pathname:"/",y=h?h.pathnameBase:"/",x=h&&h.route;{let Y=x&&x.path||"";wm(g,!x||Y.endsWith("*")||Y.endsWith("*?"),`You rendered descendant <Routes> (or called \`useRoutes()\`) at "${g}" (under <Route path="${Y}">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.

Please change the parent <Route path="${Y}"> to <Route path="${Y==="/"?"*":`${Y}/*`}">.`)}let O=ol(),C;if(r){let Y=typeof r=="string"?Fa(r):r;Oe(y==="/"||Y.pathname?.startsWith(y),`When overriding the location using \`<Routes location>\` or \`useRoutes(routes, location)\`, the location pathname must begin with the portion of the URL pathname that was matched by all parent routes. The current pathname base is "${y}" but pathname "${Y.pathname}" was given in the \`location\` prop.`),C=Y}else C=O;let G=C.pathname||"/",D=G;if(y!=="/"){let Y=y.replace(/^\//,"").split("/");D="/"+G.replace(/^\//,"").split("/").slice(Y.length).join("/")}let q=Sm(n,{pathname:D});Ct(x||q!=null,`No routes matched location "${C.pathname}${C.search}${C.hash}" `),Ct(q==null||q[q.length-1].route.element!==void 0||q[q.length-1].route.Component!==void 0||q[q.length-1].route.lazy!==void 0,`Matched leaf route at location "${C.pathname}${C.search}${C.hash}" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.`);let B=wg(q&&q.map(Y=>Object.assign({},Y,{params:Object.assign({},v,Y.params),pathname:rl([y,o.encodeLocation?o.encodeLocation(Y.pathname).pathname:Y.pathname]),pathnameBase:Y.pathnameBase==="/"?y:rl([y,o.encodeLocation?o.encodeLocation(Y.pathnameBase).pathname:Y.pathnameBase])})),d,c,s);return r&&B?A.createElement(ru.Provider,{value:{location:{pathname:"/",search:"",hash:"",state:null,key:"default",...C},navigationType:"POP"}},B):B}function Rg(){let n=zg(),r=gg(n)?`${n.status} ${n.statusText}`:n instanceof Error?n.message:JSON.stringify(n),c=n instanceof Error?n.stack:null,s="rgba(200,200,200, 0.5)",o={padding:"0.5rem",backgroundColor:s},d={padding:"2px 4px",backgroundColor:s},h=null;return console.error("Error handled by React Router default ErrorBoundary:",n),h=A.createElement(A.Fragment,null,A.createElement("p",null,"💿 Hey developer 👋"),A.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",A.createElement("code",{style:d},"ErrorBoundary")," or"," ",A.createElement("code",{style:d},"errorElement")," prop on your route.")),A.createElement(A.Fragment,null,A.createElement("h2",null,"Unexpected Application Error!"),A.createElement("h3",{style:{fontStyle:"italic"}},r),c?A.createElement("pre",{style:o},c):null,h)}var Ag=A.createElement(Rg,null),Og=class extends A.Component{constructor(n){super(n),this.state={location:n.location,revalidation:n.revalidation,error:n.error}}static getDerivedStateFromError(n){return{error:n}}static getDerivedStateFromProps(n,r){return r.location!==n.location||r.revalidation!=="idle"&&n.revalidation==="idle"?{error:n.error,location:n.location,revalidation:n.revalidation}:{error:n.error!==void 0?n.error:r.error,location:r.location,revalidation:n.revalidation||r.revalidation}}componentDidCatch(n,r){console.error("React Router caught the following error during render",n,r)}render(){return this.state.error!==void 0?A.createElement(zt.Provider,{value:this.props.routeContext},A.createElement(Gc.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function jg({routeContext:n,match:r,children:c}){let s=A.useContext(Wa);return s&&s.static&&s.staticContext&&(r.route.errorElement||r.route.ErrorBoundary)&&(s.staticContext._deepestRenderedBoundaryId=r.route.id),A.createElement(zt.Provider,{value:n},c)}function wg(n,r=[],c=null,s=null){if(n==null){if(!c)return null;if(c.errors)n=c.matches;else if(r.length===0&&!c.initialized&&c.matches.length>0)n=c.matches;else return null}let o=n,d=c?.errors;if(d!=null){let g=o.findIndex(y=>y.route.id&&d?.[y.route.id]!==void 0);Oe(g>=0,`Could not find a matching route for errors on route IDs: ${Object.keys(d).join(",")}`),o=o.slice(0,Math.min(o.length,g+1))}let h=!1,v=-1;if(c)for(let g=0;g<o.length;g++){let y=o[g];if((y.route.HydrateFallback||y.route.hydrateFallbackElement)&&(v=g),y.route.id){let{loaderData:x,errors:O}=c,C=y.route.loader&&!x.hasOwnProperty(y.route.id)&&(!O||O[y.route.id]===void 0);if(y.route.lazy||C){h=!0,v>=0?o=o.slice(0,v+1):o=[o[0]];break}}}return o.reduceRight((g,y,x)=>{let O,C=!1,G=null,D=null;c&&(O=d&&y.route.id?d[y.route.id]:void 0,G=y.route.errorElement||Ag,h&&(v<0&&x===0?(wm("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),C=!0,D=null):v===x&&(C=!0,D=y.route.hydrateFallbackElement||null)));let q=r.concat(o.slice(0,x+1)),B=()=>{let Y;return O?Y=G:C?Y=D:y.route.Component?Y=A.createElement(y.route.Component,null):y.route.element?Y=y.route.element:Y=g,A.createElement(jg,{match:y,routeContext:{outlet:g,matches:q,isDataRoute:c!=null},children:Y})};return c&&(y.route.ErrorBoundary||y.route.errorElement||x===0)?A.createElement(Og,{location:c.location,revalidation:c.revalidation,component:G,error:O,children:B(),routeContext:{outlet:null,matches:q,isDataRoute:!0}}):B()},null)}function Xc(n){return`${n} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function _g(n){let r=A.useContext(Wa);return Oe(r,Xc(n)),r}function Dg(n){let r=A.useContext(Qi);return Oe(r,Xc(n)),r}function Cg(n){let r=A.useContext(zt);return Oe(r,Xc(n)),r}function Qc(n){let r=Cg(n),c=r.matches[r.matches.length-1];return Oe(c.route.id,`${n} can only be used on routes that contain a unique "id"`),c.route.id}function Mg(){return Qc("useRouteId")}function zg(){let n=A.useContext(Gc),r=Dg("useRouteError"),c=Qc("useRouteError");return n!==void 0?n:r.errors?.[c]}function Ug(){let{router:n}=_g("useNavigate"),r=Qc("useNavigate"),c=A.useRef(!1);return Om(()=>{c.current=!0}),A.useCallback(async(o,d={})=>{Ct(c.current,Am),c.current&&(typeof o=="number"?n.navigate(o):await n.navigate(o,{fromRouteId:r,...d}))},[n,r])}var lm={};function wm(n,r,c){!r&&!lm[n]&&(lm[n]=!0,Ct(!1,c))}A.memo(Hg);function Hg({routes:n,future:r,state:c}){return jm(n,void 0,c,r)}function _m({to:n,replace:r,state:c,relative:s}){Oe(Pa(),"<Navigate> may be used only in the context of a <Router> component.");let{static:o}=A.useContext(Mt);Ct(!o,"<Navigate> must not be used on the initial render in a <StaticRouter>. This is a no-op, but you should modify your code so the <Navigate> is only ever rendered in response to some user interaction or state change.");let{matches:d}=A.useContext(zt),{pathname:h}=ol(),v=Vi(),g=Yc(n,Lc(d),h,s==="path"),y=JSON.stringify(g);return A.useEffect(()=>{v(JSON.parse(y),{replace:r,state:c,relative:s})},[v,y,s,r,c]),null}function ul(n){Oe(!1,"A <Route> is only ever to be used as the child of <Routes> element, never rendered directly. Please wrap your <Route> in a <Routes>.")}function Bg({basename:n="/",children:r=null,location:c,navigationType:s="POP",navigator:o,static:d=!1}){Oe(!Pa(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let h=n.replace(/^\/*/,"/"),v=A.useMemo(()=>({basename:h,navigator:o,static:d,future:{}}),[h,o,d]);typeof c=="string"&&(c=Fa(c));let{pathname:g="/",search:y="",hash:x="",state:O=null,key:C="default"}=c,G=A.useMemo(()=>{let D=sl(g,h);return D==null?null:{location:{pathname:D,search:y,hash:x,state:O,key:C},navigationType:s}},[h,g,y,x,O,C,s]);return Ct(G!=null,`<Router basename="${h}"> is not able to match the URL "${g}${y}${x}" because it does not start with the basename, so the <Router> won't render anything.`),G==null?null:A.createElement(Mt.Provider,{value:v},A.createElement(ru.Provider,{children:r,value:G}))}function qg({children:n,location:r}){return Tg(wc(n),r)}function wc(n,r=[]){let c=[];return A.Children.forEach(n,(s,o)=>{if(!A.isValidElement(s))return;let d=[...r,o];if(s.type===A.Fragment){c.push.apply(c,wc(s.props.children,d));return}Oe(s.type===ul,`[${typeof s.type=="string"?s.type:s.type.name}] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>`),Oe(!s.props.index||!s.props.children,"An index route cannot have child routes.");let h={id:s.props.id||d.join("-"),caseSensitive:s.props.caseSensitive,element:s.props.element,Component:s.props.Component,index:s.props.index,path:s.props.path,loader:s.props.loader,action:s.props.action,hydrateFallbackElement:s.props.hydrateFallbackElement,HydrateFallback:s.props.HydrateFallback,errorElement:s.props.errorElement,ErrorBoundary:s.props.ErrorBoundary,hasErrorBoundary:s.props.hasErrorBoundary===!0||s.props.ErrorBoundary!=null||s.props.errorElement!=null,shouldRevalidate:s.props.shouldRevalidate,handle:s.props.handle,lazy:s.props.lazy};s.props.children&&(h.children=wc(s.props.children,d)),c.push(h)}),c}var Di="get",Ci="application/x-www-form-urlencoded";function Zi(n){return n!=null&&typeof n.tagName=="string"}function Lg(n){return Zi(n)&&n.tagName.toLowerCase()==="button"}function Yg(n){return Zi(n)&&n.tagName.toLowerCase()==="form"}function Gg(n){return Zi(n)&&n.tagName.toLowerCase()==="input"}function Xg(n){return!!(n.metaKey||n.altKey||n.ctrlKey||n.shiftKey)}function Qg(n,r){return n.button===0&&(!r||r==="_self")&&!Xg(n)}var ji=null;function Vg(){if(ji===null)try{new FormData(document.createElement("form"),0),ji=!1}catch{ji=!0}return ji}var Zg=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function Ec(n){return n!=null&&!Zg.has(n)?(Ct(!1,`"${n}" is not a valid \`encType\` for \`<Form>\`/\`<fetcher.Form>\` and will default to "${Ci}"`),null):n}function Kg(n,r){let c,s,o,d,h;if(Yg(n)){let v=n.getAttribute("action");s=v?sl(v,r):null,c=n.getAttribute("method")||Di,o=Ec(n.getAttribute("enctype"))||Ci,d=new FormData(n)}else if(Lg(n)||Gg(n)&&(n.type==="submit"||n.type==="image")){let v=n.form;if(v==null)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let g=n.getAttribute("formaction")||v.getAttribute("action");if(s=g?sl(g,r):null,c=n.getAttribute("formmethod")||v.getAttribute("method")||Di,o=Ec(n.getAttribute("formenctype"))||Ec(v.getAttribute("enctype"))||Ci,d=new FormData(v,n),!Vg()){let{name:y,type:x,value:O}=n;if(x==="image"){let C=y?`${y}.`:"";d.append(`${C}x`,"0"),d.append(`${C}y`,"0")}else y&&d.append(y,O)}}else{if(Zi(n))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');c=Di,s=null,o=Ci,h=n}return d&&o==="text/plain"&&(h=d,d=void 0),{action:s,method:c.toLowerCase(),encType:o,formData:d,body:h}}Object.getOwnPropertyNames(Object.prototype).sort().join("\0");function Vc(n,r){if(n===!1||n===null||typeof n>"u")throw new Error(r)}function kg(n,r,c){let s=typeof n=="string"?new URL(n,typeof window>"u"?"server://singlefetch/":window.location.origin):n;return s.pathname==="/"?s.pathname=`_root.${c}`:r&&sl(s.pathname,r)==="/"?s.pathname=`${r.replace(/\/$/,"")}/_root.${c}`:s.pathname=`${s.pathname.replace(/\/$/,"")}.${c}`,s}async function Jg(n,r){if(n.id in r)return r[n.id];try{let c=await import(n.module);return r[n.id]=c,c}catch(c){return console.error(`Error loading route module \`${n.module}\`, reloading page...`),console.error(c),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise(()=>{})}}function $g(n){return n==null?!1:n.href==null?n.rel==="preload"&&typeof n.imageSrcSet=="string"&&typeof n.imageSizes=="string":typeof n.rel=="string"&&typeof n.href=="string"}async function Fg(n,r,c){let s=await Promise.all(n.map(async o=>{let d=r.routes[o.route.id];if(d){let h=await Jg(d,c);return h.links?h.links():[]}return[]}));return e1(s.flat(1).filter($g).filter(o=>o.rel==="stylesheet"||o.rel==="preload").map(o=>o.rel==="stylesheet"?{...o,rel:"prefetch",as:"style"}:{...o,rel:"prefetch"}))}function am(n,r,c,s,o,d){let h=(g,y)=>c[y]?g.route.id!==c[y].route.id:!0,v=(g,y)=>c[y].pathname!==g.pathname||c[y].route.path?.endsWith("*")&&c[y].params["*"]!==g.params["*"];return d==="assets"?r.filter((g,y)=>h(g,y)||v(g,y)):d==="data"?r.filter((g,y)=>{let x=s.routes[g.route.id];if(!x||!x.hasLoader)return!1;if(h(g,y)||v(g,y))return!0;if(g.route.shouldRevalidate){let O=g.route.shouldRevalidate({currentUrl:new URL(o.pathname+o.search+o.hash,window.origin),currentParams:c[0]?.params||{},nextUrl:new URL(n,window.origin),nextParams:g.params,defaultShouldRevalidate:!0});if(typeof O=="boolean")return O}return!0}):[]}function Wg(n,r,{includeHydrateFallback:c}={}){return Pg(n.map(s=>{let o=r.routes[s.route.id];if(!o)return[];let d=[o.module];return o.clientActionModule&&(d=d.concat(o.clientActionModule)),o.clientLoaderModule&&(d=d.concat(o.clientLoaderModule)),c&&o.hydrateFallbackModule&&(d=d.concat(o.hydrateFallbackModule)),o.imports&&(d=d.concat(o.imports)),d}).flat(1))}function Pg(n){return[...new Set(n)]}function Ig(n){let r={},c=Object.keys(n).sort();for(let s of c)r[s]=n[s];return r}function e1(n,r){let c=new Set;return new Set(r),n.reduce((s,o)=>{let d=JSON.stringify(Ig(o));return c.has(d)||(c.add(d),s.push({key:d,link:o})),s},[])}function Dm(){let n=A.useContext(Wa);return Vc(n,"You must render this element inside a <DataRouterContext.Provider> element"),n}function t1(){let n=A.useContext(Qi);return Vc(n,"You must render this element inside a <DataRouterStateContext.Provider> element"),n}var Zc=A.createContext(void 0);Zc.displayName="FrameworkContext";function Cm(){let n=A.useContext(Zc);return Vc(n,"You must render this element inside a <HydratedRouter> element"),n}function l1(n,r){let c=A.useContext(Zc),[s,o]=A.useState(!1),[d,h]=A.useState(!1),{onFocus:v,onBlur:g,onMouseEnter:y,onMouseLeave:x,onTouchStart:O}=r,C=A.useRef(null);A.useEffect(()=>{if(n==="render"&&h(!0),n==="viewport"){let q=Y=>{Y.forEach($=>{h($.isIntersecting)})},B=new IntersectionObserver(q,{threshold:.5});return C.current&&B.observe(C.current),()=>{B.disconnect()}}},[n]),A.useEffect(()=>{if(s){let q=setTimeout(()=>{h(!0)},100);return()=>{clearTimeout(q)}}},[s]);let G=()=>{o(!0)},D=()=>{o(!1),h(!1)};return c?n!=="intent"?[d,C,{}]:[d,C,{onFocus:tu(v,G),onBlur:tu(g,D),onMouseEnter:tu(y,G),onMouseLeave:tu(x,D),onTouchStart:tu(O,G)}]:[!1,C,{}]}function tu(n,r){return c=>{n&&n(c),c.defaultPrevented||r(c)}}function a1({page:n,...r}){let{router:c}=Dm(),s=A.useMemo(()=>Sm(c.routes,n,c.basename),[c.routes,n,c.basename]);return s?A.createElement(u1,{page:n,matches:s,...r}):null}function n1(n){let{manifest:r,routeModules:c}=Cm(),[s,o]=A.useState([]);return A.useEffect(()=>{let d=!1;return Fg(n,r,c).then(h=>{d||o(h)}),()=>{d=!0}},[n,r,c]),s}function u1({page:n,matches:r,...c}){let s=ol(),{manifest:o,routeModules:d}=Cm(),{basename:h}=Dm(),{loaderData:v,matches:g}=t1(),y=A.useMemo(()=>am(n,r,g,o,s,"data"),[n,r,g,o,s]),x=A.useMemo(()=>am(n,r,g,o,s,"assets"),[n,r,g,o,s]),O=A.useMemo(()=>{if(n===s.pathname+s.search+s.hash)return[];let D=new Set,q=!1;if(r.forEach(Y=>{let $=o.routes[Y.route.id];!$||!$.hasLoader||(!y.some(K=>K.route.id===Y.route.id)&&Y.route.id in v&&d[Y.route.id]?.shouldRevalidate||$.hasClientLoader?q=!0:D.add(Y.route.id))}),D.size===0)return[];let B=kg(n,h,"data");return q&&D.size>0&&B.searchParams.set("_routes",r.filter(Y=>D.has(Y.route.id)).map(Y=>Y.route.id).join(",")),[B.pathname+B.search]},[h,v,s,o,y,r,n,d]),C=A.useMemo(()=>Wg(x,o),[x,o]),G=n1(x);return A.createElement(A.Fragment,null,O.map(D=>A.createElement("link",{key:D,rel:"prefetch",as:"fetch",href:D,...c})),C.map(D=>A.createElement("link",{key:D,rel:"modulepreload",href:D,...c})),G.map(({key:D,link:q})=>A.createElement("link",{key:D,...q})))}function i1(...n){return r=>{n.forEach(c=>{typeof c=="function"?c(r):c!=null&&(c.current=r)})}}var Mm=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u";try{Mm&&(window.__reactRouterVersion="7.7.0")}catch{}function r1({basename:n,children:r,window:c}){let s=A.useRef();s.current==null&&(s.current=Jp({window:c,v5Compat:!0}));let o=s.current,[d,h]=A.useState({action:o.action,location:o.location}),v=A.useCallback(g=>{A.startTransition(()=>h(g))},[h]);return A.useLayoutEffect(()=>o.listen(v),[o,v]),A.createElement(Bg,{basename:n,children:r,location:d.location,navigationType:d.action,navigator:o})}var zm=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,Ae=A.forwardRef(function({onClick:r,discover:c="render",prefetch:s="none",relative:o,reloadDocument:d,replace:h,state:v,target:g,to:y,preventScrollReset:x,viewTransition:O,...C},G){let{basename:D}=A.useContext(Mt),q=typeof y=="string"&&zm.test(y),B,Y=!1;if(typeof y=="string"&&q&&(B=y,Mm))try{let Ne=new URL(window.location.href),st=y.startsWith("//")?new URL(Ne.protocol+y):new URL(y),bt=sl(st.pathname,D);st.origin===Ne.origin&&bt!=null?y=bt+st.search+st.hash:Y=!0}catch{Ct(!1,`<Link to="${y}"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.`)}let $=Sg(y,{relative:o}),[K,ce,k]=l1(s,C),ve=f1(y,{replace:h,state:v,target:g,preventScrollReset:x,relative:o,viewTransition:O});function be(Ne){r&&r(Ne),Ne.defaultPrevented||ve(Ne)}let je=A.createElement("a",{...C,...k,href:B||$,onClick:Y||d?r:be,ref:i1(G,ce),target:g,"data-discover":!q&&c==="render"?"true":void 0});return K&&!q?A.createElement(A.Fragment,null,je,A.createElement(a1,{page:$})):je});Ae.displayName="Link";var s1=A.forwardRef(function({"aria-current":r="page",caseSensitive:c=!1,className:s="",end:o=!1,style:d,to:h,viewTransition:v,children:g,...y},x){let O=su(h,{relative:y.relative}),C=ol(),G=A.useContext(Qi),{navigator:D,basename:q}=A.useContext(Mt),B=G!=null&&p1(O)&&v===!0,Y=D.encodeLocation?D.encodeLocation(O).pathname:O.pathname,$=C.pathname,K=G&&G.navigation&&G.navigation.location?G.navigation.location.pathname:null;c||($=$.toLowerCase(),K=K?K.toLowerCase():null,Y=Y.toLowerCase()),K&&q&&(K=sl(K,q)||K);const ce=Y!=="/"&&Y.endsWith("/")?Y.length-1:Y.length;let k=$===Y||!o&&$.startsWith(Y)&&$.charAt(ce)==="/",ve=K!=null&&(K===Y||!o&&K.startsWith(Y)&&K.charAt(Y.length)==="/"),be={isActive:k,isPending:ve,isTransitioning:B},je=k?r:void 0,Ne;typeof s=="function"?Ne=s(be):Ne=[s,k?"active":null,ve?"pending":null,B?"transitioning":null].filter(Boolean).join(" ");let st=typeof d=="function"?d(be):d;return A.createElement(Ae,{...y,"aria-current":je,className:Ne,ref:x,style:st,to:h,viewTransition:v},typeof g=="function"?g(be):g)});s1.displayName="NavLink";var c1=A.forwardRef(({discover:n="render",fetcherKey:r,navigate:c,reloadDocument:s,replace:o,state:d,method:h=Di,action:v,onSubmit:g,relative:y,preventScrollReset:x,viewTransition:O,...C},G)=>{let D=m1(),q=y1(v,{relative:y}),B=h.toLowerCase()==="get"?"get":"post",Y=typeof v=="string"&&zm.test(v),$=K=>{if(g&&g(K),K.defaultPrevented)return;K.preventDefault();let ce=K.nativeEvent.submitter,k=ce?.getAttribute("formmethod")||h;D(ce||K.currentTarget,{fetcherKey:r,method:k,navigate:c,replace:o,state:d,relative:y,preventScrollReset:x,viewTransition:O})};return A.createElement("form",{ref:G,method:B,action:q,onSubmit:s?g:$,...C,"data-discover":!Y&&n==="render"?"true":void 0})});c1.displayName="Form";function o1(n){return`${n} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function Um(n){let r=A.useContext(Wa);return Oe(r,o1(n)),r}function f1(n,{target:r,replace:c,state:s,preventScrollReset:o,relative:d,viewTransition:h}={}){let v=Vi(),g=ol(),y=su(n,{relative:d});return A.useCallback(x=>{if(Qg(x,r)){x.preventDefault();let O=c!==void 0?c:uu(g)===uu(y);v(n,{replace:O,state:s,preventScrollReset:o,relative:d,viewTransition:h})}},[g,v,y,c,s,r,n,o,d,h])}var d1=0,h1=()=>`__${String(++d1)}__`;function m1(){let{router:n}=Um("useSubmit"),{basename:r}=A.useContext(Mt),c=Mg();return A.useCallback(async(s,o={})=>{let{action:d,method:h,encType:v,formData:g,body:y}=Kg(s,r);if(o.navigate===!1){let x=o.fetcherKey||h1();await n.fetch(x,c,o.action||d,{preventScrollReset:o.preventScrollReset,formData:g,body:y,formMethod:o.method||h,formEncType:o.encType||v,flushSync:o.flushSync})}else await n.navigate(o.action||d,{preventScrollReset:o.preventScrollReset,formData:g,body:y,formMethod:o.method||h,formEncType:o.encType||v,replace:o.replace,state:o.state,fromRouteId:c,flushSync:o.flushSync,viewTransition:o.viewTransition})},[n,r,c])}function y1(n,{relative:r}={}){let{basename:c}=A.useContext(Mt),s=A.useContext(zt);Oe(s,"useFormAction must be used inside a RouteContext");let[o]=s.matches.slice(-1),d={...su(n||".",{relative:r})},h=ol();if(n==null){d.search=h.search;let v=new URLSearchParams(d.search),g=v.getAll("index");if(g.some(x=>x==="")){v.delete("index"),g.filter(O=>O).forEach(O=>v.append("index",O));let x=v.toString();d.search=x?`?${x}`:""}}return(!n||n===".")&&o.route.index&&(d.search=d.search?d.search.replace(/^\?/,"?index&"):"?index"),c!=="/"&&(d.pathname=d.pathname==="/"?c:rl([c,d.pathname])),uu(d)}function p1(n,r={}){let c=A.useContext(Rm);Oe(c!=null,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:s}=Um("useViewTransitionState"),o=su(n,{relative:r.relative});if(!c.isTransitioning)return!1;let d=sl(c.currentLocation.pathname,s)||c.currentLocation.pathname,h=sl(c.nextLocation.pathname,s)||c.nextLocation.pathname;return Bi(o.pathname,h)!=null||Bi(o.pathname,d)!=null}let g1={data:""},v1=n=>typeof window=="object"?((n?n.querySelector("#_goober"):window._goober)||Object.assign((n||document.head).appendChild(document.createElement("style")),{innerHTML:" ",id:"_goober"})).firstChild:n||g1,b1=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,x1=/\/\*[^]*?\*\/|  +/g,nm=/\n+/g,Hl=(n,r)=>{let c="",s="",o="";for(let d in n){let h=n[d];d[0]=="@"?d[1]=="i"?c=d+" "+h+";":s+=d[1]=="f"?Hl(h,d):d+"{"+Hl(h,d[1]=="k"?"":r)+"}":typeof h=="object"?s+=Hl(h,r?r.replace(/([^,])+/g,v=>d.replace(/([^,]*:\S+\([^)]*\))|([^,])+/g,g=>/&/.test(g)?g.replace(/&/g,v):v?v+" "+g:g)):d):h!=null&&(d=/^--/.test(d)?d:d.replace(/[A-Z]/g,"-$&").toLowerCase(),o+=Hl.p?Hl.p(d,h):d+":"+h+";")}return c+(r&&o?r+"{"+o+"}":o)+s},nl={},Hm=n=>{if(typeof n=="object"){let r="";for(let c in n)r+=c+Hm(n[c]);return r}return n},S1=(n,r,c,s,o)=>{let d=Hm(n),h=nl[d]||(nl[d]=(g=>{let y=0,x=11;for(;y<g.length;)x=101*x+g.charCodeAt(y++)>>>0;return"go"+x})(d));if(!nl[h]){let g=d!==n?n:(y=>{let x,O,C=[{}];for(;x=b1.exec(y.replace(x1,""));)x[4]?C.shift():x[3]?(O=x[3].replace(nm," ").trim(),C.unshift(C[0][O]=C[0][O]||{})):C[0][x[1]]=x[2].replace(nm," ").trim();return C[0]})(n);nl[h]=Hl(o?{["@keyframes "+h]:g}:g,c?"":"."+h)}let v=c&&nl.g?nl.g:null;return c&&(nl.g=nl[h]),((g,y,x,O)=>{O?y.data=y.data.replace(O,g):y.data.indexOf(g)===-1&&(y.data=x?g+y.data:y.data+g)})(nl[h],r,s,v),h},E1=(n,r,c)=>n.reduce((s,o,d)=>{let h=r[d];if(h&&h.call){let v=h(c),g=v&&v.props&&v.props.className||/^go/.test(v)&&v;h=g?"."+g:v&&typeof v=="object"?v.props?"":Hl(v,""):v===!1?"":v}return s+o+(h??"")},"");function Ki(n){let r=this||{},c=n.call?n(r.p):n;return S1(c.unshift?c.raw?E1(c,[].slice.call(arguments,1),r.p):c.reduce((s,o)=>Object.assign(s,o&&o.call?o(r.p):o),{}):c,v1(r.target),r.g,r.o,r.k)}let Bm,_c,Dc;Ki.bind({g:1});let cl=Ki.bind({k:1});function N1(n,r,c,s){Hl.p=r,Bm=n,_c=c,Dc=s}function Bl(n,r){let c=this||{};return function(){let s=arguments;function o(d,h){let v=Object.assign({},d),g=v.className||o.className;c.p=Object.assign({theme:_c&&_c()},v),c.o=/ *go\d+/.test(g),v.className=Ki.apply(c,s)+(g?" "+g:"");let y=n;return n[0]&&(y=v.as||n,delete v.as),Dc&&y[0]&&Dc(v),Bm(y,v)}return o}}var T1=n=>typeof n=="function",qi=(n,r)=>T1(n)?n(r):n,R1=(()=>{let n=0;return()=>(++n).toString()})(),qm=(()=>{let n;return()=>{if(n===void 0&&typeof window<"u"){let r=matchMedia("(prefers-reduced-motion: reduce)");n=!r||r.matches}return n}})(),A1=20,Lm=(n,r)=>{switch(r.type){case 0:return{...n,toasts:[r.toast,...n.toasts].slice(0,A1)};case 1:return{...n,toasts:n.toasts.map(d=>d.id===r.toast.id?{...d,...r.toast}:d)};case 2:let{toast:c}=r;return Lm(n,{type:n.toasts.find(d=>d.id===c.id)?1:0,toast:c});case 3:let{toastId:s}=r;return{...n,toasts:n.toasts.map(d=>d.id===s||s===void 0?{...d,dismissed:!0,visible:!1}:d)};case 4:return r.toastId===void 0?{...n,toasts:[]}:{...n,toasts:n.toasts.filter(d=>d.id!==r.toastId)};case 5:return{...n,pausedAt:r.time};case 6:let o=r.time-(n.pausedAt||0);return{...n,pausedAt:void 0,toasts:n.toasts.map(d=>({...d,pauseDuration:d.pauseDuration+o}))}}},Mi=[],na={toasts:[],pausedAt:void 0},ca=n=>{na=Lm(na,n),Mi.forEach(r=>{r(na)})},O1={blank:4e3,error:4e3,success:2e3,loading:1/0,custom:4e3},j1=(n={})=>{let[r,c]=A.useState(na),s=A.useRef(na);A.useEffect(()=>(s.current!==na&&c(na),Mi.push(c),()=>{let d=Mi.indexOf(c);d>-1&&Mi.splice(d,1)}),[]);let o=r.toasts.map(d=>{var h,v,g;return{...n,...n[d.type],...d,removeDelay:d.removeDelay||((h=n[d.type])==null?void 0:h.removeDelay)||n?.removeDelay,duration:d.duration||((v=n[d.type])==null?void 0:v.duration)||n?.duration||O1[d.type],style:{...n.style,...(g=n[d.type])==null?void 0:g.style,...d.style}}});return{...r,toasts:o}},w1=(n,r="blank",c)=>({createdAt:Date.now(),visible:!0,dismissed:!1,type:r,ariaProps:{role:"status","aria-live":"polite"},message:n,pauseDuration:0,...c,id:c?.id||R1()}),cu=n=>(r,c)=>{let s=w1(r,n,c);return ca({type:2,toast:s}),s.id},Ie=(n,r)=>cu("blank")(n,r);Ie.error=cu("error");Ie.success=cu("success");Ie.loading=cu("loading");Ie.custom=cu("custom");Ie.dismiss=n=>{ca({type:3,toastId:n})};Ie.remove=n=>ca({type:4,toastId:n});Ie.promise=(n,r,c)=>{let s=Ie.loading(r.loading,{...c,...c?.loading});return typeof n=="function"&&(n=n()),n.then(o=>{let d=r.success?qi(r.success,o):void 0;return d?Ie.success(d,{id:s,...c,...c?.success}):Ie.dismiss(s),o}).catch(o=>{let d=r.error?qi(r.error,o):void 0;d?Ie.error(d,{id:s,...c,...c?.error}):Ie.dismiss(s)}),n};var _1=(n,r)=>{ca({type:1,toast:{id:n,height:r}})},D1=()=>{ca({type:5,time:Date.now()})},nu=new Map,C1=1e3,M1=(n,r=C1)=>{if(nu.has(n))return;let c=setTimeout(()=>{nu.delete(n),ca({type:4,toastId:n})},r);nu.set(n,c)},z1=n=>{let{toasts:r,pausedAt:c}=j1(n);A.useEffect(()=>{if(c)return;let d=Date.now(),h=r.map(v=>{if(v.duration===1/0)return;let g=(v.duration||0)+v.pauseDuration-(d-v.createdAt);if(g<0){v.visible&&Ie.dismiss(v.id);return}return setTimeout(()=>Ie.dismiss(v.id),g)});return()=>{h.forEach(v=>v&&clearTimeout(v))}},[r,c]);let s=A.useCallback(()=>{c&&ca({type:6,time:Date.now()})},[c]),o=A.useCallback((d,h)=>{let{reverseOrder:v=!1,gutter:g=8,defaultPosition:y}=h||{},x=r.filter(G=>(G.position||y)===(d.position||y)&&G.height),O=x.findIndex(G=>G.id===d.id),C=x.filter((G,D)=>D<O&&G.visible).length;return x.filter(G=>G.visible).slice(...v?[C+1]:[0,C]).reduce((G,D)=>G+(D.height||0)+g,0)},[r]);return A.useEffect(()=>{r.forEach(d=>{if(d.dismissed)M1(d.id,d.removeDelay);else{let h=nu.get(d.id);h&&(clearTimeout(h),nu.delete(d.id))}})},[r]),{toasts:r,handlers:{updateHeight:_1,startPause:D1,endPause:s,calculateOffset:o}}},U1=cl`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
 transform: scale(1) rotate(45deg);
  opacity: 1;
}`,H1=cl`
from {
  transform: scale(0);
  opacity: 0;
}
to {
  transform: scale(1);
  opacity: 1;
}`,B1=cl`
from {
  transform: scale(0) rotate(90deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(90deg);
	opacity: 1;
}`,q1=Bl("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${n=>n.primary||"#ff4b4b"};
  position: relative;
  transform: rotate(45deg);

  animation: ${U1} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;

  &:after,
  &:before {
    content: '';
    animation: ${H1} 0.15s ease-out forwards;
    animation-delay: 150ms;
    position: absolute;
    border-radius: 3px;
    opacity: 0;
    background: ${n=>n.secondary||"#fff"};
    bottom: 9px;
    left: 4px;
    height: 2px;
    width: 12px;
  }

  &:before {
    animation: ${B1} 0.15s ease-out forwards;
    animation-delay: 180ms;
    transform: rotate(90deg);
  }
`,L1=cl`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`,Y1=Bl("div")`
  width: 12px;
  height: 12px;
  box-sizing: border-box;
  border: 2px solid;
  border-radius: 100%;
  border-color: ${n=>n.secondary||"#e0e0e0"};
  border-right-color: ${n=>n.primary||"#616161"};
  animation: ${L1} 1s linear infinite;
`,G1=cl`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(45deg);
	opacity: 1;
}`,X1=cl`
0% {
	height: 0;
	width: 0;
	opacity: 0;
}
40% {
  height: 0;
	width: 6px;
	opacity: 1;
}
100% {
  opacity: 1;
  height: 10px;
}`,Q1=Bl("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${n=>n.primary||"#61d345"};
  position: relative;
  transform: rotate(45deg);

  animation: ${G1} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;
  &:after {
    content: '';
    box-sizing: border-box;
    animation: ${X1} 0.2s ease-out forwards;
    opacity: 0;
    animation-delay: 200ms;
    position: absolute;
    border-right: 2px solid;
    border-bottom: 2px solid;
    border-color: ${n=>n.secondary||"#fff"};
    bottom: 6px;
    left: 6px;
    height: 10px;
    width: 6px;
  }
`,V1=Bl("div")`
  position: absolute;
`,Z1=Bl("div")`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 20px;
  min-height: 20px;
`,K1=cl`
from {
  transform: scale(0.6);
  opacity: 0.4;
}
to {
  transform: scale(1);
  opacity: 1;
}`,k1=Bl("div")`
  position: relative;
  transform: scale(0.6);
  opacity: 0.4;
  min-width: 20px;
  animation: ${K1} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
`,J1=({toast:n})=>{let{icon:r,type:c,iconTheme:s}=n;return r!==void 0?typeof r=="string"?A.createElement(k1,null,r):r:c==="blank"?null:A.createElement(Z1,null,A.createElement(Y1,{...s}),c!=="loading"&&A.createElement(V1,null,c==="error"?A.createElement(q1,{...s}):A.createElement(Q1,{...s})))},$1=n=>`
0% {transform: translate3d(0,${n*-200}%,0) scale(.6); opacity:.5;}
100% {transform: translate3d(0,0,0) scale(1); opacity:1;}
`,F1=n=>`
0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}
100% {transform: translate3d(0,${n*-150}%,-1px) scale(.6); opacity:0;}
`,W1="0%{opacity:0;} 100%{opacity:1;}",P1="0%{opacity:1;} 100%{opacity:0;}",I1=Bl("div")`
  display: flex;
  align-items: center;
  background: #fff;
  color: #363636;
  line-height: 1.3;
  will-change: transform;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);
  max-width: 350px;
  pointer-events: auto;
  padding: 8px 10px;
  border-radius: 8px;
`,ev=Bl("div")`
  display: flex;
  justify-content: center;
  margin: 4px 10px;
  color: inherit;
  flex: 1 1 auto;
  white-space: pre-line;
`,tv=(n,r)=>{let c=n.includes("top")?1:-1,[s,o]=qm()?[W1,P1]:[$1(c),F1(c)];return{animation:r?`${cl(s)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards`:`${cl(o)} 0.4s forwards cubic-bezier(.06,.71,.55,1)`}},lv=A.memo(({toast:n,position:r,style:c,children:s})=>{let o=n.height?tv(n.position||r||"top-center",n.visible):{opacity:0},d=A.createElement(J1,{toast:n}),h=A.createElement(ev,{...n.ariaProps},qi(n.message,n));return A.createElement(I1,{className:n.className,style:{...o,...c,...n.style}},typeof s=="function"?s({icon:d,message:h}):A.createElement(A.Fragment,null,d,h))});N1(A.createElement);var av=({id:n,className:r,style:c,onHeightUpdate:s,children:o})=>{let d=A.useCallback(h=>{if(h){let v=()=>{let g=h.getBoundingClientRect().height;s(n,g)};v(),new MutationObserver(v).observe(h,{subtree:!0,childList:!0,characterData:!0})}},[n,s]);return A.createElement("div",{ref:d,className:r,style:c},o)},nv=(n,r)=>{let c=n.includes("top"),s=c?{top:0}:{bottom:0},o=n.includes("center")?{justifyContent:"center"}:n.includes("right")?{justifyContent:"flex-end"}:{};return{left:0,right:0,display:"flex",position:"absolute",transition:qm()?void 0:"all 230ms cubic-bezier(.21,1.02,.73,1)",transform:`translateY(${r*(c?1:-1)}px)`,...s,...o}},uv=Ki`
  z-index: 9999;
  > * {
    pointer-events: auto;
  }
`,wi=16,iv=({reverseOrder:n,position:r="top-center",toastOptions:c,gutter:s,children:o,containerStyle:d,containerClassName:h})=>{let{toasts:v,handlers:g}=z1(c);return A.createElement("div",{id:"_rht_toaster",style:{position:"fixed",zIndex:9999,top:wi,left:wi,right:wi,bottom:wi,pointerEvents:"none",...d},className:h,onMouseEnter:g.startPause,onMouseLeave:g.endPause},v.map(y=>{let x=y.position||r,O=g.calculateOffset(y,{reverseOrder:n,gutter:s,defaultPosition:r}),C=nv(x,O);return A.createElement(av,{id:y.id,key:y.id,onHeightUpdate:g.updateHeight,className:y.visible?uv:"",style:C},y.type==="custom"?qi(y.message,y):o?o(y):A.createElement(lv,{toast:y,position:x}))}))},il=Ie;function Ym(n,r){return function(){return n.apply(r,arguments)}}const{toString:rv}=Object.prototype,{getPrototypeOf:Kc}=Object,{iterator:ki,toStringTag:Gm}=Symbol,Ji=(n=>r=>{const c=rv.call(r);return n[c]||(n[c]=c.slice(8,-1).toLowerCase())})(Object.create(null)),Ut=n=>(n=n.toLowerCase(),r=>Ji(r)===n),$i=n=>r=>typeof r===n,{isArray:Ia}=Array,iu=$i("undefined");function sv(n){return n!==null&&!iu(n)&&n.constructor!==null&&!iu(n.constructor)&&it(n.constructor.isBuffer)&&n.constructor.isBuffer(n)}const Xm=Ut("ArrayBuffer");function cv(n){let r;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?r=ArrayBuffer.isView(n):r=n&&n.buffer&&Xm(n.buffer),r}const ov=$i("string"),it=$i("function"),Qm=$i("number"),Fi=n=>n!==null&&typeof n=="object",fv=n=>n===!0||n===!1,zi=n=>{if(Ji(n)!=="object")return!1;const r=Kc(n);return(r===null||r===Object.prototype||Object.getPrototypeOf(r)===null)&&!(Gm in n)&&!(ki in n)},dv=Ut("Date"),hv=Ut("File"),mv=Ut("Blob"),yv=Ut("FileList"),pv=n=>Fi(n)&&it(n.pipe),gv=n=>{let r;return n&&(typeof FormData=="function"&&n instanceof FormData||it(n.append)&&((r=Ji(n))==="formdata"||r==="object"&&it(n.toString)&&n.toString()==="[object FormData]"))},vv=Ut("URLSearchParams"),[bv,xv,Sv,Ev]=["ReadableStream","Request","Response","Headers"].map(Ut),Nv=n=>n.trim?n.trim():n.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function ou(n,r,{allOwnKeys:c=!1}={}){if(n===null||typeof n>"u")return;let s,o;if(typeof n!="object"&&(n=[n]),Ia(n))for(s=0,o=n.length;s<o;s++)r.call(null,n[s],s,n);else{const d=c?Object.getOwnPropertyNames(n):Object.keys(n),h=d.length;let v;for(s=0;s<h;s++)v=d[s],r.call(null,n[v],v,n)}}function Vm(n,r){r=r.toLowerCase();const c=Object.keys(n);let s=c.length,o;for(;s-- >0;)if(o=c[s],r===o.toLowerCase())return o;return null}const ua=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,Zm=n=>!iu(n)&&n!==ua;function Cc(){const{caseless:n}=Zm(this)&&this||{},r={},c=(s,o)=>{const d=n&&Vm(r,o)||o;zi(r[d])&&zi(s)?r[d]=Cc(r[d],s):zi(s)?r[d]=Cc({},s):Ia(s)?r[d]=s.slice():r[d]=s};for(let s=0,o=arguments.length;s<o;s++)arguments[s]&&ou(arguments[s],c);return r}const Tv=(n,r,c,{allOwnKeys:s}={})=>(ou(r,(o,d)=>{c&&it(o)?n[d]=Ym(o,c):n[d]=o},{allOwnKeys:s}),n),Rv=n=>(n.charCodeAt(0)===65279&&(n=n.slice(1)),n),Av=(n,r,c,s)=>{n.prototype=Object.create(r.prototype,s),n.prototype.constructor=n,Object.defineProperty(n,"super",{value:r.prototype}),c&&Object.assign(n.prototype,c)},Ov=(n,r,c,s)=>{let o,d,h;const v={};if(r=r||{},n==null)return r;do{for(o=Object.getOwnPropertyNames(n),d=o.length;d-- >0;)h=o[d],(!s||s(h,n,r))&&!v[h]&&(r[h]=n[h],v[h]=!0);n=c!==!1&&Kc(n)}while(n&&(!c||c(n,r))&&n!==Object.prototype);return r},jv=(n,r,c)=>{n=String(n),(c===void 0||c>n.length)&&(c=n.length),c-=r.length;const s=n.indexOf(r,c);return s!==-1&&s===c},wv=n=>{if(!n)return null;if(Ia(n))return n;let r=n.length;if(!Qm(r))return null;const c=new Array(r);for(;r-- >0;)c[r]=n[r];return c},_v=(n=>r=>n&&r instanceof n)(typeof Uint8Array<"u"&&Kc(Uint8Array)),Dv=(n,r)=>{const s=(n&&n[ki]).call(n);let o;for(;(o=s.next())&&!o.done;){const d=o.value;r.call(n,d[0],d[1])}},Cv=(n,r)=>{let c;const s=[];for(;(c=n.exec(r))!==null;)s.push(c);return s},Mv=Ut("HTMLFormElement"),zv=n=>n.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(c,s,o){return s.toUpperCase()+o}),um=(({hasOwnProperty:n})=>(r,c)=>n.call(r,c))(Object.prototype),Uv=Ut("RegExp"),Km=(n,r)=>{const c=Object.getOwnPropertyDescriptors(n),s={};ou(c,(o,d)=>{let h;(h=r(o,d,n))!==!1&&(s[d]=h||o)}),Object.defineProperties(n,s)},Hv=n=>{Km(n,(r,c)=>{if(it(n)&&["arguments","caller","callee"].indexOf(c)!==-1)return!1;const s=n[c];if(it(s)){if(r.enumerable=!1,"writable"in r){r.writable=!1;return}r.set||(r.set=()=>{throw Error("Can not rewrite read-only method '"+c+"'")})}})},Bv=(n,r)=>{const c={},s=o=>{o.forEach(d=>{c[d]=!0})};return Ia(n)?s(n):s(String(n).split(r)),c},qv=()=>{},Lv=(n,r)=>n!=null&&Number.isFinite(n=+n)?n:r;function Yv(n){return!!(n&&it(n.append)&&n[Gm]==="FormData"&&n[ki])}const Gv=n=>{const r=new Array(10),c=(s,o)=>{if(Fi(s)){if(r.indexOf(s)>=0)return;if(!("toJSON"in s)){r[o]=s;const d=Ia(s)?[]:{};return ou(s,(h,v)=>{const g=c(h,o+1);!iu(g)&&(d[v]=g)}),r[o]=void 0,d}}return s};return c(n,0)},Xv=Ut("AsyncFunction"),Qv=n=>n&&(Fi(n)||it(n))&&it(n.then)&&it(n.catch),km=((n,r)=>n?setImmediate:r?((c,s)=>(ua.addEventListener("message",({source:o,data:d})=>{o===ua&&d===c&&s.length&&s.shift()()},!1),o=>{s.push(o),ua.postMessage(c,"*")}))(`axios@${Math.random()}`,[]):c=>setTimeout(c))(typeof setImmediate=="function",it(ua.postMessage)),Vv=typeof queueMicrotask<"u"?queueMicrotask.bind(ua):typeof process<"u"&&process.nextTick||km,Zv=n=>n!=null&&it(n[ki]),_={isArray:Ia,isArrayBuffer:Xm,isBuffer:sv,isFormData:gv,isArrayBufferView:cv,isString:ov,isNumber:Qm,isBoolean:fv,isObject:Fi,isPlainObject:zi,isReadableStream:bv,isRequest:xv,isResponse:Sv,isHeaders:Ev,isUndefined:iu,isDate:dv,isFile:hv,isBlob:mv,isRegExp:Uv,isFunction:it,isStream:pv,isURLSearchParams:vv,isTypedArray:_v,isFileList:yv,forEach:ou,merge:Cc,extend:Tv,trim:Nv,stripBOM:Rv,inherits:Av,toFlatObject:Ov,kindOf:Ji,kindOfTest:Ut,endsWith:jv,toArray:wv,forEachEntry:Dv,matchAll:Cv,isHTMLForm:Mv,hasOwnProperty:um,hasOwnProp:um,reduceDescriptors:Km,freezeMethods:Hv,toObjectSet:Bv,toCamelCase:zv,noop:qv,toFiniteNumber:Lv,findKey:Vm,global:ua,isContextDefined:Zm,isSpecCompliantForm:Yv,toJSONObject:Gv,isAsyncFn:Xv,isThenable:Qv,setImmediate:km,asap:Vv,isIterable:Zv};function le(n,r,c,s,o){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=n,this.name="AxiosError",r&&(this.code=r),c&&(this.config=c),s&&(this.request=s),o&&(this.response=o,this.status=o.status?o.status:null)}_.inherits(le,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:_.toJSONObject(this.config),code:this.code,status:this.status}}});const Jm=le.prototype,$m={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(n=>{$m[n]={value:n}});Object.defineProperties(le,$m);Object.defineProperty(Jm,"isAxiosError",{value:!0});le.from=(n,r,c,s,o,d)=>{const h=Object.create(Jm);return _.toFlatObject(n,h,function(g){return g!==Error.prototype},v=>v!=="isAxiosError"),le.call(h,n.message,r,c,s,o),h.cause=n,h.name=n.name,d&&Object.assign(h,d),h};const Kv=null;function Mc(n){return _.isPlainObject(n)||_.isArray(n)}function Fm(n){return _.endsWith(n,"[]")?n.slice(0,-2):n}function im(n,r,c){return n?n.concat(r).map(function(o,d){return o=Fm(o),!c&&d?"["+o+"]":o}).join(c?".":""):r}function kv(n){return _.isArray(n)&&!n.some(Mc)}const Jv=_.toFlatObject(_,{},null,function(r){return/^is[A-Z]/.test(r)});function Wi(n,r,c){if(!_.isObject(n))throw new TypeError("target must be an object");r=r||new FormData,c=_.toFlatObject(c,{metaTokens:!0,dots:!1,indexes:!1},!1,function(q,B){return!_.isUndefined(B[q])});const s=c.metaTokens,o=c.visitor||x,d=c.dots,h=c.indexes,g=(c.Blob||typeof Blob<"u"&&Blob)&&_.isSpecCompliantForm(r);if(!_.isFunction(o))throw new TypeError("visitor must be a function");function y(D){if(D===null)return"";if(_.isDate(D))return D.toISOString();if(_.isBoolean(D))return D.toString();if(!g&&_.isBlob(D))throw new le("Blob is not supported. Use a Buffer instead.");return _.isArrayBuffer(D)||_.isTypedArray(D)?g&&typeof Blob=="function"?new Blob([D]):Buffer.from(D):D}function x(D,q,B){let Y=D;if(D&&!B&&typeof D=="object"){if(_.endsWith(q,"{}"))q=s?q:q.slice(0,-2),D=JSON.stringify(D);else if(_.isArray(D)&&kv(D)||(_.isFileList(D)||_.endsWith(q,"[]"))&&(Y=_.toArray(D)))return q=Fm(q),Y.forEach(function(K,ce){!(_.isUndefined(K)||K===null)&&r.append(h===!0?im([q],ce,d):h===null?q:q+"[]",y(K))}),!1}return Mc(D)?!0:(r.append(im(B,q,d),y(D)),!1)}const O=[],C=Object.assign(Jv,{defaultVisitor:x,convertValue:y,isVisitable:Mc});function G(D,q){if(!_.isUndefined(D)){if(O.indexOf(D)!==-1)throw Error("Circular reference detected in "+q.join("."));O.push(D),_.forEach(D,function(Y,$){(!(_.isUndefined(Y)||Y===null)&&o.call(r,Y,_.isString($)?$.trim():$,q,C))===!0&&G(Y,q?q.concat($):[$])}),O.pop()}}if(!_.isObject(n))throw new TypeError("data must be an object");return G(n),r}function rm(n){const r={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(n).replace(/[!'()~]|%20|%00/g,function(s){return r[s]})}function kc(n,r){this._pairs=[],n&&Wi(n,this,r)}const Wm=kc.prototype;Wm.append=function(r,c){this._pairs.push([r,c])};Wm.toString=function(r){const c=r?function(s){return r.call(this,s,rm)}:rm;return this._pairs.map(function(o){return c(o[0])+"="+c(o[1])},"").join("&")};function $v(n){return encodeURIComponent(n).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Pm(n,r,c){if(!r)return n;const s=c&&c.encode||$v;_.isFunction(c)&&(c={serialize:c});const o=c&&c.serialize;let d;if(o?d=o(r,c):d=_.isURLSearchParams(r)?r.toString():new kc(r,c).toString(s),d){const h=n.indexOf("#");h!==-1&&(n=n.slice(0,h)),n+=(n.indexOf("?")===-1?"?":"&")+d}return n}class sm{constructor(){this.handlers=[]}use(r,c,s){return this.handlers.push({fulfilled:r,rejected:c,synchronous:s?s.synchronous:!1,runWhen:s?s.runWhen:null}),this.handlers.length-1}eject(r){this.handlers[r]&&(this.handlers[r]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(r){_.forEach(this.handlers,function(s){s!==null&&r(s)})}}const Im={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},Fv=typeof URLSearchParams<"u"?URLSearchParams:kc,Wv=typeof FormData<"u"?FormData:null,Pv=typeof Blob<"u"?Blob:null,Iv={isBrowser:!0,classes:{URLSearchParams:Fv,FormData:Wv,Blob:Pv},protocols:["http","https","file","blob","url","data"]},Jc=typeof window<"u"&&typeof document<"u",zc=typeof navigator=="object"&&navigator||void 0,eb=Jc&&(!zc||["ReactNative","NativeScript","NS"].indexOf(zc.product)<0),tb=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",lb=Jc&&window.location.href||"http://localhost",ab=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:Jc,hasStandardBrowserEnv:eb,hasStandardBrowserWebWorkerEnv:tb,navigator:zc,origin:lb},Symbol.toStringTag,{value:"Module"})),We={...ab,...Iv};function nb(n,r){return Wi(n,new We.classes.URLSearchParams,Object.assign({visitor:function(c,s,o,d){return We.isNode&&_.isBuffer(c)?(this.append(s,c.toString("base64")),!1):d.defaultVisitor.apply(this,arguments)}},r))}function ub(n){return _.matchAll(/\w+|\[(\w*)]/g,n).map(r=>r[0]==="[]"?"":r[1]||r[0])}function ib(n){const r={},c=Object.keys(n);let s;const o=c.length;let d;for(s=0;s<o;s++)d=c[s],r[d]=n[d];return r}function e0(n){function r(c,s,o,d){let h=c[d++];if(h==="__proto__")return!0;const v=Number.isFinite(+h),g=d>=c.length;return h=!h&&_.isArray(o)?o.length:h,g?(_.hasOwnProp(o,h)?o[h]=[o[h],s]:o[h]=s,!v):((!o[h]||!_.isObject(o[h]))&&(o[h]=[]),r(c,s,o[h],d)&&_.isArray(o[h])&&(o[h]=ib(o[h])),!v)}if(_.isFormData(n)&&_.isFunction(n.entries)){const c={};return _.forEachEntry(n,(s,o)=>{r(ub(s),o,c,0)}),c}return null}function rb(n,r,c){if(_.isString(n))try{return(r||JSON.parse)(n),_.trim(n)}catch(s){if(s.name!=="SyntaxError")throw s}return(c||JSON.stringify)(n)}const fu={transitional:Im,adapter:["xhr","http","fetch"],transformRequest:[function(r,c){const s=c.getContentType()||"",o=s.indexOf("application/json")>-1,d=_.isObject(r);if(d&&_.isHTMLForm(r)&&(r=new FormData(r)),_.isFormData(r))return o?JSON.stringify(e0(r)):r;if(_.isArrayBuffer(r)||_.isBuffer(r)||_.isStream(r)||_.isFile(r)||_.isBlob(r)||_.isReadableStream(r))return r;if(_.isArrayBufferView(r))return r.buffer;if(_.isURLSearchParams(r))return c.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),r.toString();let v;if(d){if(s.indexOf("application/x-www-form-urlencoded")>-1)return nb(r,this.formSerializer).toString();if((v=_.isFileList(r))||s.indexOf("multipart/form-data")>-1){const g=this.env&&this.env.FormData;return Wi(v?{"files[]":r}:r,g&&new g,this.formSerializer)}}return d||o?(c.setContentType("application/json",!1),rb(r)):r}],transformResponse:[function(r){const c=this.transitional||fu.transitional,s=c&&c.forcedJSONParsing,o=this.responseType==="json";if(_.isResponse(r)||_.isReadableStream(r))return r;if(r&&_.isString(r)&&(s&&!this.responseType||o)){const h=!(c&&c.silentJSONParsing)&&o;try{return JSON.parse(r)}catch(v){if(h)throw v.name==="SyntaxError"?le.from(v,le.ERR_BAD_RESPONSE,this,null,this.response):v}}return r}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:We.classes.FormData,Blob:We.classes.Blob},validateStatus:function(r){return r>=200&&r<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};_.forEach(["delete","get","head","post","put","patch"],n=>{fu.headers[n]={}});const sb=_.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),cb=n=>{const r={};let c,s,o;return n&&n.split(`
`).forEach(function(h){o=h.indexOf(":"),c=h.substring(0,o).trim().toLowerCase(),s=h.substring(o+1).trim(),!(!c||r[c]&&sb[c])&&(c==="set-cookie"?r[c]?r[c].push(s):r[c]=[s]:r[c]=r[c]?r[c]+", "+s:s)}),r},cm=Symbol("internals");function lu(n){return n&&String(n).trim().toLowerCase()}function Ui(n){return n===!1||n==null?n:_.isArray(n)?n.map(Ui):String(n)}function ob(n){const r=Object.create(null),c=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let s;for(;s=c.exec(n);)r[s[1]]=s[2];return r}const fb=n=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(n.trim());function Nc(n,r,c,s,o){if(_.isFunction(s))return s.call(this,r,c);if(o&&(r=c),!!_.isString(r)){if(_.isString(s))return r.indexOf(s)!==-1;if(_.isRegExp(s))return s.test(r)}}function db(n){return n.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(r,c,s)=>c.toUpperCase()+s)}function hb(n,r){const c=_.toCamelCase(" "+r);["get","set","has"].forEach(s=>{Object.defineProperty(n,s+c,{value:function(o,d,h){return this[s].call(this,r,o,d,h)},configurable:!0})})}let rt=class{constructor(r){r&&this.set(r)}set(r,c,s){const o=this;function d(v,g,y){const x=lu(g);if(!x)throw new Error("header name must be a non-empty string");const O=_.findKey(o,x);(!O||o[O]===void 0||y===!0||y===void 0&&o[O]!==!1)&&(o[O||g]=Ui(v))}const h=(v,g)=>_.forEach(v,(y,x)=>d(y,x,g));if(_.isPlainObject(r)||r instanceof this.constructor)h(r,c);else if(_.isString(r)&&(r=r.trim())&&!fb(r))h(cb(r),c);else if(_.isObject(r)&&_.isIterable(r)){let v={},g,y;for(const x of r){if(!_.isArray(x))throw TypeError("Object iterator must return a key-value pair");v[y=x[0]]=(g=v[y])?_.isArray(g)?[...g,x[1]]:[g,x[1]]:x[1]}h(v,c)}else r!=null&&d(c,r,s);return this}get(r,c){if(r=lu(r),r){const s=_.findKey(this,r);if(s){const o=this[s];if(!c)return o;if(c===!0)return ob(o);if(_.isFunction(c))return c.call(this,o,s);if(_.isRegExp(c))return c.exec(o);throw new TypeError("parser must be boolean|regexp|function")}}}has(r,c){if(r=lu(r),r){const s=_.findKey(this,r);return!!(s&&this[s]!==void 0&&(!c||Nc(this,this[s],s,c)))}return!1}delete(r,c){const s=this;let o=!1;function d(h){if(h=lu(h),h){const v=_.findKey(s,h);v&&(!c||Nc(s,s[v],v,c))&&(delete s[v],o=!0)}}return _.isArray(r)?r.forEach(d):d(r),o}clear(r){const c=Object.keys(this);let s=c.length,o=!1;for(;s--;){const d=c[s];(!r||Nc(this,this[d],d,r,!0))&&(delete this[d],o=!0)}return o}normalize(r){const c=this,s={};return _.forEach(this,(o,d)=>{const h=_.findKey(s,d);if(h){c[h]=Ui(o),delete c[d];return}const v=r?db(d):String(d).trim();v!==d&&delete c[d],c[v]=Ui(o),s[v]=!0}),this}concat(...r){return this.constructor.concat(this,...r)}toJSON(r){const c=Object.create(null);return _.forEach(this,(s,o)=>{s!=null&&s!==!1&&(c[o]=r&&_.isArray(s)?s.join(", "):s)}),c}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([r,c])=>r+": "+c).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(r){return r instanceof this?r:new this(r)}static concat(r,...c){const s=new this(r);return c.forEach(o=>s.set(o)),s}static accessor(r){const s=(this[cm]=this[cm]={accessors:{}}).accessors,o=this.prototype;function d(h){const v=lu(h);s[v]||(hb(o,h),s[v]=!0)}return _.isArray(r)?r.forEach(d):d(r),this}};rt.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);_.reduceDescriptors(rt.prototype,({value:n},r)=>{let c=r[0].toUpperCase()+r.slice(1);return{get:()=>n,set(s){this[c]=s}}});_.freezeMethods(rt);function Tc(n,r){const c=this||fu,s=r||c,o=rt.from(s.headers);let d=s.data;return _.forEach(n,function(v){d=v.call(c,d,o.normalize(),r?r.status:void 0)}),o.normalize(),d}function t0(n){return!!(n&&n.__CANCEL__)}function en(n,r,c){le.call(this,n??"canceled",le.ERR_CANCELED,r,c),this.name="CanceledError"}_.inherits(en,le,{__CANCEL__:!0});function l0(n,r,c){const s=c.config.validateStatus;!c.status||!s||s(c.status)?n(c):r(new le("Request failed with status code "+c.status,[le.ERR_BAD_REQUEST,le.ERR_BAD_RESPONSE][Math.floor(c.status/100)-4],c.config,c.request,c))}function mb(n){const r=/^([-+\w]{1,25})(:?\/\/|:)/.exec(n);return r&&r[1]||""}function yb(n,r){n=n||10;const c=new Array(n),s=new Array(n);let o=0,d=0,h;return r=r!==void 0?r:1e3,function(g){const y=Date.now(),x=s[d];h||(h=y),c[o]=g,s[o]=y;let O=d,C=0;for(;O!==o;)C+=c[O++],O=O%n;if(o=(o+1)%n,o===d&&(d=(d+1)%n),y-h<r)return;const G=x&&y-x;return G?Math.round(C*1e3/G):void 0}}function pb(n,r){let c=0,s=1e3/r,o,d;const h=(y,x=Date.now())=>{c=x,o=null,d&&(clearTimeout(d),d=null),n.apply(null,y)};return[(...y)=>{const x=Date.now(),O=x-c;O>=s?h(y,x):(o=y,d||(d=setTimeout(()=>{d=null,h(o)},s-O)))},()=>o&&h(o)]}const Li=(n,r,c=3)=>{let s=0;const o=yb(50,250);return pb(d=>{const h=d.loaded,v=d.lengthComputable?d.total:void 0,g=h-s,y=o(g),x=h<=v;s=h;const O={loaded:h,total:v,progress:v?h/v:void 0,bytes:g,rate:y||void 0,estimated:y&&v&&x?(v-h)/y:void 0,event:d,lengthComputable:v!=null,[r?"download":"upload"]:!0};n(O)},c)},om=(n,r)=>{const c=n!=null;return[s=>r[0]({lengthComputable:c,total:n,loaded:s}),r[1]]},fm=n=>(...r)=>_.asap(()=>n(...r)),gb=We.hasStandardBrowserEnv?((n,r)=>c=>(c=new URL(c,We.origin),n.protocol===c.protocol&&n.host===c.host&&(r||n.port===c.port)))(new URL(We.origin),We.navigator&&/(msie|trident)/i.test(We.navigator.userAgent)):()=>!0,vb=We.hasStandardBrowserEnv?{write(n,r,c,s,o,d){const h=[n+"="+encodeURIComponent(r)];_.isNumber(c)&&h.push("expires="+new Date(c).toGMTString()),_.isString(s)&&h.push("path="+s),_.isString(o)&&h.push("domain="+o),d===!0&&h.push("secure"),document.cookie=h.join("; ")},read(n){const r=document.cookie.match(new RegExp("(^|;\\s*)("+n+")=([^;]*)"));return r?decodeURIComponent(r[3]):null},remove(n){this.write(n,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function bb(n){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(n)}function xb(n,r){return r?n.replace(/\/?\/$/,"")+"/"+r.replace(/^\/+/,""):n}function a0(n,r,c){let s=!bb(r);return n&&(s||c==!1)?xb(n,r):r}const dm=n=>n instanceof rt?{...n}:n;function sa(n,r){r=r||{};const c={};function s(y,x,O,C){return _.isPlainObject(y)&&_.isPlainObject(x)?_.merge.call({caseless:C},y,x):_.isPlainObject(x)?_.merge({},x):_.isArray(x)?x.slice():x}function o(y,x,O,C){if(_.isUndefined(x)){if(!_.isUndefined(y))return s(void 0,y,O,C)}else return s(y,x,O,C)}function d(y,x){if(!_.isUndefined(x))return s(void 0,x)}function h(y,x){if(_.isUndefined(x)){if(!_.isUndefined(y))return s(void 0,y)}else return s(void 0,x)}function v(y,x,O){if(O in r)return s(y,x);if(O in n)return s(void 0,y)}const g={url:d,method:d,data:d,baseURL:h,transformRequest:h,transformResponse:h,paramsSerializer:h,timeout:h,timeoutMessage:h,withCredentials:h,withXSRFToken:h,adapter:h,responseType:h,xsrfCookieName:h,xsrfHeaderName:h,onUploadProgress:h,onDownloadProgress:h,decompress:h,maxContentLength:h,maxBodyLength:h,beforeRedirect:h,transport:h,httpAgent:h,httpsAgent:h,cancelToken:h,socketPath:h,responseEncoding:h,validateStatus:v,headers:(y,x,O)=>o(dm(y),dm(x),O,!0)};return _.forEach(Object.keys(Object.assign({},n,r)),function(x){const O=g[x]||o,C=O(n[x],r[x],x);_.isUndefined(C)&&O!==v||(c[x]=C)}),c}const n0=n=>{const r=sa({},n);let{data:c,withXSRFToken:s,xsrfHeaderName:o,xsrfCookieName:d,headers:h,auth:v}=r;r.headers=h=rt.from(h),r.url=Pm(a0(r.baseURL,r.url,r.allowAbsoluteUrls),n.params,n.paramsSerializer),v&&h.set("Authorization","Basic "+btoa((v.username||"")+":"+(v.password?unescape(encodeURIComponent(v.password)):"")));let g;if(_.isFormData(c)){if(We.hasStandardBrowserEnv||We.hasStandardBrowserWebWorkerEnv)h.setContentType(void 0);else if((g=h.getContentType())!==!1){const[y,...x]=g?g.split(";").map(O=>O.trim()).filter(Boolean):[];h.setContentType([y||"multipart/form-data",...x].join("; "))}}if(We.hasStandardBrowserEnv&&(s&&_.isFunction(s)&&(s=s(r)),s||s!==!1&&gb(r.url))){const y=o&&d&&vb.read(d);y&&h.set(o,y)}return r},Sb=typeof XMLHttpRequest<"u",Eb=Sb&&function(n){return new Promise(function(c,s){const o=n0(n);let d=o.data;const h=rt.from(o.headers).normalize();let{responseType:v,onUploadProgress:g,onDownloadProgress:y}=o,x,O,C,G,D;function q(){G&&G(),D&&D(),o.cancelToken&&o.cancelToken.unsubscribe(x),o.signal&&o.signal.removeEventListener("abort",x)}let B=new XMLHttpRequest;B.open(o.method.toUpperCase(),o.url,!0),B.timeout=o.timeout;function Y(){if(!B)return;const K=rt.from("getAllResponseHeaders"in B&&B.getAllResponseHeaders()),k={data:!v||v==="text"||v==="json"?B.responseText:B.response,status:B.status,statusText:B.statusText,headers:K,config:n,request:B};l0(function(be){c(be),q()},function(be){s(be),q()},k),B=null}"onloadend"in B?B.onloadend=Y:B.onreadystatechange=function(){!B||B.readyState!==4||B.status===0&&!(B.responseURL&&B.responseURL.indexOf("file:")===0)||setTimeout(Y)},B.onabort=function(){B&&(s(new le("Request aborted",le.ECONNABORTED,n,B)),B=null)},B.onerror=function(){s(new le("Network Error",le.ERR_NETWORK,n,B)),B=null},B.ontimeout=function(){let ce=o.timeout?"timeout of "+o.timeout+"ms exceeded":"timeout exceeded";const k=o.transitional||Im;o.timeoutErrorMessage&&(ce=o.timeoutErrorMessage),s(new le(ce,k.clarifyTimeoutError?le.ETIMEDOUT:le.ECONNABORTED,n,B)),B=null},d===void 0&&h.setContentType(null),"setRequestHeader"in B&&_.forEach(h.toJSON(),function(ce,k){B.setRequestHeader(k,ce)}),_.isUndefined(o.withCredentials)||(B.withCredentials=!!o.withCredentials),v&&v!=="json"&&(B.responseType=o.responseType),y&&([C,D]=Li(y,!0),B.addEventListener("progress",C)),g&&B.upload&&([O,G]=Li(g),B.upload.addEventListener("progress",O),B.upload.addEventListener("loadend",G)),(o.cancelToken||o.signal)&&(x=K=>{B&&(s(!K||K.type?new en(null,n,B):K),B.abort(),B=null)},o.cancelToken&&o.cancelToken.subscribe(x),o.signal&&(o.signal.aborted?x():o.signal.addEventListener("abort",x)));const $=mb(o.url);if($&&We.protocols.indexOf($)===-1){s(new le("Unsupported protocol "+$+":",le.ERR_BAD_REQUEST,n));return}B.send(d||null)})},Nb=(n,r)=>{const{length:c}=n=n?n.filter(Boolean):[];if(r||c){let s=new AbortController,o;const d=function(y){if(!o){o=!0,v();const x=y instanceof Error?y:this.reason;s.abort(x instanceof le?x:new en(x instanceof Error?x.message:x))}};let h=r&&setTimeout(()=>{h=null,d(new le(`timeout ${r} of ms exceeded`,le.ETIMEDOUT))},r);const v=()=>{n&&(h&&clearTimeout(h),h=null,n.forEach(y=>{y.unsubscribe?y.unsubscribe(d):y.removeEventListener("abort",d)}),n=null)};n.forEach(y=>y.addEventListener("abort",d));const{signal:g}=s;return g.unsubscribe=()=>_.asap(v),g}},Tb=function*(n,r){let c=n.byteLength;if(c<r){yield n;return}let s=0,o;for(;s<c;)o=s+r,yield n.slice(s,o),s=o},Rb=async function*(n,r){for await(const c of Ab(n))yield*Tb(c,r)},Ab=async function*(n){if(n[Symbol.asyncIterator]){yield*n;return}const r=n.getReader();try{for(;;){const{done:c,value:s}=await r.read();if(c)break;yield s}}finally{await r.cancel()}},hm=(n,r,c,s)=>{const o=Rb(n,r);let d=0,h,v=g=>{h||(h=!0,s&&s(g))};return new ReadableStream({async pull(g){try{const{done:y,value:x}=await o.next();if(y){v(),g.close();return}let O=x.byteLength;if(c){let C=d+=O;c(C)}g.enqueue(new Uint8Array(x))}catch(y){throw v(y),y}},cancel(g){return v(g),o.return()}},{highWaterMark:2})},Pi=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",u0=Pi&&typeof ReadableStream=="function",Ob=Pi&&(typeof TextEncoder=="function"?(n=>r=>n.encode(r))(new TextEncoder):async n=>new Uint8Array(await new Response(n).arrayBuffer())),i0=(n,...r)=>{try{return!!n(...r)}catch{return!1}},jb=u0&&i0(()=>{let n=!1;const r=new Request(We.origin,{body:new ReadableStream,method:"POST",get duplex(){return n=!0,"half"}}).headers.has("Content-Type");return n&&!r}),mm=64*1024,Uc=u0&&i0(()=>_.isReadableStream(new Response("").body)),Yi={stream:Uc&&(n=>n.body)};Pi&&(n=>{["text","arrayBuffer","blob","formData","stream"].forEach(r=>{!Yi[r]&&(Yi[r]=_.isFunction(n[r])?c=>c[r]():(c,s)=>{throw new le(`Response type '${r}' is not supported`,le.ERR_NOT_SUPPORT,s)})})})(new Response);const wb=async n=>{if(n==null)return 0;if(_.isBlob(n))return n.size;if(_.isSpecCompliantForm(n))return(await new Request(We.origin,{method:"POST",body:n}).arrayBuffer()).byteLength;if(_.isArrayBufferView(n)||_.isArrayBuffer(n))return n.byteLength;if(_.isURLSearchParams(n)&&(n=n+""),_.isString(n))return(await Ob(n)).byteLength},_b=async(n,r)=>{const c=_.toFiniteNumber(n.getContentLength());return c??wb(r)},Db=Pi&&(async n=>{let{url:r,method:c,data:s,signal:o,cancelToken:d,timeout:h,onDownloadProgress:v,onUploadProgress:g,responseType:y,headers:x,withCredentials:O="same-origin",fetchOptions:C}=n0(n);y=y?(y+"").toLowerCase():"text";let G=Nb([o,d&&d.toAbortSignal()],h),D;const q=G&&G.unsubscribe&&(()=>{G.unsubscribe()});let B;try{if(g&&jb&&c!=="get"&&c!=="head"&&(B=await _b(x,s))!==0){let k=new Request(r,{method:"POST",body:s,duplex:"half"}),ve;if(_.isFormData(s)&&(ve=k.headers.get("content-type"))&&x.setContentType(ve),k.body){const[be,je]=om(B,Li(fm(g)));s=hm(k.body,mm,be,je)}}_.isString(O)||(O=O?"include":"omit");const Y="credentials"in Request.prototype;D=new Request(r,{...C,signal:G,method:c.toUpperCase(),headers:x.normalize().toJSON(),body:s,duplex:"half",credentials:Y?O:void 0});let $=await fetch(D,C);const K=Uc&&(y==="stream"||y==="response");if(Uc&&(v||K&&q)){const k={};["status","statusText","headers"].forEach(Ne=>{k[Ne]=$[Ne]});const ve=_.toFiniteNumber($.headers.get("content-length")),[be,je]=v&&om(ve,Li(fm(v),!0))||[];$=new Response(hm($.body,mm,be,()=>{je&&je(),q&&q()}),k)}y=y||"text";let ce=await Yi[_.findKey(Yi,y)||"text"]($,n);return!K&&q&&q(),await new Promise((k,ve)=>{l0(k,ve,{data:ce,headers:rt.from($.headers),status:$.status,statusText:$.statusText,config:n,request:D})})}catch(Y){throw q&&q(),Y&&Y.name==="TypeError"&&/Load failed|fetch/i.test(Y.message)?Object.assign(new le("Network Error",le.ERR_NETWORK,n,D),{cause:Y.cause||Y}):le.from(Y,Y&&Y.code,n,D)}}),Hc={http:Kv,xhr:Eb,fetch:Db};_.forEach(Hc,(n,r)=>{if(n){try{Object.defineProperty(n,"name",{value:r})}catch{}Object.defineProperty(n,"adapterName",{value:r})}});const ym=n=>`- ${n}`,Cb=n=>_.isFunction(n)||n===null||n===!1,r0={getAdapter:n=>{n=_.isArray(n)?n:[n];const{length:r}=n;let c,s;const o={};for(let d=0;d<r;d++){c=n[d];let h;if(s=c,!Cb(c)&&(s=Hc[(h=String(c)).toLowerCase()],s===void 0))throw new le(`Unknown adapter '${h}'`);if(s)break;o[h||"#"+d]=s}if(!s){const d=Object.entries(o).map(([v,g])=>`adapter ${v} `+(g===!1?"is not supported by the environment":"is not available in the build"));let h=r?d.length>1?`since :
`+d.map(ym).join(`
`):" "+ym(d[0]):"as no adapter specified";throw new le("There is no suitable adapter to dispatch the request "+h,"ERR_NOT_SUPPORT")}return s},adapters:Hc};function Rc(n){if(n.cancelToken&&n.cancelToken.throwIfRequested(),n.signal&&n.signal.aborted)throw new en(null,n)}function pm(n){return Rc(n),n.headers=rt.from(n.headers),n.data=Tc.call(n,n.transformRequest),["post","put","patch"].indexOf(n.method)!==-1&&n.headers.setContentType("application/x-www-form-urlencoded",!1),r0.getAdapter(n.adapter||fu.adapter)(n).then(function(s){return Rc(n),s.data=Tc.call(n,n.transformResponse,s),s.headers=rt.from(s.headers),s},function(s){return t0(s)||(Rc(n),s&&s.response&&(s.response.data=Tc.call(n,n.transformResponse,s.response),s.response.headers=rt.from(s.response.headers))),Promise.reject(s)})}const s0="1.10.0",Ii={};["object","boolean","number","function","string","symbol"].forEach((n,r)=>{Ii[n]=function(s){return typeof s===n||"a"+(r<1?"n ":" ")+n}});const gm={};Ii.transitional=function(r,c,s){function o(d,h){return"[Axios v"+s0+"] Transitional option '"+d+"'"+h+(s?". "+s:"")}return(d,h,v)=>{if(r===!1)throw new le(o(h," has been removed"+(c?" in "+c:"")),le.ERR_DEPRECATED);return c&&!gm[h]&&(gm[h]=!0,console.warn(o(h," has been deprecated since v"+c+" and will be removed in the near future"))),r?r(d,h,v):!0}};Ii.spelling=function(r){return(c,s)=>(console.warn(`${s} is likely a misspelling of ${r}`),!0)};function Mb(n,r,c){if(typeof n!="object")throw new le("options must be an object",le.ERR_BAD_OPTION_VALUE);const s=Object.keys(n);let o=s.length;for(;o-- >0;){const d=s[o],h=r[d];if(h){const v=n[d],g=v===void 0||h(v,d,n);if(g!==!0)throw new le("option "+d+" must be "+g,le.ERR_BAD_OPTION_VALUE);continue}if(c!==!0)throw new le("Unknown option "+d,le.ERR_BAD_OPTION)}}const Hi={assertOptions:Mb,validators:Ii},Xt=Hi.validators;let ia=class{constructor(r){this.defaults=r||{},this.interceptors={request:new sm,response:new sm}}async request(r,c){try{return await this._request(r,c)}catch(s){if(s instanceof Error){let o={};Error.captureStackTrace?Error.captureStackTrace(o):o=new Error;const d=o.stack?o.stack.replace(/^.+\n/,""):"";try{s.stack?d&&!String(s.stack).endsWith(d.replace(/^.+\n.+\n/,""))&&(s.stack+=`
`+d):s.stack=d}catch{}}throw s}}_request(r,c){typeof r=="string"?(c=c||{},c.url=r):c=r||{},c=sa(this.defaults,c);const{transitional:s,paramsSerializer:o,headers:d}=c;s!==void 0&&Hi.assertOptions(s,{silentJSONParsing:Xt.transitional(Xt.boolean),forcedJSONParsing:Xt.transitional(Xt.boolean),clarifyTimeoutError:Xt.transitional(Xt.boolean)},!1),o!=null&&(_.isFunction(o)?c.paramsSerializer={serialize:o}:Hi.assertOptions(o,{encode:Xt.function,serialize:Xt.function},!0)),c.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?c.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:c.allowAbsoluteUrls=!0),Hi.assertOptions(c,{baseUrl:Xt.spelling("baseURL"),withXsrfToken:Xt.spelling("withXSRFToken")},!0),c.method=(c.method||this.defaults.method||"get").toLowerCase();let h=d&&_.merge(d.common,d[c.method]);d&&_.forEach(["delete","get","head","post","put","patch","common"],D=>{delete d[D]}),c.headers=rt.concat(h,d);const v=[];let g=!0;this.interceptors.request.forEach(function(q){typeof q.runWhen=="function"&&q.runWhen(c)===!1||(g=g&&q.synchronous,v.unshift(q.fulfilled,q.rejected))});const y=[];this.interceptors.response.forEach(function(q){y.push(q.fulfilled,q.rejected)});let x,O=0,C;if(!g){const D=[pm.bind(this),void 0];for(D.unshift.apply(D,v),D.push.apply(D,y),C=D.length,x=Promise.resolve(c);O<C;)x=x.then(D[O++],D[O++]);return x}C=v.length;let G=c;for(O=0;O<C;){const D=v[O++],q=v[O++];try{G=D(G)}catch(B){q.call(this,B);break}}try{x=pm.call(this,G)}catch(D){return Promise.reject(D)}for(O=0,C=y.length;O<C;)x=x.then(y[O++],y[O++]);return x}getUri(r){r=sa(this.defaults,r);const c=a0(r.baseURL,r.url,r.allowAbsoluteUrls);return Pm(c,r.params,r.paramsSerializer)}};_.forEach(["delete","get","head","options"],function(r){ia.prototype[r]=function(c,s){return this.request(sa(s||{},{method:r,url:c,data:(s||{}).data}))}});_.forEach(["post","put","patch"],function(r){function c(s){return function(d,h,v){return this.request(sa(v||{},{method:r,headers:s?{"Content-Type":"multipart/form-data"}:{},url:d,data:h}))}}ia.prototype[r]=c(),ia.prototype[r+"Form"]=c(!0)});let zb=class c0{constructor(r){if(typeof r!="function")throw new TypeError("executor must be a function.");let c;this.promise=new Promise(function(d){c=d});const s=this;this.promise.then(o=>{if(!s._listeners)return;let d=s._listeners.length;for(;d-- >0;)s._listeners[d](o);s._listeners=null}),this.promise.then=o=>{let d;const h=new Promise(v=>{s.subscribe(v),d=v}).then(o);return h.cancel=function(){s.unsubscribe(d)},h},r(function(d,h,v){s.reason||(s.reason=new en(d,h,v),c(s.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(r){if(this.reason){r(this.reason);return}this._listeners?this._listeners.push(r):this._listeners=[r]}unsubscribe(r){if(!this._listeners)return;const c=this._listeners.indexOf(r);c!==-1&&this._listeners.splice(c,1)}toAbortSignal(){const r=new AbortController,c=s=>{r.abort(s)};return this.subscribe(c),r.signal.unsubscribe=()=>this.unsubscribe(c),r.signal}static source(){let r;return{token:new c0(function(o){r=o}),cancel:r}}};function Ub(n){return function(c){return n.apply(null,c)}}function Hb(n){return _.isObject(n)&&n.isAxiosError===!0}const Bc={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Bc).forEach(([n,r])=>{Bc[r]=n});function o0(n){const r=new ia(n),c=Ym(ia.prototype.request,r);return _.extend(c,ia.prototype,r,{allOwnKeys:!0}),_.extend(c,r,null,{allOwnKeys:!0}),c.create=function(o){return o0(sa(n,o))},c}const ze=o0(fu);ze.Axios=ia;ze.CanceledError=en;ze.CancelToken=zb;ze.isCancel=t0;ze.VERSION=s0;ze.toFormData=Wi;ze.AxiosError=le;ze.Cancel=ze.CanceledError;ze.all=function(r){return Promise.all(r)};ze.spread=Ub;ze.isAxiosError=Hb;ze.mergeConfig=sa;ze.AxiosHeaders=rt;ze.formToJSON=n=>e0(_.isHTMLForm(n)?new FormData(n):n);ze.getAdapter=r0.getAdapter;ze.HttpStatusCode=Bc;ze.default=ze;const{Axios:Ex,AxiosError:Nx,CanceledError:Tx,isCancel:Rx,CancelToken:Ax,VERSION:Ox,all:jx,Cancel:wx,isAxiosError:_x,spread:Dx,toFormData:Cx,AxiosHeaders:Mx,HttpStatusCode:zx,formToJSON:Ux,getAdapter:Hx,mergeConfig:Bx}=ze,$a=ze.create({baseURL:"http://localhost:5000/api",timeout:1e4,headers:{"Content-Type":"application/json"}});$a.interceptors.request.use(n=>{const r=localStorage.getItem("auth_token");return r&&(n.headers.Authorization=`Bearer ${r}`),n},n=>Promise.reject(n));$a.interceptors.response.use(n=>n,n=>(n.response?.status===401&&(localStorage.removeItem("auth_token"),localStorage.removeItem("auth_user"),window.location.href="/login"),Promise.reject(n)));const Ac={async login(n,r){return(await $a.post("/auth/login",{username:n,password:r})).data},async register(n,r,c){return(await $a.post("/auth/register",{username:n,email:r,password:c})).data},async getProfile(n){const r=n?{headers:{Authorization:`Bearer ${n}`}}:{};return(await $a.get("/auth/profile",r)).data.user},async updateProfile(n){return(await $a.put("/auth/profile",n)).data.user}},f0=A.createContext(void 0),du=()=>{const n=A.useContext(f0);if(!n)throw new Error("useAuth must be used within an AuthProvider");return n},Bb=({children:n})=>{const[r,c]=A.useState(null),[s,o]=A.useState(null),[d,h]=A.useState(!0);A.useEffect(()=>{(async()=>{try{const G=localStorage.getItem("auth_token"),D=localStorage.getItem("auth_user");if(G&&D){o(G),c(JSON.parse(D));try{const q=await Ac.getProfile(G);c(q)}catch{localStorage.removeItem("auth_token"),localStorage.removeItem("auth_user"),o(null),c(null)}}}catch(G){console.error("Error initializing auth:",G)}finally{h(!1)}})()},[]);const O={user:r,token:s,login:async(C,G)=>{try{h(!0);const D=await Ac.login(C,G);o(D.access_token),c(D.user),localStorage.setItem("auth_token",D.access_token),localStorage.setItem("auth_user",JSON.stringify(D.user)),il.success("Login successful!")}catch(D){const q=D.response?.data?.error||"Login failed";throw il.error(q),D}finally{h(!1)}},register:async(C,G,D)=>{try{h(!0);const q=await Ac.register(C,G,D);o(q.access_token),c(q.user),localStorage.setItem("auth_token",q.access_token),localStorage.setItem("auth_user",JSON.stringify(q.user)),il.success("Registration successful!")}catch(q){const B=q.response?.data?.error||"Registration failed";throw il.error(B),q}finally{h(!1)}},logout:()=>{o(null),c(null),localStorage.removeItem("auth_token"),localStorage.removeItem("auth_user"),il.success("Logged out successfully")},isLoading:d,isAuthenticated:!!(s&&r)};return p.jsx(f0.Provider,{value:O,children:n})},au=({children:n})=>{const{isAuthenticated:r,isLoading:c}=du();return c?p.jsx("div",{className:"min-h-screen flex items-center justify-center",children:p.jsx("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"})}):r?p.jsx(p.Fragment,{children:n}):p.jsx(_m,{to:"/login",replace:!0})};/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const qb=n=>n.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),Lb=n=>n.replace(/^([A-Z])|[\s-_]+(\w)/g,(r,c,s)=>s?s.toUpperCase():c.toLowerCase()),vm=n=>{const r=Lb(n);return r.charAt(0).toUpperCase()+r.slice(1)},d0=(...n)=>n.filter((r,c,s)=>!!r&&r.trim()!==""&&s.indexOf(r)===c).join(" ").trim(),Yb=n=>{for(const r in n)if(r.startsWith("aria-")||r==="role"||r==="title")return!0};/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var Gb={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Xb=A.forwardRef(({color:n="currentColor",size:r=24,strokeWidth:c=2,absoluteStrokeWidth:s,className:o="",children:d,iconNode:h,...v},g)=>A.createElement("svg",{ref:g,...Gb,width:r,height:r,stroke:n,strokeWidth:s?Number(c)*24/Number(r):c,className:d0("lucide",o),...!d&&!Yb(v)&&{"aria-hidden":"true"},...v},[...h.map(([y,x])=>A.createElement(y,x)),...Array.isArray(d)?d:[d]]));/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const et=(n,r)=>{const c=A.forwardRef(({className:s,...o},d)=>A.createElement(Xb,{ref:d,iconNode:r,className:d0(`lucide-${qb(vm(n))}`,`lucide-${n}`,s),...o}));return c.displayName=vm(n),c};/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Qb=[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]],ra=et("book-open",Qb);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Vb=[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z",key:"ep3f8r"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4",key:"1p4c4q"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375",key:"tmeiqw"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396",key:"1qfode"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18",key:"159ez6"}]],Gi=et("brain",Vb);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Zb=[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]],_i=et("chart-column",Zb);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Kb=[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]],Oc=et("clock",Kb);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const kb=[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]],h0=et("eye-off",kb);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Jb=[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]],m0=et("eye",Jb);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $b=[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]],Fb=et("log-out",$b);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Wb=[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]],Pb=et("play",Wb);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ib=[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]],Xi=et("plus",Ib);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ex=[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]],tx=et("square-pen",ex);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const lx=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]],ax=et("target",lx);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const nx=[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]],ux=et("trash-2",nx);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ix=[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]],rx=et("trending-up",ix);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const sx=[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]],cx=et("user",sx);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ox=[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]],bm=et("zap",ox),fx=()=>{const{user:n,logout:r,isAuthenticated:c}=du(),s=ol(),o=d=>s.pathname===d;return c?p.jsx("nav",{className:"bg-white shadow-sm border-b border-gray-200",children:p.jsxs("div",{className:"container mx-auto px-4",children:[p.jsxs("div",{className:"flex justify-between items-center h-16",children:[p.jsxs(Ae,{to:"/",className:"flex items-center space-x-2",children:[p.jsx(Gi,{className:"h-8 w-8 text-primary-600"}),p.jsx("span",{className:"text-xl font-bold text-gray-900",children:"FlashGenius"})]}),p.jsxs("div",{className:"hidden md:flex items-center space-x-8",children:[p.jsxs(Ae,{to:"/",className:`flex items-center space-x-1 px-3 py-2 rounded-md text-sm font-medium transition-colors ${o("/")?"text-primary-600 bg-primary-50":"text-gray-600 hover:text-gray-900 hover:bg-gray-50"}`,children:[p.jsx(_i,{className:"h-4 w-4"}),p.jsx("span",{children:"Dashboard"})]}),p.jsxs(Ae,{to:"/decks",className:`flex items-center space-x-1 px-3 py-2 rounded-md text-sm font-medium transition-colors ${o("/decks")?"text-primary-600 bg-primary-50":"text-gray-600 hover:text-gray-900 hover:bg-gray-50"}`,children:[p.jsx(ra,{className:"h-4 w-4"}),p.jsx("span",{children:"My Decks"})]}),p.jsxs(Ae,{to:"/analytics",className:`flex items-center space-x-1 px-3 py-2 rounded-md text-sm font-medium transition-colors ${o("/analytics")?"text-primary-600 bg-primary-50":"text-gray-600 hover:text-gray-900 hover:bg-gray-50"}`,children:[p.jsx(_i,{className:"h-4 w-4"}),p.jsx("span",{children:"Analytics"})]}),p.jsxs(Ae,{to:"/decks/create",className:"btn-primary text-sm flex items-center space-x-1",children:[p.jsx(Xi,{className:"h-4 w-4"}),p.jsx("span",{children:"Create Deck"})]})]}),p.jsxs("div",{className:"flex items-center space-x-4",children:[p.jsxs("div",{className:"flex items-center space-x-2",children:[p.jsx(cx,{className:"h-5 w-5 text-gray-400"}),p.jsx("span",{className:"text-sm text-gray-700",children:n?.username})]}),p.jsxs("button",{onClick:r,className:"flex items-center space-x-1 text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium transition-colors",children:[p.jsx(Fb,{className:"h-4 w-4"}),p.jsx("span",{children:"Logout"})]})]})]}),p.jsx("div",{className:"md:hidden pb-4",children:p.jsxs("div",{className:"flex flex-wrap gap-2",children:[p.jsxs(Ae,{to:"/",className:`flex items-center space-x-1 px-3 py-2 rounded-md text-sm font-medium transition-colors ${o("/")?"text-primary-600 bg-primary-50":"text-gray-600 hover:text-gray-900 hover:bg-gray-50"}`,children:[p.jsx(_i,{className:"h-4 w-4"}),p.jsx("span",{children:"Dashboard"})]}),p.jsxs(Ae,{to:"/decks",className:`flex items-center space-x-1 px-3 py-2 rounded-md text-sm font-medium transition-colors ${o("/decks")?"text-primary-600 bg-primary-50":"text-gray-600 hover:text-gray-900 hover:bg-gray-50"}`,children:[p.jsx(ra,{className:"h-4 w-4"}),p.jsx("span",{children:"Decks"})]}),p.jsxs(Ae,{to:"/analytics",className:`flex items-center space-x-1 px-3 py-2 rounded-md text-sm font-medium transition-colors ${o("/analytics")?"text-primary-600 bg-primary-50":"text-gray-600 hover:text-gray-900 hover:bg-gray-50"}`,children:[p.jsx(_i,{className:"h-4 w-4"}),p.jsx("span",{children:"Analytics"})]})]})})]})}):p.jsx("nav",{className:"bg-white shadow-sm border-b border-gray-200",children:p.jsx("div",{className:"container mx-auto px-4",children:p.jsxs("div",{className:"flex justify-between items-center h-16",children:[p.jsxs(Ae,{to:"/",className:"flex items-center space-x-2",children:[p.jsx(Gi,{className:"h-8 w-8 text-primary-600"}),p.jsx("span",{className:"text-xl font-bold text-gray-900",children:"FlashGenius"})]}),p.jsxs("div",{className:"flex items-center space-x-4",children:[p.jsx(Ae,{to:"/login",className:"text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium",children:"Login"}),p.jsx(Ae,{to:"/register",className:"btn-primary text-sm",children:"Sign Up"})]})]})})})},dx=()=>{const[n,r]=A.useState({username:"",password:""}),[c,s]=A.useState(!1),[o,d]=A.useState(!1),{login:h,isAuthenticated:v}=du(),g=Vi();xm.useEffect(()=>{v&&g("/",{replace:!0})},[v,g]);const y=O=>{r({...n,[O.target.name]:O.target.value})},x=async O=>{if(O.preventDefault(),!n.username||!n.password){il.error("Please fill in all fields");return}d(!0);try{await h(n.username,n.password),g("/",{replace:!0})}catch{}finally{d(!1)}};return p.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:p.jsxs("div",{className:"max-w-md w-full space-y-8",children:[p.jsxs("div",{children:[p.jsx("div",{className:"flex justify-center",children:p.jsx(Gi,{className:"h-12 w-12 text-primary-600"})}),p.jsx("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"Sign in to FlashGenius"}),p.jsxs("p",{className:"mt-2 text-center text-sm text-gray-600",children:["Or"," ",p.jsx(Ae,{to:"/register",className:"font-medium text-primary-600 hover:text-primary-500",children:"create a new account"})]})]}),p.jsxs("form",{className:"mt-8 space-y-6",onSubmit:x,children:[p.jsxs("div",{className:"space-y-4",children:[p.jsxs("div",{children:[p.jsx("label",{htmlFor:"username",className:"block text-sm font-medium text-gray-700",children:"Username or Email"}),p.jsx("input",{id:"username",name:"username",type:"text",autoComplete:"username",required:!0,className:"input mt-1",placeholder:"Enter your username or email",value:n.username,onChange:y,disabled:o})]}),p.jsxs("div",{children:[p.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Password"}),p.jsxs("div",{className:"mt-1 relative",children:[p.jsx("input",{id:"password",name:"password",type:c?"text":"password",autoComplete:"current-password",required:!0,className:"input pr-10",placeholder:"Enter your password",value:n.password,onChange:y,disabled:o}),p.jsx("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>s(!c),children:c?p.jsx(h0,{className:"h-5 w-5 text-gray-400"}):p.jsx(m0,{className:"h-5 w-5 text-gray-400"})})]})]})]}),p.jsx("div",{children:p.jsx("button",{type:"submit",disabled:o,className:"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed",children:o?p.jsxs("div",{className:"flex items-center",children:[p.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Signing in..."]}):"Sign in"})}),p.jsx("div",{className:"text-center",children:p.jsxs("p",{className:"text-sm text-gray-600",children:["Demo credentials: ",p.jsx("br",{}),"Username: ",p.jsx("code",{className:"bg-gray-100 px-1 rounded",children:"demo"})," ",p.jsx("br",{}),"Password: ",p.jsx("code",{className:"bg-gray-100 px-1 rounded",children:"demo123"})]})})]})]})})},hx=()=>{const[n,r]=A.useState({username:"",email:"",password:"",confirmPassword:""}),[c,s]=A.useState(!1),[o,d]=A.useState(!1),{register:h,isAuthenticated:v}=du(),g=Vi();xm.useEffect(()=>{v&&g("/",{replace:!0})},[v,g]);const y=O=>{r({...n,[O.target.name]:O.target.value})},x=async O=>{if(O.preventDefault(),!n.username||!n.email||!n.password||!n.confirmPassword){il.error("Please fill in all fields");return}if(n.password!==n.confirmPassword){il.error("Passwords do not match");return}if(n.password.length<6){il.error("Password must be at least 6 characters long");return}d(!0);try{await h(n.username,n.email,n.password),g("/",{replace:!0})}catch{}finally{d(!1)}};return p.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:p.jsxs("div",{className:"max-w-md w-full space-y-8",children:[p.jsxs("div",{children:[p.jsx("div",{className:"flex justify-center",children:p.jsx(Gi,{className:"h-12 w-12 text-primary-600"})}),p.jsx("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"Create your account"}),p.jsxs("p",{className:"mt-2 text-center text-sm text-gray-600",children:["Or"," ",p.jsx(Ae,{to:"/login",className:"font-medium text-primary-600 hover:text-primary-500",children:"sign in to your existing account"})]})]}),p.jsxs("form",{className:"mt-8 space-y-6",onSubmit:x,children:[p.jsxs("div",{className:"space-y-4",children:[p.jsxs("div",{children:[p.jsx("label",{htmlFor:"username",className:"block text-sm font-medium text-gray-700",children:"Username"}),p.jsx("input",{id:"username",name:"username",type:"text",autoComplete:"username",required:!0,className:"input mt-1",placeholder:"Choose a username",value:n.username,onChange:y,disabled:o})]}),p.jsxs("div",{children:[p.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email address"}),p.jsx("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,className:"input mt-1",placeholder:"Enter your email",value:n.email,onChange:y,disabled:o})]}),p.jsxs("div",{children:[p.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Password"}),p.jsxs("div",{className:"mt-1 relative",children:[p.jsx("input",{id:"password",name:"password",type:c?"text":"password",autoComplete:"new-password",required:!0,className:"input pr-10",placeholder:"Create a password",value:n.password,onChange:y,disabled:o}),p.jsx("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>s(!c),children:c?p.jsx(h0,{className:"h-5 w-5 text-gray-400"}):p.jsx(m0,{className:"h-5 w-5 text-gray-400"})})]})]}),p.jsxs("div",{children:[p.jsx("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700",children:"Confirm Password"}),p.jsx("input",{id:"confirmPassword",name:"confirmPassword",type:"password",autoComplete:"new-password",required:!0,className:"input mt-1",placeholder:"Confirm your password",value:n.confirmPassword,onChange:y,disabled:o})]})]}),p.jsx("div",{children:p.jsx("button",{type:"submit",disabled:o,className:"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed",children:o?p.jsxs("div",{className:"flex items-center",children:[p.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Creating account..."]}):"Create account"})})]})]})})},mx=()=>{const{user:n}=du(),r={totalDecks:5,totalCards:127,cardsDue:23,studyStreak:7,studyTime:45,accuracy:87},c=[{id:1,name:"Spanish Vocabulary",cardCount:45,dueCount:12,color:"#3B82F6"},{id:2,name:"History Facts",cardCount:32,dueCount:8,color:"#10B981"},{id:3,name:"Programming Concepts",cardCount:28,dueCount:3,color:"#8B5CF6"}];return p.jsxs("div",{className:"max-w-7xl mx-auto",children:[p.jsxs("div",{className:"mb-8",children:[p.jsxs("h1",{className:"text-3xl font-bold text-gray-900",children:["Welcome back, ",n?.username,"! 👋"]}),p.jsx("p",{className:"text-gray-600 mt-2",children:"Ready to continue your learning journey?"})]}),p.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8",children:[p.jsx("div",{className:"card",children:p.jsxs("div",{className:"flex items-center",children:[p.jsx("div",{className:"p-2 bg-primary-100 rounded-lg",children:p.jsx(ra,{className:"h-6 w-6 text-primary-600"})}),p.jsxs("div",{className:"ml-4",children:[p.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Total Decks"}),p.jsx("p",{className:"text-2xl font-bold text-gray-900",children:r.totalDecks})]})]})}),p.jsx("div",{className:"card",children:p.jsxs("div",{className:"flex items-center",children:[p.jsx("div",{className:"p-2 bg-success-100 rounded-lg",children:p.jsx(ax,{className:"h-6 w-6 text-success-600"})}),p.jsxs("div",{className:"ml-4",children:[p.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Total Cards"}),p.jsx("p",{className:"text-2xl font-bold text-gray-900",children:r.totalCards})]})]})}),p.jsx("div",{className:"card",children:p.jsxs("div",{className:"flex items-center",children:[p.jsx("div",{className:"p-2 bg-warning-100 rounded-lg",children:p.jsx(Oc,{className:"h-6 w-6 text-warning-600"})}),p.jsxs("div",{className:"ml-4",children:[p.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Cards Due"}),p.jsx("p",{className:"text-2xl font-bold text-gray-900",children:r.cardsDue})]})]})}),p.jsx("div",{className:"card",children:p.jsxs("div",{className:"flex items-center",children:[p.jsx("div",{className:"p-2 bg-error-100 rounded-lg",children:p.jsx(bm,{className:"h-6 w-6 text-error-600"})}),p.jsxs("div",{className:"ml-4",children:[p.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Study Streak"}),p.jsxs("p",{className:"text-2xl font-bold text-gray-900",children:[r.studyStreak," days"]})]})]})}),p.jsx("div",{className:"card",children:p.jsxs("div",{className:"flex items-center",children:[p.jsx("div",{className:"p-2 bg-purple-100 rounded-lg",children:p.jsx(Oc,{className:"h-6 w-6 text-purple-600"})}),p.jsxs("div",{className:"ml-4",children:[p.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Study Time"}),p.jsxs("p",{className:"text-2xl font-bold text-gray-900",children:[r.studyTime,"m"]})]})]})}),p.jsx("div",{className:"card",children:p.jsxs("div",{className:"flex items-center",children:[p.jsx("div",{className:"p-2 bg-indigo-100 rounded-lg",children:p.jsx(rx,{className:"h-6 w-6 text-indigo-600"})}),p.jsxs("div",{className:"ml-4",children:[p.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Accuracy"}),p.jsxs("p",{className:"text-2xl font-bold text-gray-900",children:[r.accuracy,"%"]})]})]})})]}),p.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8",children:[p.jsxs("div",{className:"card",children:[p.jsx("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Study Now"}),p.jsxs("div",{children:[p.jsxs("p",{className:"text-gray-600 mb-4",children:["You have ",r.cardsDue," cards ready for review. Keep up the momentum!"]}),p.jsxs(Ae,{to:"/decks",className:"btn-primary inline-flex items-center space-x-2",children:[p.jsx(Oc,{className:"h-4 w-4"}),p.jsx("span",{children:"Start Studying"})]})]})]}),p.jsxs("div",{className:"card",children:[p.jsx("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Quick Create"}),p.jsx("p",{className:"text-gray-600 mb-4",children:"Start learning something new by creating a deck or generating flashcards from text."}),p.jsxs("div",{className:"space-y-2",children:[p.jsxs(Ae,{to:"/decks/create",className:"btn-primary w-full inline-flex items-center justify-center space-x-2",children:[p.jsx(Xi,{className:"h-4 w-4"}),p.jsx("span",{children:"Create New Deck"})]}),p.jsxs("button",{className:"btn-secondary w-full inline-flex items-center justify-center space-x-2",children:[p.jsx(bm,{className:"h-4 w-4"}),p.jsx("span",{children:"Generate from Text"})]})]})]})]}),p.jsxs("div",{className:"card",children:[p.jsxs("div",{className:"flex items-center justify-between mb-6",children:[p.jsx("h2",{className:"text-xl font-semibold text-gray-900",children:"Recent Decks"}),p.jsx(Ae,{to:"/decks",className:"text-primary-600 hover:text-primary-700 text-sm font-medium",children:"View all decks →"})]}),p.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:c.map(s=>p.jsxs("div",{className:"border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer",children:[p.jsxs("div",{className:"flex items-center mb-3",children:[p.jsx("div",{className:"w-4 h-4 rounded-full mr-3",style:{backgroundColor:s.color}}),p.jsx("h3",{className:"font-medium text-gray-900 truncate",children:s.name})]}),p.jsxs("div",{className:"flex justify-between text-sm text-gray-600 mb-3",children:[p.jsxs("span",{children:[s.cardCount," cards"]}),p.jsxs("span",{className:"text-warning-600 font-medium",children:[s.dueCount," due"]})]}),p.jsx(Ae,{to:`/study/${s.id}`,className:"btn-primary text-sm w-full",children:"Study Now"})]},s.id))}),c.length===0&&p.jsxs("div",{className:"text-center py-8",children:[p.jsx(ra,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),p.jsx("p",{className:"text-gray-600 mb-4",children:"No decks yet. Create your first deck to get started!"}),p.jsx(Ae,{to:"/decks/create",className:"btn-primary",children:"Create Your First Deck"})]})]})]})},yx=()=>{const n=[{id:1,name:"Spanish Vocabulary",description:"Essential Spanish words and phrases for beginners",cardCount:45,dueCount:12,newCount:5,color:"#3B82F6",tags:["Spanish","Language","Beginner"]},{id:2,name:"History Facts",description:"Important historical events and dates",cardCount:32,dueCount:8,newCount:0,color:"#10B981",tags:["History","Facts"]},{id:3,name:"Programming Concepts",description:"Core programming principles and algorithms",cardCount:28,dueCount:3,newCount:12,color:"#8B5CF6",tags:["Programming","Computer Science"]}];return p.jsxs("div",{className:"max-w-7xl mx-auto",children:[p.jsxs("div",{className:"flex items-center justify-between mb-8",children:[p.jsxs("div",{children:[p.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"My Decks"}),p.jsx("p",{className:"text-gray-600 mt-2",children:"Manage your flashcard collections and track your progress"})]}),p.jsxs(Ae,{to:"/decks/create",className:"btn-primary inline-flex items-center space-x-2",children:[p.jsx(Xi,{className:"h-4 w-4"}),p.jsx("span",{children:"Create New Deck"})]})]}),n.length>0?p.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:n.map(r=>p.jsxs("div",{className:"card hover:shadow-lg transition-shadow",children:[p.jsxs("div",{className:"flex items-start justify-between mb-4",children:[p.jsxs("div",{className:"flex items-center",children:[p.jsx("div",{className:"w-4 h-4 rounded-full mr-3 flex-shrink-0",style:{backgroundColor:r.color}}),p.jsxs("div",{className:"min-w-0",children:[p.jsx("h3",{className:"font-semibold text-gray-900 truncate",children:r.name}),p.jsx("p",{className:"text-sm text-gray-600 mt-1 line-clamp-2",children:r.description})]})]}),p.jsxs("div",{className:"flex items-center space-x-1 ml-2",children:[p.jsx("button",{className:"p-1 text-gray-400 hover:text-gray-600 rounded",children:p.jsx(tx,{className:"h-4 w-4"})}),p.jsx("button",{className:"p-1 text-gray-400 hover:text-red-600 rounded",children:p.jsx(ux,{className:"h-4 w-4"})})]})]}),p.jsxs("div",{className:"flex justify-between text-sm text-gray-600 mb-4",children:[p.jsxs("span",{children:[r.cardCount," cards"]}),p.jsxs("div",{className:"flex space-x-3",children:[r.dueCount>0&&p.jsxs("span",{className:"text-warning-600 font-medium",children:[r.dueCount," due"]}),r.newCount>0&&p.jsxs("span",{className:"text-primary-600 font-medium",children:[r.newCount," new"]})]})]}),r.tags.length>0&&p.jsx("div",{className:"flex flex-wrap gap-1 mb-4",children:r.tags.map((c,s)=>p.jsx("span",{className:"px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full",children:c},s))}),p.jsxs("div",{className:"flex space-x-2",children:[p.jsxs(Ae,{to:`/study/${r.id}`,className:"btn-primary flex-1 text-sm inline-flex items-center justify-center space-x-1",children:[p.jsx(Pb,{className:"h-4 w-4"}),p.jsx("span",{children:"Study"})]}),p.jsxs(Ae,{to:`/decks/${r.id}`,className:"btn-secondary flex-1 text-sm inline-flex items-center justify-center space-x-1",children:[p.jsx(ra,{className:"h-4 w-4"}),p.jsx("span",{children:"View"})]})]})]},r.id))}):p.jsxs("div",{className:"text-center py-12",children:[p.jsx(ra,{className:"h-16 w-16 text-gray-400 mx-auto mb-4"}),p.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"No decks yet"}),p.jsx("p",{className:"text-gray-600 mb-6 max-w-md mx-auto",children:"Create your first flashcard deck to start learning. You can add cards manually or generate them from text content."}),p.jsxs("div",{className:"flex flex-col sm:flex-row gap-3 justify-center",children:[p.jsxs(Ae,{to:"/decks/create",className:"btn-primary inline-flex items-center space-x-2",children:[p.jsx(Xi,{className:"h-4 w-4"}),p.jsx("span",{children:"Create Your First Deck"})]}),p.jsxs("button",{className:"btn-secondary inline-flex items-center space-x-2",children:[p.jsx(ra,{className:"h-4 w-4"}),p.jsx("span",{children:"Import from File"})]})]})]}),n.length>0&&p.jsxs("div",{className:"mt-8 grid grid-cols-1 md:grid-cols-3 gap-4",children:[p.jsxs("div",{className:"card text-center",children:[p.jsx("div",{className:"text-2xl font-bold text-primary-600",children:n.length}),p.jsx("div",{className:"text-sm text-gray-600",children:"Total Decks"})]}),p.jsxs("div",{className:"card text-center",children:[p.jsx("div",{className:"text-2xl font-bold text-success-600",children:n.reduce((r,c)=>r+c.cardCount,0)}),p.jsx("div",{className:"text-sm text-gray-600",children:"Total Cards"})]}),p.jsxs("div",{className:"card text-center",children:[p.jsx("div",{className:"text-2xl font-bold text-warning-600",children:n.reduce((r,c)=>r+c.dueCount,0)}),p.jsx("div",{className:"text-sm text-gray-600",children:"Cards Due"})]})]})]})},px=()=>{const{deckId:n}=Ng();return p.jsx("div",{className:"max-w-4xl mx-auto",children:p.jsxs("div",{className:"text-center py-12",children:[p.jsxs("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:["Study Page - Deck ",n]}),p.jsx("p",{className:"text-gray-600",children:"Study functionality will be implemented in the next phase."})]})})},gx=()=>p.jsx("div",{className:"max-w-7xl mx-auto",children:p.jsxs("div",{className:"text-center py-12",children:[p.jsx("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Analytics Dashboard"}),p.jsx("p",{className:"text-gray-600",children:"Analytics and progress tracking will be implemented in the next phase."})]})}),vx=()=>p.jsx("div",{className:"max-w-4xl mx-auto",children:p.jsxs("div",{className:"text-center py-12",children:[p.jsx("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Create New Deck"}),p.jsx("p",{className:"text-gray-600",children:"Deck creation functionality will be implemented in the next phase."})]})});function bx(){return p.jsx(Bb,{children:p.jsx(r1,{children:p.jsxs("div",{className:"min-h-screen bg-gray-50",children:[p.jsx(fx,{}),p.jsx("main",{className:"container mx-auto px-4 py-8",children:p.jsxs(qg,{children:[p.jsx(ul,{path:"/login",element:p.jsx(dx,{})}),p.jsx(ul,{path:"/register",element:p.jsx(hx,{})}),p.jsx(ul,{path:"/",element:p.jsx(au,{children:p.jsx(mx,{})})}),p.jsx(ul,{path:"/decks",element:p.jsx(au,{children:p.jsx(yx,{})})}),p.jsx(ul,{path:"/decks/create",element:p.jsx(au,{children:p.jsx(vx,{})})}),p.jsx(ul,{path:"/study/:deckId",element:p.jsx(au,{children:p.jsx(px,{})})}),p.jsx(ul,{path:"/analytics",element:p.jsx(au,{children:p.jsx(gx,{})})}),p.jsx(ul,{path:"*",element:p.jsx(_m,{to:"/",replace:!0})})]})}),p.jsx(iv,{position:"top-right",toastOptions:{duration:4e3,style:{background:"#363636",color:"#fff"}}})]})})})}kp.createRoot(document.getElementById("root")).render(p.jsx(A.StrictMode,{children:p.jsx(bx,{})}));
