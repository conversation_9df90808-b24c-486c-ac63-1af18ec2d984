import { useState, useEffect } from 'react';
import { deckService } from '../services/deckService';
import type { Deck } from '../types';
import toast from 'react-hot-toast';

export const useDecks = () => {
  const [decks, setDecks] = useState<Deck[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchDecks = async () => {
    try {
      setIsLoading(true);
      setError(null);
      const fetchedDecks = await deckService.getDecks();
      setDecks(fetchedDecks);
    } catch (err: any) {
      const message = err.response?.data?.error || 'Failed to fetch decks';
      setError(message);
      toast.error(message);
    } finally {
      setIsLoading(false);
    }
  };

  const createDeck = async (deckData: any) => {
    try {
      const newDeck = await deckService.createDeck(deckData);
      setDecks(prev => [newDeck, ...prev]);
      toast.success('Deck created successfully!');
      return newDeck;
    } catch (err: any) {
      const message = err.response?.data?.error || 'Failed to create deck';
      toast.error(message);
      throw err;
    }
  };

  const updateDeck = async (deckId: number, deckData: any) => {
    try {
      const updatedDeck = await deckService.updateDeck(deckId, deckData);
      setDecks(prev => prev.map(deck => 
        deck.id === deckId ? updatedDeck : deck
      ));
      toast.success('Deck updated successfully!');
      return updatedDeck;
    } catch (err: any) {
      const message = err.response?.data?.error || 'Failed to update deck';
      toast.error(message);
      throw err;
    }
  };

  const deleteDeck = async (deckId: number) => {
    try {
      await deckService.deleteDeck(deckId);
      setDecks(prev => prev.filter(deck => deck.id !== deckId));
      toast.success('Deck deleted successfully!');
    } catch (err: any) {
      const message = err.response?.data?.error || 'Failed to delete deck';
      toast.error(message);
      throw err;
    }
  };

  useEffect(() => {
    fetchDecks();
  }, []);

  return {
    decks,
    isLoading,
    error,
    fetchDecks,
    createDeck,
    updateDeck,
    deleteDeck,
  };
};
