import React, { useState } from 'react';
import { RotateCcw, Eye, EyeOff } from 'lucide-react';
import type { Flashcard } from '../types';

interface FlashcardComponentProps {
  flashcard: Flashcard;
  onAnswer: (quality: number) => void;
  isLoading?: boolean;
}

export const FlashcardComponent: React.FC<FlashcardComponentProps> = ({
  flashcard,
  onAnswer,
  isLoading = false,
}) => {
  const [showAnswer, setShowAnswer] = useState(false);

  const handleToggleAnswer = () => {
    setShowAnswer(!showAnswer);
  };

  const handleAnswer = (quality: number) => {
    onAnswer(quality);
    setShowAnswer(false); // Reset for next card
  };

  const qualityButtons = [
    { quality: 0, label: 'Again', color: 'bg-red-500 hover:bg-red-600', description: 'Complete blackout' },
    { quality: 1, label: 'Hard', color: 'bg-orange-500 hover:bg-orange-600', description: 'Incorrect, but remembered' },
    { quality: 2, label: 'Good', color: 'bg-yellow-500 hover:bg-yellow-600', description: 'Incorrect, seemed easy' },
    { quality: 3, label: 'Easy', color: 'bg-green-500 hover:bg-green-600', description: 'Correct with difficulty' },
    { quality: 4, label: 'Perfect', color: 'bg-blue-500 hover:bg-blue-600', description: 'Correct after hesitation' },
    { quality: 5, label: 'Excellent', color: 'bg-purple-500 hover:bg-purple-600', description: 'Perfect response' },
  ];

  return (
    <div className="max-w-2xl mx-auto">
      {/* Flashcard */}
      <div 
        className={`flashcard ${showAnswer ? 'flashcard-flipped' : ''} mb-6`}
        onClick={handleToggleAnswer}
      >
        <div className="text-center">
          {!showAnswer ? (
            <>
              <div className="text-lg font-medium text-gray-900 mb-4">
                {flashcard.front}
              </div>
              <div className="flex items-center justify-center text-gray-500">
                <Eye className="h-5 w-5 mr-2" />
                <span>Click to reveal answer</span>
              </div>
            </>
          ) : (
            <>
              <div className="text-sm text-gray-600 mb-2">
                {flashcard.front}
              </div>
              <hr className="my-4 border-gray-300" />
              <div className="text-lg font-medium text-gray-900 mb-4">
                {flashcard.back}
              </div>
              <div className="flex items-center justify-center text-gray-500">
                <EyeOff className="h-5 w-5 mr-2" />
                <span>Click to hide answer</span>
              </div>
            </>
          )}
        </div>
      </div>

      {/* Toggle Button */}
      <div className="text-center mb-6">
        <button
          onClick={handleToggleAnswer}
          className="btn-secondary inline-flex items-center space-x-2"
          disabled={isLoading}
        >
          <RotateCcw className="h-4 w-4" />
          <span>{showAnswer ? 'Hide Answer' : 'Show Answer'}</span>
        </button>
      </div>

      {/* Quality Buttons */}
      {showAnswer && (
        <div className="space-y-4">
          <div className="text-center">
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              How well did you know this?
            </h3>
            <p className="text-sm text-gray-600">
              Rate your response to help optimize future reviews
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
            {qualityButtons.map((button) => (
              <button
                key={button.quality}
                onClick={() => handleAnswer(button.quality)}
                disabled={isLoading}
                className={`${button.color} text-white px-4 py-3 rounded-lg font-medium transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed hover:shadow-md`}
                title={button.description}
              >
                <div className="text-sm font-bold">{button.label}</div>
                <div className="text-xs opacity-90">{button.quality}</div>
              </button>
            ))}
          </div>

          <div className="text-center text-xs text-gray-500 mt-4">
            <p>0 = Didn't know • 3 = Knew with difficulty • 5 = Perfect recall</p>
          </div>
        </div>
      )}

      {/* Card Info */}
      <div className="mt-8 p-4 bg-gray-50 rounded-lg">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div>
            <div className="font-medium text-gray-900">Times Reviewed</div>
            <div className="text-gray-600">{flashcard.times_reviewed}</div>
          </div>
          <div>
            <div className="font-medium text-gray-900">Success Rate</div>
            <div className="text-gray-600">{flashcard.success_rate}%</div>
          </div>
          <div>
            <div className="font-medium text-gray-900">Ease Factor</div>
            <div className="text-gray-600">{flashcard.ease_factor.toFixed(1)}</div>
          </div>
          <div>
            <div className="font-medium text-gray-900">Next Review</div>
            <div className="text-gray-600">
              {flashcard.days_until_review > 0 
                ? `${flashcard.days_until_review} days`
                : flashcard.days_until_review === 0 
                ? 'Today'
                : `${Math.abs(flashcard.days_until_review)} days overdue`
              }
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
