"""
Flashcard management routes for CRUD operations and study functionality.
"""

from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from app import db
from app.models.flashcard import Flashcard
from app.models.deck import Deck
from app.models.study_session import StudySession

bp = Blueprint('flashcards', __name__)

@bp.route('', methods=['POST'])
@jwt_required()
def create_flashcard():
    """Create a new flashcard."""
    try:
        user_id = get_jwt_identity()
        data = request.get_json()
        
        if not data or not all(k in data for k in ('front', 'back', 'deck_id')):
            return jsonify({'error': 'Front, back, and deck_id are required'}), 400
        
        # Verify deck ownership
        deck = Deck.query.filter_by(id=data['deck_id'], user_id=user_id).first()
        if not deck:
            return jsonify({'error': 'Deck not found'}), 404
        
        # Create flashcard
        flashcard = Flashcard(
            front=data['front'].strip(),
            back=data['back'].strip(),
            deck_id=data['deck_id'],
            difficulty=data.get('difficulty', 'medium'),
            notes=data.get('notes')
        )
        
        db.session.add(flashcard)
        db.session.commit()
        
        return jsonify({
            'message': 'Flashcard created successfully',
            'flashcard': flashcard.to_dict()
        }), 201
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': 'Failed to create flashcard', 'details': str(e)}), 500

@bp.route('/<int:flashcard_id>', methods=['GET'])
@jwt_required()
def get_flashcard(flashcard_id):
    """Get a specific flashcard."""
    try:
        user_id = get_jwt_identity()
        flashcard = db.session.query(Flashcard).join(Deck).filter(
            Flashcard.id == flashcard_id,
            Deck.user_id == user_id
        ).first()
        
        if not flashcard:
            return jsonify({'error': 'Flashcard not found'}), 404
        
        return jsonify({
            'flashcard': flashcard.to_dict()
        }), 200
        
    except Exception as e:
        return jsonify({'error': 'Failed to get flashcard', 'details': str(e)}), 500

@bp.route('/<int:flashcard_id>', methods=['PUT'])
@jwt_required()
def update_flashcard(flashcard_id):
    """Update a flashcard."""
    try:
        user_id = get_jwt_identity()
        flashcard = db.session.query(Flashcard).join(Deck).filter(
            Flashcard.id == flashcard_id,
            Deck.user_id == user_id
        ).first()
        
        if not flashcard:
            return jsonify({'error': 'Flashcard not found'}), 404
        
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        # Update allowed fields
        if 'front' in data:
            flashcard.front = data['front'].strip()
        
        if 'back' in data:
            flashcard.back = data['back'].strip()
        
        if 'difficulty' in data:
            flashcard.difficulty = data['difficulty']
        
        if 'notes' in data:
            flashcard.notes = data['notes']
        
        db.session.commit()
        
        return jsonify({
            'message': 'Flashcard updated successfully',
            'flashcard': flashcard.to_dict()
        }), 200
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': 'Failed to update flashcard', 'details': str(e)}), 500

@bp.route('/<int:flashcard_id>', methods=['DELETE'])
@jwt_required()
def delete_flashcard(flashcard_id):
    """Delete a flashcard."""
    try:
        user_id = get_jwt_identity()
        flashcard = db.session.query(Flashcard).join(Deck).filter(
            Flashcard.id == flashcard_id,
            Deck.user_id == user_id
        ).first()
        
        if not flashcard:
            return jsonify({'error': 'Flashcard not found'}), 404
        
        db.session.delete(flashcard)
        db.session.commit()
        
        return jsonify({
            'message': 'Flashcard deleted successfully'
        }), 200
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': 'Failed to delete flashcard', 'details': str(e)}), 500

@bp.route('/<int:flashcard_id>/study', methods=['POST'])
@jwt_required()
def study_flashcard(flashcard_id):
    """Record study result for a flashcard (spaced repetition)."""
    try:
        user_id = get_jwt_identity()
        flashcard = db.session.query(Flashcard).join(Deck).filter(
            Flashcard.id == flashcard_id,
            Deck.user_id == user_id
        ).first()
        
        if not flashcard:
            return jsonify({'error': 'Flashcard not found'}), 404
        
        data = request.get_json()
        if not data or 'quality' not in data:
            return jsonify({'error': 'Quality rating (0-5) is required'}), 400
        
        quality = data['quality']
        if not isinstance(quality, int) or quality < 0 or quality > 5:
            return jsonify({'error': 'Quality must be an integer between 0 and 5'}), 400
        
        # Update spaced repetition
        flashcard.update_spaced_repetition(quality)
        
        # Update study session if provided
        session_id = data.get('session_id')
        if session_id:
            session = StudySession.query.filter_by(
                id=session_id,
                user_id=user_id
            ).first()
            if session and session.is_active:
                session.add_card_result(correct=(quality >= 3))
        
        return jsonify({
            'message': 'Study result recorded successfully',
            'flashcard': flashcard.to_dict()
        }), 200
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': 'Failed to record study result', 'details': str(e)}), 500

@bp.route('/bulk', methods=['POST'])
@jwt_required()
def create_bulk_flashcards():
    """Create multiple flashcards at once."""
    try:
        user_id = get_jwt_identity()
        data = request.get_json()
        
        if not data or 'deck_id' not in data or 'flashcards' not in data:
            return jsonify({'error': 'deck_id and flashcards array are required'}), 400
        
        # Verify deck ownership
        deck = Deck.query.filter_by(id=data['deck_id'], user_id=user_id).first()
        if not deck:
            return jsonify({'error': 'Deck not found'}), 404
        
        flashcards_data = data['flashcards']
        if not isinstance(flashcards_data, list):
            return jsonify({'error': 'flashcards must be an array'}), 400
        
        created_flashcards = []
        
        for card_data in flashcards_data:
            if not all(k in card_data for k in ('front', 'back')):
                continue  # Skip invalid cards
            
            flashcard = Flashcard(
                front=card_data['front'].strip(),
                back=card_data['back'].strip(),
                deck_id=data['deck_id'],
                difficulty=card_data.get('difficulty', 'medium'),
                notes=card_data.get('notes')
            )
            
            db.session.add(flashcard)
            created_flashcards.append(flashcard)
        
        db.session.commit()
        
        return jsonify({
            'message': f'Created {len(created_flashcards)} flashcards successfully',
            'flashcards': [card.to_dict() for card in created_flashcards]
        }), 201
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': 'Failed to create flashcards', 'details': str(e)}), 500
