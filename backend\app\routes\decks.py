"""
Deck management routes for creating, reading, updating, and deleting decks.
"""

from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from app import db
from app.models.deck import Deck
from app.models.user import User

bp = Blueprint('decks', __name__)

@bp.route('', methods=['GET'])
@jwt_required()
def get_decks():
    """Get all decks for the current user."""
    try:
        user_id = get_jwt_identity()
        decks = Deck.query.filter_by(user_id=user_id).order_by(Deck.updated_at.desc()).all()
        
        return jsonify({
            'decks': [deck.to_dict() for deck in decks]
        }), 200
        
    except Exception as e:
        return jsonify({'error': 'Failed to get decks', 'details': str(e)}), 500

@bp.route('/<int:deck_id>', methods=['GET'])
@jwt_required()
def get_deck(deck_id):
    """Get a specific deck with its flashcards."""
    try:
        user_id = get_jwt_identity()
        deck = Deck.query.filter_by(id=deck_id, user_id=user_id).first()
        
        if not deck:
            return jsonify({'error': 'Deck not found'}), 404
        
        return jsonify({
            'deck': deck.to_dict(include_flashcards=True)
        }), 200
        
    except Exception as e:
        return jsonify({'error': 'Failed to get deck', 'details': str(e)}), 500

@bp.route('', methods=['POST'])
@jwt_required()
def create_deck():
    """Create a new deck."""
    try:
        user_id = get_jwt_identity()
        data = request.get_json()
        
        if not data or 'name' not in data:
            return jsonify({'error': 'Deck name is required'}), 400
        
        name = data['name'].strip()
        if not name:
            return jsonify({'error': 'Deck name cannot be empty'}), 400
        
        # Check if deck name already exists for this user
        existing_deck = Deck.query.filter_by(user_id=user_id, name=name).first()
        if existing_deck:
            return jsonify({'error': 'Deck with this name already exists'}), 409
        
        # Create new deck
        deck = Deck(
            name=name,
            user_id=user_id,
            description=data.get('description'),
            tags=', '.join(data.get('tags', [])) if data.get('tags') else None,
            color=data.get('color', '#3B82F6')
        )
        
        db.session.add(deck)
        db.session.commit()
        
        return jsonify({
            'message': 'Deck created successfully',
            'deck': deck.to_dict()
        }), 201
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': 'Failed to create deck', 'details': str(e)}), 500

@bp.route('/<int:deck_id>', methods=['PUT'])
@jwt_required()
def update_deck(deck_id):
    """Update a deck."""
    try:
        user_id = get_jwt_identity()
        deck = Deck.query.filter_by(id=deck_id, user_id=user_id).first()
        
        if not deck:
            return jsonify({'error': 'Deck not found'}), 404
        
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        # Update allowed fields
        if 'name' in data:
            name = data['name'].strip()
            if not name:
                return jsonify({'error': 'Deck name cannot be empty'}), 400
            
            # Check if new name conflicts with existing deck
            existing_deck = Deck.query.filter_by(
                user_id=user_id, 
                name=name
            ).filter(Deck.id != deck_id).first()
            
            if existing_deck:
                return jsonify({'error': 'Deck with this name already exists'}), 409
            
            deck.name = name
        
        if 'description' in data:
            deck.description = data['description']
        
        if 'tags' in data:
            deck.set_tags(data['tags'])
        
        if 'color' in data:
            deck.color = data['color']
        
        if 'is_public' in data:
            deck.is_public = data['is_public']
        
        db.session.commit()
        
        return jsonify({
            'message': 'Deck updated successfully',
            'deck': deck.to_dict()
        }), 200
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': 'Failed to update deck', 'details': str(e)}), 500

@bp.route('/<int:deck_id>', methods=['DELETE'])
@jwt_required()
def delete_deck(deck_id):
    """Delete a deck and all its flashcards."""
    try:
        user_id = get_jwt_identity()
        deck = Deck.query.filter_by(id=deck_id, user_id=user_id).first()
        
        if not deck:
            return jsonify({'error': 'Deck not found'}), 404
        
        db.session.delete(deck)
        db.session.commit()
        
        return jsonify({
            'message': 'Deck deleted successfully'
        }), 200
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': 'Failed to delete deck', 'details': str(e)}), 500

@bp.route('/<int:deck_id>/study', methods=['GET'])
@jwt_required()
def get_study_cards(deck_id):
    """Get cards for study session (due cards + new cards)."""
    try:
        user_id = get_jwt_identity()
        deck = Deck.query.filter_by(id=deck_id, user_id=user_id).first()
        
        if not deck:
            return jsonify({'error': 'Deck not found'}), 404
        
        # Get due cards and new cards
        due_cards = deck.get_due_flashcards()
        new_cards = deck.get_new_flashcards()
        
        # Limit to reasonable study session size
        max_cards = request.args.get('max_cards', 20, type=int)
        study_cards = (due_cards + new_cards)[:max_cards]
        
        return jsonify({
            'deck': deck.to_dict(),
            'study_cards': [card.to_dict() for card in study_cards],
            'total_due': len(due_cards),
            'total_new': len(new_cards)
        }), 200
        
    except Exception as e:
        return jsonify({'error': 'Failed to get study cards', 'details': str(e)}), 500
