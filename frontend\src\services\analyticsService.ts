import api from './api';
import type { AnalyticsData } from '../types';

export const analyticsService = {
  async getDashboardAnalytics(): Promise<AnalyticsData> {
    const response = await api.get<AnalyticsData>('/analytics/dashboard');
    return response.data;
  },

  async getPerformanceAnalytics(days: number = 30): Promise<any> {
    const response = await api.get('/analytics/performance', {
      params: { days }
    });
    return response.data;
  },
};
