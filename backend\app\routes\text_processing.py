"""
Text processing routes for generating flashcards from various text sources.
"""

import re
import time
from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from app.models.deck import Deck

bp = Blueprint('text_processing', __name__)

@bp.route('/generate', methods=['POST'])
@jwt_required()
def generate_flashcards():
    """Generate flashcards from text input."""
    try:
        user_id = get_jwt_identity()
        data = request.get_json()
        
        if not data or 'text' not in data:
            return jsonify({'error': 'Text input is required'}), 400
        
        text = data['text'].strip()
        if not text:
            return jsonify({'error': 'Text cannot be empty'}), 400
        
        # Get generation options
        options = data.get('generation_options', {})
        max_cards = options.get('max_cards', 10)
        difficulty = options.get('difficulty', 'medium')
        card_type = options.get('card_type', 'mixed')
        
        start_time = time.time()
        
        # Generate flashcards using simple text processing
        generated_cards = process_text_to_flashcards(
            text, max_cards, difficulty, card_type
        )
        
        processing_time = round(time.time() - start_time, 2)
        
        # Create source summary
        source_summary = create_source_summary(text)
        
        return jsonify({
            'generated_cards': generated_cards,
            'source_summary': source_summary,
            'processing_time': processing_time
        }), 200
        
    except Exception as e:
        return jsonify({'error': 'Failed to generate flashcards', 'details': str(e)}), 500

@bp.route('/import-url', methods=['POST'])
@jwt_required()
def import_from_url():
    """Import and process text from a URL."""
    try:
        data = request.get_json()
        
        if not data or 'url' not in data:
            return jsonify({'error': 'URL is required'}), 400
        
        url = data['url'].strip()
        if not url:
            return jsonify({'error': 'URL cannot be empty'}), 400
        
        # For now, return a placeholder response
        # In a full implementation, you would use requests and BeautifulSoup
        # to scrape the webpage content
        
        return jsonify({
            'message': 'URL import feature coming soon',
            'url': url
        }), 501
        
    except Exception as e:
        return jsonify({'error': 'Failed to import from URL', 'details': str(e)}), 500

def process_text_to_flashcards(text, max_cards, difficulty, card_type):
    """
    Process text and generate flashcards using simple NLP techniques.
    This is a basic implementation - in production, you'd use more sophisticated NLP.
    """
    generated_cards = []
    
    # Split text into sentences
    sentences = re.split(r'[.!?]+', text)
    sentences = [s.strip() for s in sentences if s.strip() and len(s.strip()) > 10]
    
    # Generate different types of cards based on card_type
    if card_type in ['definition', 'mixed']:
        generated_cards.extend(generate_definition_cards(sentences, max_cards // 2))
    
    if card_type in ['qa', 'mixed']:
        generated_cards.extend(generate_qa_cards(sentences, max_cards // 2))
    
    # Limit to max_cards
    generated_cards = generated_cards[:max_cards]
    
    # Assign difficulty and confidence scores
    for card in generated_cards:
        card['difficulty'] = difficulty
        card['confidence'] = calculate_confidence_score(card['front'], card['back'])
    
    return generated_cards

def generate_definition_cards(sentences, max_cards):
    """Generate definition-style flashcards."""
    cards = []
    
    # Look for sentences that might contain definitions
    definition_patterns = [
        r'(.+?)\s+is\s+(.+)',
        r'(.+?)\s+are\s+(.+)',
        r'(.+?)\s+means\s+(.+)',
        r'(.+?)\s+refers to\s+(.+)',
        r'(.+?):\s+(.+)',
    ]
    
    for sentence in sentences:
        if len(cards) >= max_cards:
            break
            
        for pattern in definition_patterns:
            match = re.search(pattern, sentence, re.IGNORECASE)
            if match:
                term = match.group(1).strip()
                definition = match.group(2).strip()
                
                # Clean up the term and definition
                term = re.sub(r'^(the|a|an)\s+', '', term, flags=re.IGNORECASE)
                
                if len(term) > 3 and len(definition) > 10:
                    cards.append({
                        'front': f"What is {term}?",
                        'back': definition.capitalize()
                    })
                    break
    
    return cards

def generate_qa_cards(sentences, max_cards):
    """Generate question-answer style flashcards."""
    cards = []
    
    # Generate questions from key sentences
    question_starters = [
        "What", "How", "Why", "When", "Where", "Who"
    ]
    
    for sentence in sentences:
        if len(cards) >= max_cards:
            break
        
        # Skip very short or very long sentences
        if len(sentence.split()) < 5 or len(sentence.split()) > 30:
            continue
        
        # Look for sentences with important keywords
        important_keywords = [
            'important', 'significant', 'key', 'main', 'primary',
            'because', 'therefore', 'however', 'although', 'since'
        ]
        
        if any(keyword in sentence.lower() for keyword in important_keywords):
            # Create a simple question by replacing key information
            question = create_question_from_sentence(sentence)
            if question:
                cards.append({
                    'front': question,
                    'back': sentence.strip()
                })
    
    return cards

def create_question_from_sentence(sentence):
    """Create a question from a sentence by identifying key information."""
    # Simple approach: look for numbers, dates, names, or key concepts
    
    # Look for years/dates
    year_match = re.search(r'\b(19|20)\d{2}\b', sentence)
    if year_match:
        year = year_match.group()
        question = sentence.replace(year, "____")
        return f"Fill in the blank: {question}"
    
    # Look for numbers
    number_match = re.search(r'\b\d+\b', sentence)
    if number_match:
        number = number_match.group()
        question = sentence.replace(number, "____")
        return f"Fill in the blank: {question}"
    
    # Look for proper nouns (capitalized words)
    proper_nouns = re.findall(r'\b[A-Z][a-z]+\b', sentence)
    if proper_nouns and len(proper_nouns[0]) > 3:
        name = proper_nouns[0]
        question = sentence.replace(name, "____")
        return f"Fill in the blank: {question}"
    
    # Default: create a general question
    return f"What does this statement describe: {sentence[:50]}...?"

def calculate_confidence_score(front, back):
    """Calculate a confidence score for the generated flashcard."""
    # Simple scoring based on length and complexity
    front_score = min(len(front.split()) / 10, 1.0)
    back_score = min(len(back.split()) / 20, 1.0)
    
    # Bonus for question words
    question_words = ['what', 'how', 'why', 'when', 'where', 'who']
    has_question = any(word in front.lower() for word in question_words)
    question_bonus = 0.2 if has_question else 0
    
    confidence = (front_score + back_score) / 2 + question_bonus
    return round(min(confidence, 1.0), 2)

def create_source_summary(text):
    """Create a summary of the source text."""
    word_count = len(text.split())
    sentence_count = len(re.split(r'[.!?]+', text))
    
    # Extract first sentence as a preview
    first_sentence = re.split(r'[.!?]+', text)[0].strip()
    if len(first_sentence) > 100:
        first_sentence = first_sentence[:100] + "..."
    
    return {
        'word_count': word_count,
        'sentence_count': sentence_count,
        'preview': first_sentence,
        'length_category': 'short' if word_count < 100 else 'medium' if word_count < 500 else 'long'
    }
