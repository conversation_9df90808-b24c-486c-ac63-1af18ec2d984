import React, { useState, useEffect } from 'react';
import { analyticsService } from '../services/analyticsService';
import { useDecks } from '../hooks/useDecks';
import {
  TrendingUp,
  Clock,
  Target,
  BookOpen,
  Zap,
  Calendar,
  Loader2
} from 'lucide-react';
import type { AnalyticsData } from '../types';

export const AnalyticsPage: React.FC = () => {
  const { decks } = useDecks();
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadAnalytics();
  }, []);

  const loadAnalytics = async () => {
    try {
      setIsLoading(true);
      const data = await analyticsService.getDashboardAnalytics();
      setAnalytics(data);
    } catch (error) {
      console.error('Failed to load analytics:', error);
      // Use fallback data if API fails
      setAnalytics({
        total_decks: decks.length,
        total_flashcards: decks.reduce((sum, deck) => sum + deck.flashcard_count, 0),
        cards_due_today: decks.reduce((sum, deck) => sum + deck.due_count, 0),
        study_streak: 0,
        total_study_time: 0,
        average_accuracy: 0,
        recent_sessions: [],
        performance_chart: { labels: [], datasets: [] },
        deck_progress: [],
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="max-w-7xl mx-auto">
        <div className="flex items-center justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-primary-600" />
          <span className="ml-2 text-gray-600">Loading analytics...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">Analytics Dashboard</h1>
        <p className="text-gray-600 mt-2">
          Track your learning progress and performance over time
        </p>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className="card">
          <div className="flex items-center">
            <div className="p-2 bg-primary-100 rounded-lg">
              <BookOpen className="h-6 w-6 text-primary-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Decks</p>
              <p className="text-2xl font-bold text-gray-900">{analytics?.total_decks || 0}</p>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="flex items-center">
            <div className="p-2 bg-success-100 rounded-lg">
              <Target className="h-6 w-6 text-success-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Cards</p>
              <p className="text-2xl font-bold text-gray-900">{analytics?.total_flashcards || 0}</p>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="flex items-center">
            <div className="p-2 bg-warning-100 rounded-lg">
              <Clock className="h-6 w-6 text-warning-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Cards Due</p>
              <p className="text-2xl font-bold text-gray-900">{analytics?.cards_due_today || 0}</p>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="flex items-center">
            <div className="p-2 bg-error-100 rounded-lg">
              <Zap className="h-6 w-6 text-error-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Study Streak</p>
              <p className="text-2xl font-bold text-gray-900">{analytics?.study_streak || 0} days</p>
            </div>
          </div>
        </div>
      </div>

      {/* Performance Metrics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <div className="card">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Study Time</h2>
          <div className="flex items-center">
            <div className="p-3 bg-purple-100 rounded-lg">
              <Clock className="h-8 w-8 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-3xl font-bold text-gray-900">
                {Math.round((analytics?.total_study_time || 0) / 60)}m
              </p>
              <p className="text-sm text-gray-600">Total study time (last 30 days)</p>
            </div>
          </div>
        </div>

        <div className="card">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Accuracy</h2>
          <div className="flex items-center">
            <div className="p-3 bg-indigo-100 rounded-lg">
              <TrendingUp className="h-8 w-8 text-indigo-600" />
            </div>
            <div className="ml-4">
              <p className="text-3xl font-bold text-gray-900">
                {analytics?.average_accuracy || 0}%
              </p>
              <p className="text-sm text-gray-600">Average accuracy (last 30 days)</p>
            </div>
          </div>
        </div>
      </div>

      {/* Recent Sessions */}
      <div className="card mb-8">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Recent Study Sessions</h2>
        {analytics?.recent_sessions && analytics.recent_sessions.length > 0 ? (
          <div className="space-y-3">
            {analytics.recent_sessions.map((session, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center">
                  <Calendar className="h-5 w-5 text-gray-400 mr-3" />
                  <div>
                    <p className="font-medium text-gray-900">
                      {session.session_type.charAt(0).toUpperCase() + session.session_type.slice(1)} Session
                    </p>
                    <p className="text-sm text-gray-600">
                      {new Date(session.started_at).toLocaleDateString()}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-medium text-gray-900">
                    {session.cards_studied} cards
                  </p>
                  <p className="text-sm text-gray-600">
                    {session.accuracy_percentage}% accuracy
                  </p>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8">
            <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600">No study sessions yet. Start studying to see your progress!</p>
          </div>
        )}
      </div>

      {/* Deck Progress */}
      <div className="card">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Deck Progress</h2>
        {decks.length > 0 ? (
          <div className="space-y-4">
            {decks.map((deck) => (
              <div key={deck.id} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center">
                    <div
                      className="w-4 h-4 rounded-full mr-3"
                      style={{ backgroundColor: deck.color }}
                    ></div>
                    <h3 className="font-medium text-gray-900">{deck.name}</h3>
                  </div>
                  <span className="text-sm text-gray-600">
                    {deck.flashcard_count} cards
                  </span>
                </div>

                <div className="flex justify-between text-sm text-gray-600 mb-2">
                  <span>Progress</span>
                  <span>
                    {deck.due_count > 0 ? `${deck.due_count} due` : 'Up to date'}
                  </span>
                </div>

                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-primary-600 h-2 rounded-full"
                    style={{
                      width: `${deck.flashcard_count > 0
                        ? Math.max(10, ((deck.flashcard_count - deck.due_count) / deck.flashcard_count) * 100)
                        : 0}%`
                    }}
                  ></div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8">
            <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600">No decks yet. Create your first deck to start tracking progress!</p>
          </div>
        )}
      </div>
    </div>
  );
};
