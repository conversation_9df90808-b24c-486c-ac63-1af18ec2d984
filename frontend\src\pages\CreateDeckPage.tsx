import React, { useState } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { ArrowLeft, Plus, Loader2 } from 'lucide-react';
import { useDecks } from '../hooks/useDecks';
import toast from 'react-hot-toast';

export const CreateDeckPage: React.FC = () => {
  const navigate = useNavigate();
  const { createDeck } = useDecks();
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    tags: '',
    color: '#3B82F6',
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name.trim()) {
      toast.error('Deck name is required');
      return;
    }

    setIsLoading(true);
    try {
      const deckData = {
        name: formData.name.trim(),
        description: formData.description.trim() || undefined,
        tags: formData.tags.trim() ? formData.tags.split(',').map(tag => tag.trim()) : undefined,
        color: formData.color,
      };

      await createDeck(deckData);
      navigate('/decks');
    } catch (error) {
      // Error is handled in the hook
    } finally {
      setIsLoading(false);
    }
  };

  const colorOptions = [
    { value: '#3B82F6', name: 'Blue' },
    { value: '#10B981', name: 'Green' },
    { value: '#8B5CF6', name: 'Purple' },
    { value: '#F59E0B', name: 'Yellow' },
    { value: '#EF4444', name: 'Red' },
    { value: '#6B7280', name: 'Gray' },
  ];

  return (
    <div className="max-w-2xl mx-auto">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <Link
          to="/decks"
          className="btn-secondary inline-flex items-center space-x-2"
        >
          <ArrowLeft className="h-4 w-4" />
          <span>Back to Decks</span>
        </Link>

        <h1 className="text-2xl font-bold text-gray-900">Create New Deck</h1>
        <div></div> {/* Spacer for centering */}
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit} className="card">
        <div className="space-y-6">
          {/* Deck Name */}
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
              Deck Name *
            </label>
            <input
              type="text"
              id="name"
              name="name"
              required
              className="input"
              placeholder="Enter deck name"
              value={formData.name}
              onChange={handleChange}
              disabled={isLoading}
            />
          </div>

          {/* Description */}
          <div>
            <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
              Description
            </label>
            <textarea
              id="description"
              name="description"
              rows={3}
              className="input resize-none"
              placeholder="Optional description for your deck"
              value={formData.description}
              onChange={handleChange}
              disabled={isLoading}
            />
          </div>

          {/* Tags */}
          <div>
            <label htmlFor="tags" className="block text-sm font-medium text-gray-700 mb-2">
              Tags
            </label>
            <input
              type="text"
              id="tags"
              name="tags"
              className="input"
              placeholder="Enter tags separated by commas (e.g., Spanish, Language, Beginner)"
              value={formData.tags}
              onChange={handleChange}
              disabled={isLoading}
            />
            <p className="text-xs text-gray-500 mt-1">
              Tags help organize and filter your decks
            </p>
          </div>

          {/* Color */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Deck Color
            </label>
            <div className="flex space-x-3">
              {colorOptions.map((color) => (
                <button
                  key={color.value}
                  type="button"
                  className={`w-8 h-8 rounded-full border-2 transition-all ${
                    formData.color === color.value
                      ? 'border-gray-900 scale-110'
                      : 'border-gray-300 hover:border-gray-400'
                  }`}
                  style={{ backgroundColor: color.value }}
                  onClick={() => setFormData({ ...formData, color: color.value })}
                  disabled={isLoading}
                  title={color.name}
                />
              ))}
            </div>
          </div>

          {/* Submit Button */}
          <div className="flex justify-end space-x-3 pt-4">
            <Link
              to="/decks"
              className="btn-secondary"
            >
              Cancel
            </Link>
            <button
              type="submit"
              disabled={isLoading}
              className="btn-primary inline-flex items-center space-x-2"
            >
              {isLoading ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span>Creating...</span>
                </>
              ) : (
                <>
                  <Plus className="h-4 w-4" />
                  <span>Create Deck</span>
                </>
              )}
            </button>
          </div>
        </div>
      </form>

      {/* Next Steps */}
      <div className="mt-8 p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <h3 className="text-sm font-medium text-blue-900 mb-2">
          What's next?
        </h3>
        <p className="text-sm text-blue-700">
          After creating your deck, you can add flashcards manually or generate them from text content.
        </p>
      </div>
    </div>
  );
};
