// User types
export interface User {
  id: number;
  username: string;
  email: string;
  created_at: string;
  last_login?: string;
  is_active: boolean;
}

export interface AuthResponse {
  message: string;
  access_token: string;
  user: User;
}

// Deck types
export interface Deck {
  id: number;
  name: string;
  description?: string;
  user_id: number;
  created_at: string;
  updated_at: string;
  is_public: boolean;
  tags: string[];
  color: string;
  flashcard_count: number;
  due_count: number;
  new_count: number;
  flashcards?: Flashcard[];
}

export interface CreateDeckRequest {
  name: string;
  description?: string;
  tags?: string[];
  color?: string;
}

// Flashcard types
export interface Flashcard {
  id: number;
  front: string;
  back: string;
  deck_id: number;
  created_at: string;
  updated_at: string;
  ease_factor: number;
  interval: number;
  repetitions: number;
  next_review: string;
  times_reviewed: number;
  times_correct: number;
  last_reviewed?: string;
  difficulty: 'easy' | 'medium' | 'hard';
  notes?: string;
  is_due: boolean;
  is_new: boolean;
  success_rate: number;
  days_until_review: number;
}

export interface CreateFlashcardRequest {
  front: string;
  back: string;
  difficulty?: 'easy' | 'medium' | 'hard';
  notes?: string;
}

export interface StudyResponse {
  quality: number; // 0-5 rating
}

// Study session types
export interface StudySession {
  id: number;
  user_id: number;
  deck_id?: number;
  started_at: string;
  ended_at?: string;
  cards_studied: number;
  cards_correct: number;
  total_time_seconds: number;
  duration_minutes: number;
  accuracy_percentage: number;
  session_type: 'review' | 'new' | 'mixed' | 'quiz';
  is_active: boolean;
}

// Analytics types
export interface AnalyticsData {
  total_decks: number;
  total_flashcards: number;
  cards_due_today: number;
  study_streak: number;
  total_study_time: number;
  average_accuracy: number;
  recent_sessions: StudySession[];
  performance_chart: ChartData;
  deck_progress: DeckProgress[];
}

export interface ChartData {
  labels: string[];
  datasets: {
    label: string;
    data: number[];
    backgroundColor?: string;
    borderColor?: string;
    borderWidth?: number;
  }[];
}

export interface DeckProgress {
  deck_name: string;
  total_cards: number;
  mastered_cards: number;
  learning_cards: number;
  new_cards: number;
  progress_percentage: number;
}

// Text processing types
export interface TextProcessingRequest {
  text: string;
  source_type: 'text' | 'file' | 'url';
  generation_options: {
    max_cards: number;
    difficulty: 'easy' | 'medium' | 'hard';
    card_type: 'definition' | 'qa' | 'mixed';
  };
}

export interface GeneratedFlashcard {
  front: string;
  back: string;
  confidence: number;
  difficulty: 'easy' | 'medium' | 'hard';
}

export interface TextProcessingResponse {
  generated_cards: GeneratedFlashcard[];
  source_summary: string;
  processing_time: number;
}

// Import/Export types
export interface ExportOptions {
  format: 'json' | 'csv' | 'anki';
  include_progress: boolean;
  deck_ids?: number[];
}

export interface ImportOptions {
  format: 'json' | 'csv' | 'anki';
  merge_duplicates: boolean;
  target_deck_id?: number;
}

// API response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  per_page: number;
  pages: number;
}

// Form types
export interface LoginForm {
  username: string;
  password: string;
}

export interface RegisterForm {
  username: string;
  email: string;
  password: string;
  confirmPassword: string;
}

// Component prop types
export interface FlashcardProps {
  flashcard: Flashcard;
  onAnswer: (quality: number) => void;
  showAnswer: boolean;
  onToggleAnswer: () => void;
}

export interface DeckCardProps {
  deck: Deck;
  onEdit?: (deck: Deck) => void;
  onDelete?: (deckId: number) => void;
  onStudy?: (deckId: number) => void;
}

// Hook types
export interface UseAuthReturn {
  user: User | null;
  token: string | null;
  login: (username: string, password: string) => Promise<void>;
  register: (username: string, email: string, password: string) => Promise<void>;
  logout: () => void;
  isLoading: boolean;
  isAuthenticated: boolean;
}
