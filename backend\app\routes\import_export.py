"""
Import/Export routes for flashcard data in various formats.
"""

import json
import csv
import io
from flask import Blueprint, request, jsonify, make_response
from flask_jwt_extended import jwt_required, get_jwt_identity
from app import db
from app.models.user import User
from app.models.deck import Deck
from app.models.flashcard import Flashcard

bp = Blueprint('import_export', __name__)

@bp.route('/export/json', methods=['POST'])
@jwt_required()
def export_json():
    """Export decks and flashcards to JSON format."""
    try:
        user_id = get_jwt_identity()
        data = request.get_json() or {}
        
        # Get deck IDs to export (all if not specified)
        deck_ids = data.get('deck_ids')
        include_progress = data.get('include_progress', True)
        
        # Query decks
        query = Deck.query.filter_by(user_id=user_id)
        if deck_ids:
            query = query.filter(Deck.id.in_(deck_ids))
        
        decks = query.all()
        
        # Build export data
        export_data = {
            'format': 'flashgenius_json_v1',
            'exported_at': db.func.now(),
            'decks': []
        }
        
        for deck in decks:
            deck_data = {
                'name': deck.name,
                'description': deck.description,
                'tags': deck.tag_list,
                'color': deck.color,
                'flashcards': []
            }
            
            for flashcard in deck.flashcards:
                card_data = {
                    'front': flashcard.front,
                    'back': flashcard.back,
                    'difficulty': flashcard.difficulty,
                    'notes': flashcard.notes
                }
                
                if include_progress:
                    card_data.update({
                        'times_reviewed': flashcard.times_reviewed,
                        'times_correct': flashcard.times_correct,
                        'ease_factor': flashcard.ease_factor,
                        'interval': flashcard.interval,
                        'repetitions': flashcard.repetitions
                    })
                
                deck_data['flashcards'].append(card_data)
            
            export_data['decks'].append(deck_data)
        
        # Create response
        response = make_response(json.dumps(export_data, indent=2))
        response.headers['Content-Type'] = 'application/json'
        response.headers['Content-Disposition'] = 'attachment; filename=flashcards_export.json'
        
        return response
        
    except Exception as e:
        return jsonify({'error': 'Export failed', 'details': str(e)}), 500

@bp.route('/export/csv', methods=['POST'])
@jwt_required()
def export_csv():
    """Export flashcards to CSV format."""
    try:
        user_id = get_jwt_identity()
        data = request.get_json() or {}
        
        # Get deck IDs to export
        deck_ids = data.get('deck_ids')
        
        # Query flashcards
        query = db.session.query(Flashcard).join(Deck).filter(Deck.user_id == user_id)
        if deck_ids:
            query = query.filter(Deck.id.in_(deck_ids))
        
        flashcards = query.all()
        
        # Create CSV
        output = io.StringIO()
        writer = csv.writer(output)
        
        # Write header
        writer.writerow([
            'Deck Name', 'Front', 'Back', 'Difficulty', 'Notes',
            'Times Reviewed', 'Times Correct', 'Success Rate'
        ])
        
        # Write data
        for flashcard in flashcards:
            writer.writerow([
                flashcard.deck.name,
                flashcard.front,
                flashcard.back,
                flashcard.difficulty,
                flashcard.notes or '',
                flashcard.times_reviewed,
                flashcard.times_correct,
                flashcard.success_rate
            ])
        
        # Create response
        response = make_response(output.getvalue())
        response.headers['Content-Type'] = 'text/csv'
        response.headers['Content-Disposition'] = 'attachment; filename=flashcards_export.csv'
        
        return response
        
    except Exception as e:
        return jsonify({'error': 'CSV export failed', 'details': str(e)}), 500

@bp.route('/import/json', methods=['POST'])
@jwt_required()
def import_json():
    """Import flashcards from JSON format."""
    try:
        user_id = get_jwt_identity()
        
        if 'file' not in request.files:
            return jsonify({'error': 'No file provided'}), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': 'No file selected'}), 400
        
        # Parse JSON
        try:
            import_data = json.loads(file.read().decode('utf-8'))
        except json.JSONDecodeError:
            return jsonify({'error': 'Invalid JSON file'}), 400
        
        # Validate format
        if import_data.get('format') != 'flashgenius_json_v1':
            return jsonify({'error': 'Unsupported file format'}), 400
        
        imported_decks = 0
        imported_cards = 0
        
        # Import decks
        for deck_data in import_data.get('decks', []):
            # Create deck
            deck = Deck(
                name=deck_data['name'],
                user_id=user_id,
                description=deck_data.get('description'),
                color=deck_data.get('color', '#3B82F6')
            )
            
            if deck_data.get('tags'):
                deck.set_tags(deck_data['tags'])
            
            db.session.add(deck)
            db.session.flush()  # Get deck ID
            
            # Import flashcards
            for card_data in deck_data.get('flashcards', []):
                flashcard = Flashcard(
                    front=card_data['front'],
                    back=card_data['back'],
                    deck_id=deck.id,
                    difficulty=card_data.get('difficulty', 'medium'),
                    notes=card_data.get('notes')
                )
                
                # Import progress if available
                if 'times_reviewed' in card_data:
                    flashcard.times_reviewed = card_data['times_reviewed']
                    flashcard.times_correct = card_data['times_correct']
                    flashcard.ease_factor = card_data.get('ease_factor', 2.5)
                    flashcard.interval = card_data.get('interval', 1)
                    flashcard.repetitions = card_data.get('repetitions', 0)
                
                db.session.add(flashcard)
                imported_cards += 1
            
            imported_decks += 1
        
        db.session.commit()
        
        return jsonify({
            'message': 'Import successful',
            'imported_decks': imported_decks,
            'imported_cards': imported_cards
        }), 200
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': 'Import failed', 'details': str(e)}), 500

@bp.route('/import/csv', methods=['POST'])
@jwt_required()
def import_csv():
    """Import flashcards from CSV format."""
    try:
        user_id = get_jwt_identity()
        
        if 'file' not in request.files:
            return jsonify({'error': 'No file provided'}), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': 'No file selected'}), 400
        
        # Get target deck ID or create new deck
        target_deck_id = request.form.get('target_deck_id')
        new_deck_name = request.form.get('new_deck_name', 'Imported Deck')
        
        if target_deck_id:
            deck = Deck.query.filter_by(id=target_deck_id, user_id=user_id).first()
            if not deck:
                return jsonify({'error': 'Target deck not found'}), 404
        else:
            # Create new deck
            deck = Deck(name=new_deck_name, user_id=user_id)
            db.session.add(deck)
            db.session.flush()
        
        # Parse CSV
        try:
            csv_content = file.read().decode('utf-8')
            csv_reader = csv.DictReader(io.StringIO(csv_content))
        except Exception:
            return jsonify({'error': 'Invalid CSV file'}), 400
        
        imported_cards = 0
        
        # Import flashcards
        for row in csv_reader:
            if 'Front' in row and 'Back' in row:
                flashcard = Flashcard(
                    front=row['Front'].strip(),
                    back=row['Back'].strip(),
                    deck_id=deck.id,
                    difficulty=row.get('Difficulty', 'medium'),
                    notes=row.get('Notes', '').strip() or None
                )
                
                db.session.add(flashcard)
                imported_cards += 1
        
        db.session.commit()
        
        return jsonify({
            'message': 'CSV import successful',
            'imported_cards': imported_cards,
            'deck_name': deck.name
        }), 200
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': 'CSV import failed', 'details': str(e)}), 500
