import api from './api';
import type { Deck, CreateDeckRequest } from '../types';

export const deckService = {
  async getDecks(): Promise<Deck[]> {
    const response = await api.get<{ decks: Deck[] }>('/decks');
    return response.data.decks;
  },

  async getDeck(deckId: number): Promise<Deck> {
    const response = await api.get<{ deck: Deck }>(`/decks/${deckId}`);
    return response.data.deck;
  },

  async createDeck(deckData: CreateDeckRequest): Promise<Deck> {
    const response = await api.post<{ deck: Deck }>('/decks', deckData);
    return response.data.deck;
  },

  async updateDeck(deckId: number, deckData: Partial<CreateDeckRequest>): Promise<Deck> {
    const response = await api.put<{ deck: Deck }>(`/decks/${deckId}`, deckData);
    return response.data.deck;
  },

  async deleteDeck(deckId: number): Promise<void> {
    await api.delete(`/decks/${deckId}`);
  },

  async getStudyCards(deckId: number, maxCards?: number): Promise<{
    deck: Deck;
    study_cards: any[];
    total_due: number;
    total_new: number;
  }> {
    const params = maxCards ? { max_cards: maxCards } : {};
    const response = await api.get(`/decks/${deckId}/study`, { params });
    return response.data;
  },
};
