"""
Analytics routes for progress tracking and performance metrics.
"""

from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from datetime import datetime, timedelta
from sqlalchemy import func, and_
from app import db
from app.models.user import User
from app.models.deck import Deck
from app.models.flashcard import Flashcard
from app.models.study_session import StudySession

bp = Blueprint('analytics', __name__)

@bp.route('/dashboard', methods=['GET'])
@jwt_required()
def get_dashboard_analytics():
    """Get comprehensive analytics for the dashboard."""
    try:
        user_id = get_jwt_identity()
        
        # Basic counts
        total_decks = Deck.query.filter_by(user_id=user_id).count()
        total_flashcards = db.session.query(Flashcard).join(Deck).filter(
            Deck.user_id == user_id
        ).count()
        
        # Cards due today
        today = datetime.utcnow()
        cards_due_today = db.session.query(Flashcard).join(Deck).filter(
            and_(
                Deck.user_id == user_id,
                Flashcard.next_review <= today
            )
        ).count()
        
        # Study streak (consecutive days with study sessions)
        study_streak = calculate_study_streak(user_id)
        
        # Total study time (last 30 days)
        thirty_days_ago = today - timedelta(days=30)
        total_study_time = db.session.query(
            func.sum(StudySession.total_time_seconds)
        ).filter(
            and_(
                StudySession.user_id == user_id,
                StudySession.started_at >= thirty_days_ago,
                StudySession.ended_at.isnot(None)
            )
        ).scalar() or 0
        
        # Average accuracy (last 30 days)
        recent_sessions = StudySession.query.filter(
            and_(
                StudySession.user_id == user_id,
                StudySession.started_at >= thirty_days_ago,
                StudySession.cards_studied > 0
            )
        ).all()
        
        if recent_sessions:
            total_cards = sum(session.cards_studied for session in recent_sessions)
            total_correct = sum(session.cards_correct for session in recent_sessions)
            average_accuracy = round((total_correct / total_cards) * 100, 1) if total_cards > 0 else 0
        else:
            average_accuracy = 0
        
        # Recent sessions (last 10)
        recent_sessions_data = StudySession.query.filter_by(user_id=user_id).filter(
            StudySession.ended_at.isnot(None)
        ).order_by(StudySession.ended_at.desc()).limit(10).all()
        
        # Performance chart data (last 7 days)
        performance_chart = get_performance_chart_data(user_id, days=7)
        
        # Deck progress
        deck_progress = get_deck_progress(user_id)
        
        return jsonify({
            'total_decks': total_decks,
            'total_flashcards': total_flashcards,
            'cards_due_today': cards_due_today,
            'study_streak': study_streak,
            'total_study_time': total_study_time,
            'average_accuracy': average_accuracy,
            'recent_sessions': [session.to_dict() for session in recent_sessions_data],
            'performance_chart': performance_chart,
            'deck_progress': deck_progress
        }), 200
        
    except Exception as e:
        return jsonify({'error': 'Failed to get analytics', 'details': str(e)}), 500

@bp.route('/performance', methods=['GET'])
@jwt_required()
def get_performance_analytics():
    """Get detailed performance analytics."""
    try:
        user_id = get_jwt_identity()
        days = request.args.get('days', 30, type=int)
        
        # Performance over time
        performance_chart = get_performance_chart_data(user_id, days)
        
        # Study time distribution
        study_time_chart = get_study_time_chart_data(user_id, days)
        
        # Difficulty breakdown
        difficulty_breakdown = get_difficulty_breakdown(user_id)
        
        return jsonify({
            'performance_chart': performance_chart,
            'study_time_chart': study_time_chart,
            'difficulty_breakdown': difficulty_breakdown
        }), 200
        
    except Exception as e:
        return jsonify({'error': 'Failed to get performance analytics', 'details': str(e)}), 500

def calculate_study_streak(user_id):
    """Calculate consecutive days with study sessions."""
    today = datetime.utcnow().date()
    streak = 0
    current_date = today
    
    while True:
        # Check if there's a study session on current_date
        session_exists = StudySession.query.filter(
            and_(
                StudySession.user_id == user_id,
                func.date(StudySession.started_at) == current_date,
                StudySession.ended_at.isnot(None)
            )
        ).first()
        
        if session_exists:
            streak += 1
            current_date -= timedelta(days=1)
        else:
            break
    
    return streak

def get_performance_chart_data(user_id, days=7):
    """Get performance chart data for the last N days."""
    end_date = datetime.utcnow().date()
    start_date = end_date - timedelta(days=days-1)
    
    # Get sessions grouped by date
    sessions_by_date = {}
    sessions = StudySession.query.filter(
        and_(
            StudySession.user_id == user_id,
            func.date(StudySession.started_at) >= start_date,
            StudySession.ended_at.isnot(None)
        )
    ).all()
    
    for session in sessions:
        date_key = session.started_at.date()
        if date_key not in sessions_by_date:
            sessions_by_date[date_key] = {'cards_studied': 0, 'cards_correct': 0}
        
        sessions_by_date[date_key]['cards_studied'] += session.cards_studied
        sessions_by_date[date_key]['cards_correct'] += session.cards_correct
    
    # Build chart data
    labels = []
    accuracy_data = []
    cards_data = []
    
    current_date = start_date
    while current_date <= end_date:
        labels.append(current_date.strftime('%m/%d'))
        
        if current_date in sessions_by_date:
            data = sessions_by_date[current_date]
            accuracy = (data['cards_correct'] / data['cards_studied'] * 100) if data['cards_studied'] > 0 else 0
            accuracy_data.append(round(accuracy, 1))
            cards_data.append(data['cards_studied'])
        else:
            accuracy_data.append(0)
            cards_data.append(0)
        
        current_date += timedelta(days=1)
    
    return {
        'labels': labels,
        'datasets': [
            {
                'label': 'Accuracy (%)',
                'data': accuracy_data,
                'borderColor': '#3B82F6',
                'backgroundColor': 'rgba(59, 130, 246, 0.1)',
                'borderWidth': 2
            },
            {
                'label': 'Cards Studied',
                'data': cards_data,
                'borderColor': '#10B981',
                'backgroundColor': 'rgba(16, 185, 129, 0.1)',
                'borderWidth': 2
            }
        ]
    }

def get_study_time_chart_data(user_id, days=30):
    """Get study time distribution chart data."""
    end_date = datetime.utcnow().date()
    start_date = end_date - timedelta(days=days-1)
    
    # Get total study time by date
    sessions = StudySession.query.filter(
        and_(
            StudySession.user_id == user_id,
            func.date(StudySession.started_at) >= start_date,
            StudySession.ended_at.isnot(None)
        )
    ).all()
    
    time_by_date = {}
    for session in sessions:
        date_key = session.started_at.date()
        if date_key not in time_by_date:
            time_by_date[date_key] = 0
        time_by_date[date_key] += session.duration_minutes
    
    # Build chart data (weekly aggregation for 30+ days)
    if days > 14:
        return get_weekly_study_time_data(time_by_date, start_date, end_date)
    else:
        return get_daily_study_time_data(time_by_date, start_date, end_date)

def get_weekly_study_time_data(time_by_date, start_date, end_date):
    """Get weekly aggregated study time data."""
    labels = []
    data = []
    
    current_date = start_date
    while current_date <= end_date:
        week_start = current_date - timedelta(days=current_date.weekday())
        week_end = week_start + timedelta(days=6)
        
        if week_end > end_date:
            week_end = end_date
        
        week_label = f"{week_start.strftime('%m/%d')} - {week_end.strftime('%m/%d')}"
        labels.append(week_label)
        
        week_total = 0
        check_date = week_start
        while check_date <= week_end:
            if check_date in time_by_date:
                week_total += time_by_date[check_date]
            check_date += timedelta(days=1)
        
        data.append(round(week_total, 1))
        current_date = week_end + timedelta(days=1)
    
    return {
        'labels': labels,
        'datasets': [{
            'label': 'Study Time (minutes)',
            'data': data,
            'backgroundColor': '#8B5CF6',
            'borderColor': '#7C3AED',
            'borderWidth': 1
        }]
    }

def get_daily_study_time_data(time_by_date, start_date, end_date):
    """Get daily study time data."""
    labels = []
    data = []
    
    current_date = start_date
    while current_date <= end_date:
        labels.append(current_date.strftime('%m/%d'))
        data.append(round(time_by_date.get(current_date, 0), 1))
        current_date += timedelta(days=1)
    
    return {
        'labels': labels,
        'datasets': [{
            'label': 'Study Time (minutes)',
            'data': data,
            'backgroundColor': '#8B5CF6',
            'borderColor': '#7C3AED',
            'borderWidth': 1
        }]
    }

def get_difficulty_breakdown(user_id):
    """Get breakdown of flashcards by difficulty level."""
    difficulty_counts = db.session.query(
        Flashcard.difficulty,
        func.count(Flashcard.id)
    ).join(Deck).filter(
        Deck.user_id == user_id
    ).group_by(Flashcard.difficulty).all()
    
    return {
        'labels': [item[0].title() for item in difficulty_counts],
        'datasets': [{
            'data': [item[1] for item in difficulty_counts],
            'backgroundColor': ['#10B981', '#F59E0B', '#EF4444'],
            'borderWidth': 0
        }]
    }

def get_deck_progress(user_id):
    """Get progress information for each deck."""
    decks = Deck.query.filter_by(user_id=user_id).all()
    deck_progress = []
    
    for deck in decks:
        total_cards = len(deck.flashcards)
        if total_cards == 0:
            continue
        
        # Count cards by learning status
        mastered_cards = len([card for card in deck.flashcards if card.repetitions >= 3 and card.ease_factor >= 2.5])
        learning_cards = len([card for card in deck.flashcards if card.times_reviewed > 0 and card.repetitions < 3])
        new_cards = len([card for card in deck.flashcards if card.times_reviewed == 0])
        
        progress_percentage = round((mastered_cards / total_cards) * 100, 1)
        
        deck_progress.append({
            'deck_name': deck.name,
            'total_cards': total_cards,
            'mastered_cards': mastered_cards,
            'learning_cards': learning_cards,
            'new_cards': new_cards,
            'progress_percentage': progress_percentage
        })
    
    return deck_progress
