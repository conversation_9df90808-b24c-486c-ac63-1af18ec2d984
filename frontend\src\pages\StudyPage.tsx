import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { ArrowLeft, CheckCircle, Loader2, BookOpen } from 'lucide-react';
import { FlashcardComponent } from '../components/FlashcardComponent';
import { studyService } from '../services/studyService';
import type { Flashcard } from '../types';
import toast from 'react-hot-toast';

export const StudyPage: React.FC = () => {
  const { deckId } = useParams<{ deckId: string }>();
  const navigate = useNavigate();

  const [studyCards, setStudyCards] = useState<Flashcard[]>([]);
  const [currentCardIndex, setCurrentCardIndex] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [isAnswering, setIsAnswering] = useState(false);
  const [deckInfo, setDeckInfo] = useState<any>(null);
  const [studyStats, setStudyStats] = useState({
    totalCards: 0,
    cardsStudied: 0,
    cardsCorrect: 0,
  });

  useEffect(() => {
    if (deckId) {
      loadStudyCards();
    }
  }, [deckId]);

  const loadStudyCards = async () => {
    try {
      setIsLoading(true);
      const data = await studyService.getStudyCards(parseInt(deckId!));
      setStudyCards(data.study_cards);
      setDeckInfo(data.deck);
      setStudyStats(prev => ({
        ...prev,
        totalCards: data.study_cards.length,
      }));
    } catch (error: any) {
      const message = error.response?.data?.error || 'Failed to load study cards';
      toast.error(message);
      navigate('/decks');
    } finally {
      setIsLoading(false);
    }
  };

  const handleAnswer = async (quality: number) => {
    if (!studyCards[currentCardIndex]) return;

    setIsAnswering(true);
    try {
      const flashcard = studyCards[currentCardIndex];
      await studyService.recordFlashcardResult(flashcard.id, quality);

      // Update stats
      setStudyStats(prev => ({
        ...prev,
        cardsStudied: prev.cardsStudied + 1,
        cardsCorrect: prev.cardsCorrect + (quality >= 3 ? 1 : 0),
      }));

      // Move to next card or finish
      if (currentCardIndex < studyCards.length - 1) {
        setCurrentCardIndex(prev => prev + 1);
        toast.success('Answer recorded!');
      } else {
        // Study session complete
        toast.success('Study session complete!');
        navigate('/decks');
      }
    } catch (error: any) {
      const message = error.response?.data?.error || 'Failed to record answer';
      toast.error(message);
    } finally {
      setIsAnswering(false);
    }
  };

  if (isLoading) {
    return (
      <div className="max-w-4xl mx-auto">
        <div className="flex items-center justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-primary-600" />
          <span className="ml-2 text-gray-600">Loading study cards...</span>
        </div>
      </div>
    );
  }

  if (!studyCards.length) {
    return (
      <div className="max-w-4xl mx-auto">
        <div className="text-center py-12">
          <BookOpen className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 mb-4">
            No Cards to Study
          </h1>
          <p className="text-gray-600 mb-6">
            There are no cards due for review in this deck right now.
          </p>
          <Link to="/decks" className="btn-primary">
            Back to Decks
          </Link>
        </div>
      </div>
    );
  }

  const currentCard = studyCards[currentCardIndex];
  const progress = ((currentCardIndex + 1) / studyCards.length) * 100;

  return (
    <div className="max-w-4xl mx-auto">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <Link
          to="/decks"
          className="btn-secondary inline-flex items-center space-x-2"
        >
          <ArrowLeft className="h-4 w-4" />
          <span>Back to Decks</span>
        </Link>

        <div className="text-center">
          <h1 className="text-xl font-bold text-gray-900">
            {deckInfo?.name || 'Study Session'}
          </h1>
          <p className="text-sm text-gray-600">
            Card {currentCardIndex + 1} of {studyCards.length}
          </p>
        </div>

        <div className="text-right">
          <div className="text-sm text-gray-600">
            Accuracy: {studyStats.cardsStudied > 0
              ? Math.round((studyStats.cardsCorrect / studyStats.cardsStudied) * 100)
              : 0}%
          </div>
        </div>
      </div>

      {/* Progress Bar */}
      <div className="mb-8">
        <div className="flex justify-between text-sm text-gray-600 mb-2">
          <span>Progress</span>
          <span>{Math.round(progress)}%</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className="bg-primary-600 h-2 rounded-full transition-all duration-300"
            style={{ width: `${progress}%` }}
          ></div>
        </div>
      </div>

      {/* Flashcard */}
      <FlashcardComponent
        flashcard={currentCard}
        onAnswer={handleAnswer}
        isLoading={isAnswering}
      />

      {/* Study Complete */}
      {currentCardIndex === studyCards.length - 1 && (
        <div className="mt-8 p-6 bg-green-50 border border-green-200 rounded-lg">
          <div className="flex items-center">
            <CheckCircle className="h-6 w-6 text-green-600 mr-3" />
            <div>
              <h3 className="text-lg font-medium text-green-900">
                Almost Done!
              </h3>
              <p className="text-green-700">
                This is your last card. Great job on completing this study session!
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
