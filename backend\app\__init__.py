"""
Flask application factory and configuration.
"""

import os
from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate
from flask_cors import CORS
from flask_jwt_extended import JWTManager

# Initialize extensions
db = SQLAlchemy()
migrate = Migrate()
jwt = JWTManager()

def create_app():
    """Create and configure the Flask application."""
    app = Flask(__name__)
    
    # Configuration
    app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', 'dev-secret-key')
    app.config['SQLALCHEMY_DATABASE_URI'] = os.getenv('DATABASE_URL', 'sqlite:///flashcards.db')
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    app.config['JWT_SECRET_KEY'] = os.getenv('JWT_SECRET_KEY', 'jwt-secret-key')
    app.config['JWT_ACCESS_TOKEN_EXPIRES'] = False  # Tokens don't expire for demo
    
    # Initialize extensions with app
    db.init_app(app)
    migrate.init_app(app, db)
    jwt.init_app(app)
    
    # Configure CORS
    cors_origins = os.getenv('CORS_ORIGINS', 'http://localhost:5173').split(',')
    CORS(app, origins=cors_origins)
    
    # Import models to ensure they're registered
    from app.models import user, flashcard, deck, study_session
    
    # Register blueprints
    from app.routes import auth, flashcards, decks, analytics, text_processing, import_export

    app.register_blueprint(auth.bp, url_prefix='/api/auth')
    app.register_blueprint(flashcards.bp, url_prefix='/api/flashcards')
    app.register_blueprint(decks.bp, url_prefix='/api/decks')
    app.register_blueprint(analytics.bp, url_prefix='/api/analytics')
    app.register_blueprint(text_processing.bp, url_prefix='/api/text')
    app.register_blueprint(import_export.bp, url_prefix='/api/import-export')
    
    # Create database tables
    with app.app_context():
        db.create_all()
    
    @app.route('/api/health')
    def health_check():
        """Health check endpoint."""
        return {'status': 'healthy', 'message': 'Quiz/Flashcard Generator API is running'}
    
    return app
