import api from './api';

export const importExportService = {
  async exportToJSON(deckIds?: number[], includeProgress: boolean = true): Promise<Blob> {
    const response = await api.post('/import-export/export/json', {
      deck_ids: deckIds,
      include_progress: includeProgress,
    }, {
      responseType: 'blob',
    });
    return response.data;
  },

  async exportToCSV(deckIds?: number[]): Promise<Blob> {
    const response = await api.post('/import-export/export/csv', {
      deck_ids: deckIds,
    }, {
      responseType: 'blob',
    });
    return response.data;
  },

  async importFromJSON(file: File): Promise<{
    message: string;
    imported_decks: number;
    imported_cards: number;
  }> {
    const formData = new FormData();
    formData.append('file', file);

    const response = await api.post('/import-export/import/json', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  async importFromCSV(file: File, targetDeckId?: number, newDeckName?: string): Promise<{
    message: string;
    imported_cards: number;
    deck_name: string;
  }> {
    const formData = new FormData();
    formData.append('file', file);
    
    if (targetDeckId) {
      formData.append('target_deck_id', targetDeckId.toString());
    }
    
    if (newDeckName) {
      formData.append('new_deck_name', newDeckName);
    }

    const response = await api.post('/import-export/import/csv', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  downloadBlob(blob: Blob, filename: string) {
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  },
};
