import api from './api';
import type { Flashcard, StudySession } from '../types';

export const studyService = {
  async startStudySession(deckId: number, sessionType: string = 'review'): Promise<StudySession> {
    const response = await api.post<{ session: StudySession }>('/study/sessions', {
      deck_id: deckId,
      session_type: sessionType,
    });
    return response.data.session;
  },

  async endStudySession(sessionId: number): Promise<StudySession> {
    const response = await api.put<{ session: StudySession }>(`/study/sessions/${sessionId}/end`);
    return response.data.session;
  },

  async recordFlashcardResult(
    flashcardId: number, 
    quality: number, 
    sessionId?: number
  ): Promise<Flashcard> {
    const response = await api.post<{ flashcard: Flashcard }>(`/flashcards/${flashcardId}/study`, {
      quality,
      session_id: sessionId,
    });
    return response.data.flashcard;
  },

  async getStudyCards(deckId: number, maxCards: number = 20): Promise<{
    deck: any;
    study_cards: Flashcard[];
    total_due: number;
    total_new: number;
  }> {
    const response = await api.get(`/decks/${deckId}/study`, {
      params: { max_cards: maxCards }
    });
    return response.data;
  },
};
