#!/usr/bin/env python3
"""
Simple test script to verify the API functionality.
"""

import requests
import json

BASE_URL = "http://localhost:5000/api"

def test_api():
    """Test the main API endpoints."""
    
    print("🧪 Testing Quiz/Flashcard Generator API")
    print("=" * 50)
    
    # Test health endpoint
    print("1. Testing health endpoint...")
    try:
        response = requests.get(f"{BASE_URL}/health")
        if response.status_code == 200:
            print("✅ Health check passed")
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return
    except Exception as e:
        print(f"❌ Health check failed: {e}")
        return
    
    # Test login
    print("\n2. Testing login...")
    try:
        login_data = {
            "username": "demo",
            "password": "demo123"
        }
        response = requests.post(f"{BASE_URL}/auth/login", json=login_data)
        if response.status_code == 200:
            data = response.json()
            token = data.get('access_token')
            user = data.get('user')
            print(f"✅ Login successful for user: {user['username']}")
            
            # Set up headers for authenticated requests
            headers = {"Authorization": f"Bearer {token}"}
            
        else:
            print(f"❌ Login failed: {response.status_code} - {response.text}")
            return
    except Exception as e:
        print(f"❌ Login failed: {e}")
        return
    
    # Test get decks
    print("\n3. Testing get decks...")
    try:
        response = requests.get(f"{BASE_URL}/decks", headers=headers)
        if response.status_code == 200:
            decks = response.json().get('decks', [])
            print(f"✅ Retrieved {len(decks)} decks")
            for deck in decks[:3]:  # Show first 3 decks
                print(f"   - {deck['name']}: {deck['flashcard_count']} cards, {deck['due_count']} due")
        else:
            print(f"❌ Get decks failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Get decks failed: {e}")
    
    # Test analytics
    print("\n4. Testing analytics...")
    try:
        response = requests.get(f"{BASE_URL}/analytics/dashboard", headers=headers)
        if response.status_code == 200:
            analytics = response.json()
            print(f"✅ Analytics retrieved:")
            print(f"   - Total decks: {analytics.get('total_decks', 0)}")
            print(f"   - Total flashcards: {analytics.get('total_flashcards', 0)}")
            print(f"   - Cards due today: {analytics.get('cards_due_today', 0)}")
            print(f"   - Study streak: {analytics.get('study_streak', 0)} days")
        else:
            print(f"❌ Analytics failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Analytics failed: {e}")
    
    # Test study cards (if we have decks)
    if decks:
        print("\n5. Testing study cards...")
        try:
            first_deck_id = decks[0]['id']
            response = requests.get(f"{BASE_URL}/decks/{first_deck_id}/study", headers=headers)
            if response.status_code == 200:
                study_data = response.json()
                study_cards = study_data.get('study_cards', [])
                print(f"✅ Retrieved {len(study_cards)} study cards for deck '{decks[0]['name']}'")
                if study_cards:
                    card = study_cards[0]
                    print(f"   - Sample card: '{card['front'][:50]}...' -> '{card['back'][:50]}...'")
            else:
                print(f"❌ Study cards failed: {response.status_code}")
        except Exception as e:
            print(f"❌ Study cards failed: {e}")
    
    # Test text processing
    print("\n6. Testing text processing...")
    try:
        text_data = {
            "text": "Python is a high-level programming language. It was created by Guido van Rossum in 1991. Python is known for its simple syntax and readability.",
            "generation_options": {
                "max_cards": 3,
                "difficulty": "medium",
                "card_type": "mixed"
            }
        }
        response = requests.post(f"{BASE_URL}/text/generate", json=text_data, headers=headers)
        if response.status_code == 200:
            result = response.json()
            generated_cards = result.get('generated_cards', [])
            print(f"✅ Generated {len(generated_cards)} flashcards from text")
            for i, card in enumerate(generated_cards[:2]):  # Show first 2 cards
                print(f"   - Card {i+1}: '{card['front']}' -> '{card['back']}'")
        else:
            print(f"❌ Text processing failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Text processing failed: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 API testing completed!")
    print("\nDemo credentials:")
    print("Username: demo")
    print("Password: demo123")
    print("\nFrontend URL: http://localhost:5173")
    print("Backend URL: http://localhost:5000")

if __name__ == "__main__":
    test_api()
